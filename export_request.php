<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

$requestId = (int)($_GET['id'] ?? 0);
$format = $_GET['format'] ?? 'excel';

if ($requestId <= 0) {
    header('Location: my_requests.php');
    exit;
}

// Buscar dados da requisição
$stmt = $pdo->prepare("
    SELECT r.*, u.username 
    FROM requests r 
    JOIN users u ON r.user_id = u.id 
    WHERE r.id = ?
");
$stmt->execute([$requestId]);
$request = $stmt->fetch();

if (!$request) {
    header('Location: my_requests.php');
    exit;
}

// Verificar se o usuário pode exportar esta requisição
if ($_SESSION['role'] != 'admin' && $request['user_id'] != $_SESSION['user_id']) {
    header('Location: my_requests.php');
    exit;
}

// Buscar itens da requisição
$stmt = $pdo->prepare("
    SELECT ri.*, i.name, i.description, i.unit 
    FROM request_items ri 
    JOIN items i ON ri.item_id = i.id 
    WHERE ri.request_id = ?
    ORDER BY i.name
");
$stmt->execute([$requestId]);
$requestItems = $stmt->fetchAll();

// Labels para status
$statusLabels = [
    'pending' => 'Pendente',
    'approved' => 'Aprovada',
    'rejected' => 'Rejeitada',
    'delivered' => 'Entregue'
];

// Preparar dados para exportação
$exportData = [
    'request' => $request,
    'items' => $requestItems,
    'status_label' => $statusLabels[$request['status']],
    'export_date' => date('d/m/Y H:i:s'),
    'total_items' => count($requestItems),
    'total_quantity' => array_sum(array_column($requestItems, 'quantity'))
];

// Redirecionar para o formato específico
switch ($format) {
    case 'excel':
        require_once 'exports/export_excel.php';
        break;
    case 'pdf':
        require_once 'exports/export_pdf.php';
        break;
    case 'docx':
        require_once 'exports/export_docx.php';
        break;
    default:
        header('Location: my_requests.php');
        exit;
}
?>
