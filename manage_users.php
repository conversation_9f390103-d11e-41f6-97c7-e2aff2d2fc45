<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

$message = '';
$error = '';

// Parâmetros de pesquisa e paginação
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$usersPerPage = 10;
$offset = ($page - 1) * $usersPerPage;

// Processar ações
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $username = trim($_POST['username'] ?? '');
                $password = trim($_POST['password'] ?? '');
                $role = $_POST['role'] ?? 'staff';
                
                if (empty($username) || empty($password)) {
                    $error = 'Username e senha são obrigatórios';
                } elseif (strlen($password) < 6) {
                    $error = 'A senha deve ter pelo menos 6 caracteres';
                } else {
                    try {
                        // Verificar se username já existe
                        $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
                        $checkStmt->execute([$username]);
                        
                        if ($checkStmt->fetchColumn() > 0) {
                            $error = 'Username já existe';
                        } else {
                            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
                            if ($stmt->execute([$username, $hashedPassword, $role])) {
                                $message = 'Usuário criado com sucesso!';
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao criar usuário: ' . $e->getMessage();
                    }
                }
                break;
                
            case 'update':
                $id = (int)($_POST['id'] ?? 0);
                $username = trim($_POST['username'] ?? '');
                $role = $_POST['role'] ?? 'staff';
                $newPassword = trim($_POST['new_password'] ?? '');
                
                if (empty($username) || $id <= 0) {
                    $error = 'Dados inválidos para atualização';
                } else {
                    try {
                        // Verificar se username já existe (exceto o próprio usuário)
                        $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
                        $checkStmt->execute([$username, $id]);
                        
                        if ($checkStmt->fetchColumn() > 0) {
                            $error = 'Username já existe';
                        } else {
                            if (!empty($newPassword)) {
                                if (strlen($newPassword) < 6) {
                                    $error = 'A nova senha deve ter pelo menos 6 caracteres';
                                } else {
                                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                                    $stmt = $pdo->prepare("UPDATE users SET username = ?, password = ?, role = ? WHERE id = ?");
                                    $stmt->execute([$username, $hashedPassword, $role, $id]);
                                }
                            } else {
                                $stmt = $pdo->prepare("UPDATE users SET username = ?, role = ? WHERE id = ?");
                                $stmt->execute([$username, $role, $id]);
                            }
                            
                            if (!$error) {
                                $message = 'Usuário atualizado com sucesso!';
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao atualizar usuário: ' . $e->getMessage();
                    }
                }
                break;
                
            case 'delete':
                $id = (int)($_POST['id'] ?? 0);
                if ($id > 0) {
                    try {
                        // Não permitir deletar o próprio usuário
                        if ($id == $_SESSION['user_id']) {
                            $error = 'Você não pode deletar sua própria conta';
                        } else {
                            // Verificar se o usuário tem requisições
                            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM requests WHERE user_id = ?");
                            $checkStmt->execute([$id]);
                            $requestCount = $checkStmt->fetchColumn();
                            
                            if ($requestCount > 0) {
                                $error = "Não é possível excluir este usuário pois ele possui $requestCount requisição(ões)";
                            } else {
                                $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                                if ($stmt->execute([$id])) {
                                    $message = 'Usuário excluído com sucesso!';
                                }
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao excluir usuário: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Construir query de busca
$whereClause = '';
$params = [];

if (!empty($search)) {
    $whereClause = "WHERE username LIKE ? OR role LIKE ?";
    $params = ["%$search%", "%$search%"];
}

// Contar total de usuários para paginação
$countSql = "SELECT COUNT(*) FROM users $whereClause";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalUsers = $countStmt->fetchColumn();
$totalPages = ceil($totalUsers / $usersPerPage);

// Buscar usuários com paginação
$sql = "SELECT * FROM users $whereClause ORDER BY username LIMIT $usersPerPage OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$users = $stmt->fetchAll();

// Buscar usuário para edição se solicitado
$editUser = null;
if (isset($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$editId]);
    $editUser = $stmt->fetch();
}

// Função para destacar termos de pesquisa
function highlightSearchUsers($text, $search) {
    if (empty($search) || empty($text)) {
        return htmlspecialchars($text);
    }
    
    $highlighted = htmlspecialchars($text);
    $searchTerms = explode(' ', $search);
    
    foreach ($searchTerms as $term) {
        if (strlen(trim($term)) > 2) {
            $highlighted = preg_replace(
                '/(' . preg_quote(trim($term), '/') . ')/i',
                '<mark>$1</mark>',
                $highlighted
            );
        }
    }
    
    return $highlighted;
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Gerenciar Usuários - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #6c757d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        .role-badge {
            font-size: 0.8em;
        }
        mark {
            background-color: #ffeb3b;
            padding: 1px 2px;
            border-radius: 2px;
            font-weight: bold;
        }
        .table-hover tbody tr:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h2>👥 Gerenciar Usuários</h2>
            </div>
            <div class="col-md-4 text-right">
                <div class="card bg-light">
                    <div class="card-body py-2">
                        <small><strong>Total:</strong> <?php echo $totalUsers; ?> usuário(s)</small>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        <?php endif; ?>
        
        <!-- Campo de Pesquisa -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔍 Pesquisar Usuários</h5>
            </div>
            <div class="card-body">
                <form method="get" class="mb-0">
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="Digite o username ou role..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary btn-block">
                                🔍 Pesquisar
                            </button>
                        </div>
                    </div>
                    <?php if (!empty($search)): ?>
                    <div class="mt-2">
                        <small class="text-muted">
                            Pesquisando por: "<strong><?php echo htmlspecialchars($search); ?></strong>" 
                            - <?php echo $totalUsers; ?> usuário(s) encontrado(s)
                        </small>
                        <a href="manage_users.php" class="btn btn-sm btn-outline-secondary ml-2">
                            ✖ Limpar Pesquisa
                        </a>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>

        <!-- Formulário para adicionar/editar usuário -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><?php echo $editUser ? '✏️ Editar Usuário' : '➕ Adicionar Novo Usuário'; ?></h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?php echo $editUser ? 'update' : 'add'; ?>">
                    <?php if ($editUser): ?>
                        <input type="hidden" name="id" value="<?php echo $editUser['id']; ?>">
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Username *</label>
                                <input type="text" name="username" class="form-control"
                                       value="<?php echo $editUser ? htmlspecialchars($editUser['username']) : ''; ?>"
                                       required maxlength="50">
                                <small class="text-muted">Único no sistema</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label><?php echo $editUser ? 'Nova Senha' : 'Senha *'; ?></label>
                                <input type="password" name="<?php echo $editUser ? 'new_password' : 'password'; ?>"
                                       class="form-control" <?php echo !$editUser ? 'required' : ''; ?>
                                       minlength="6" maxlength="255">
                                <small class="text-muted">
                                    <?php echo $editUser ? 'Deixe em branco para manter a atual' : 'Mínimo 6 caracteres'; ?>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Função *</label>
                                <select name="role" class="form-control" required>
                                    <option value="staff" <?php echo ($editUser && $editUser['role'] == 'staff') ? 'selected' : ''; ?>>
                                        👤 Funcionário
                                    </option>
                                    <option value="admin" <?php echo ($editUser && $editUser['role'] == 'admin') ? 'selected' : ''; ?>>
                                        👑 Administrador
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <?php echo $editUser ? '💾 Atualizar Usuário' : '➕ Criar Usuário'; ?>
                        </button>
                        <?php if ($editUser): ?>
                            <a href="manage_users.php" class="btn btn-secondary">❌ Cancelar</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>

        <!-- Informações de Paginação -->
        <?php if ($totalUsers > 0): ?>
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <small class="text-muted">
                    Mostrando <?php echo min($offset + 1, $totalUsers); ?> -
                    <?php echo min($offset + $usersPerPage, $totalUsers); ?> de
                    <?php echo $totalUsers; ?> usuários
                </small>
            </div>
            <div>
                <small class="text-muted">Página <?php echo $page; ?> de <?php echo $totalPages; ?></small>
            </div>
        </div>
        <?php endif; ?>

        <!-- Lista de usuários -->
        <div class="card">
            <div class="card-header">
                <h5>📋 Usuários Cadastrados</h5>
            </div>
            <div class="card-body">
                <?php if (empty($users)): ?>
                    <div class="text-center text-muted py-4">
                        <?php if (!empty($search)): ?>
                            🔍 Nenhum usuário encontrado para "<strong><?php echo htmlspecialchars($search); ?></strong>"
                            <br><small>Tente pesquisar com outros termos</small>
                        <?php else: ?>
                            👥 Nenhum usuário cadastrado no sistema
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th width="10%">Avatar</th>
                                    <th width="25%">Username</th>
                                    <th width="20%">Função</th>
                                    <th width="20%">Status</th>
                                    <th width="25%">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="user-avatar">
                                            <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <strong><?php echo highlightSearchUsers($user['username'], $search); ?></strong>
                                        <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                            <small class="text-primary">(Você)</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['role'] == 'admin'): ?>
                                            <span class="badge badge-danger role-badge">👑 Admin</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary role-badge">👤 Staff</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">✅ Ativo</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="view_user.php?id=<?php echo $user['id']; ?>"
                                               class="btn btn-info" title="Ver Detalhes">
                                                👁️
                                            </a>
                                            <a href="?edit=<?php echo $user['id']; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>"
                                               class="btn btn-warning" title="Editar">
                                                ✏️
                                            </a>
                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <form method="post" style="display: inline;"
                                                  onsubmit="return confirm('⚠️ Tem certeza que deseja excluir o usuário <?php echo htmlspecialchars($user['username']); ?>?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="btn btn-danger" title="Excluir">
                                                    🗑️
                                                </button>
                                            </form>
                                            <?php else: ?>
                                            <button class="btn btn-secondary" disabled title="Não é possível excluir sua própria conta">
                                                🔒
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Controles de Paginação -->
        <?php if ($totalPages > 1): ?>
        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Navegação de páginas">
                <ul class="pagination">
                    <!-- Primeira página -->
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                            ⏮ Primeira
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- Página anterior -->
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                            ⏪ Anterior
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- Páginas numeradas -->
                    <?php
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);

                    for ($i = $startPage; $i <= $endPage; $i++):
                    ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <!-- Próxima página -->
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                            Próxima ⏩
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- Última página -->
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                            Última ⏭
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Focar no campo de pesquisa se não houver termo
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }

            // Validação de formulário
            const userForm = document.querySelector('form[method="post"]');
            if (userForm) {
                userForm.addEventListener('submit', function(e) {
                    const username = document.querySelector('input[name="username"]').value.trim();
                    const password = document.querySelector('input[name="password"], input[name="new_password"]').value;
                    const isEdit = document.querySelector('input[name="action"]').value === 'update';

                    if (!username) {
                        alert('⚠️ Username é obrigatório!');
                        e.preventDefault();
                        return;
                    }

                    if (!isEdit && !password) {
                        alert('⚠️ Senha é obrigatória!');
                        e.preventDefault();
                        return;
                    }

                    if (password && password.length < 6) {
                        alert('⚠️ A senha deve ter pelo menos 6 caracteres!');
                        e.preventDefault();
                        return;
                    }
                });
            }
        });

        // Atalho de teclado para pesquisa (Ctrl+F)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                const searchInput = document.querySelector('input[name="search"]');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            }
        });
    </script>
</body>
</html>
