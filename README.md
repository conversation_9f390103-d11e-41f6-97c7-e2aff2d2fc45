# Sistema de Requisição de Material de Cozinha

Sistema web para gerenciamento de requisições de materiais de cozinha, desenvolvido em PHP com MySQL.

## Funcionalidades

### Para Funcionários:
- Login no sistema
- Criar novas requisições de material com **nomes personalizados**
- **Editar requisições pendentes** (nome e itens)
- Visualizar suas requisições **identificadas pelo nome** (não mais pelo ID)
- Acompanhar status das requisições
- **Exportar requisições** para Excel, PDF e DOCX

### Para Administradores:
- Todas as funcionalidades de funcionários
- Gerenciar itens do estoque
- Aprovar/rejeitar requisições
- Marcar requisições como entregues
- Visualizar todas as requisições do sistema
- **CRUD completo de usuários** (criar, editar, visualizar, excluir)
- Pesquisar e paginar usuários
- Visualizar estatísticas de usuários

## Estrutura do Projeto

```
os_cozinha/
├── config/
│   └── db_connect.php          # Configuração do banco de dados
├── includes/
│   └── navbar.php              # Barra de navegação
├── index.php                   # Página inicial
├── login.php                   # Página de login
├── logout.php                  # Script de logout
├── request_form.php            # Formulário de nova requisição
├── my_requests.php             # Minhas requisições
├── manage_requests.php         # Gerenciar requisições (admin)
├── manage_items.php            # Gerenciar itens (admin)
├── manage_users.php            # Gerenciar usuários (admin)
├── view_request.php            # Detalhes da requisição
├── view_user.php               # Detalhes do usuário (admin)
├── edit_request.php            # Editar requisição pendente
├── export_request.php          # Exportar requisições
├── exports/                    # Módulos de exportação
│   ├── export_excel.php        # Exportação para Excel/CSV
│   ├── export_pdf.php          # Exportação para PDF
│   └── export_docx.php         # Exportação para DOCX/RTF
├── update_db_for_titles.php    # Script para atualizar banco (títulos)
└── test_titles.php             # Teste de títulos de requisições
├── create_admin.php            # Criar usuário admin
├── populate_database.php       # Popular banco com dados de exemplo
├── add_kitchen_products.php    # Adicionar produtos complementares
├── add_rotisserie_products.php # Adicionar produtos de rotisseria
├── setup_products.php          # Menu principal de configuração
├── system_check.php            # Verificação do sistema
├── database.sql                # Script de criação do banco
└── README.md                   # Este arquivo
```

## 🆕 Nova Identificação por Nome

### **Mudança Importante: Requisições agora são identificadas pelo NOME, não pelo ID**

#### **Antes:**
- Requisições listadas como "Requisição #123"
- Identificação apenas por número
- Difícil de distinguir requisições

#### **Agora:**
- Requisições com **nomes personalizados**: "Festa Junina", "Estoque Semanal"
- **ID secundário** mostrado como informação adicional
- **Data e hora** incluídas na identificação
- **Fallback automático** para "Requisição #ID" se sem nome

#### **Exemplo de Exibição:**
```
🎯 Nome Principal: "Ingredientes para Pizza Especial"
📝 Detalhes: ID: #123 • 15/12/2024 14:30
```

#### **Benefícios:**
- ✅ **Identificação intuitiva** e significativa
- ✅ **Organização melhorada** das requisições
- ✅ **Busca mais fácil** por contexto
- ✅ **Compatibilidade** com requisições antigas

## Instalação

### 1. Pré-requisitos
- Servidor web (Apache/Nginx)
- PHP 7.4 ou superior
- MySQL 5.7 ou superior
- Extensões PHP: PDO, PDO_MySQL

### 2. Configuração do Banco de Dados
1. Crie o banco de dados executando o arquivo `database.sql`
2. Configure as credenciais em `config/db_connect.php`

### 3. Configuração Inicial
1. Execute `system_check.php` para verificar se tudo está funcionando
2. Execute `populate_database.php` para criar dados de exemplo (80+ produtos)
3. Opcionalmente, execute `add_kitchen_products.php` para produtos complementares
4. Para rotisseria, execute `add_rotisserie_products.php` para produtos especializados
5. Ou execute `create_admin.php` para criar apenas um usuário admin

## Credenciais Padrão

Após executar `populate_database.php`:

**Administrador:**
- Usuário: `admin`
- Senha: `admin123`

**Funcionário:**
- Usuário: `funcionario`
- Senha: `123456`

## Produtos Cadastrados

O sistema vem com mais de **210 produtos** organizados por categorias:

### Categorias Principais (populate_database.php):
- **Grãos e Cereais:** Arroz, feijão, lentilha, quinoa, aveia
- **Farinhas e Massas:** Farinha de trigo, macarrões, fubá, polvilho
- **Óleos e Gorduras:** Óleo de soja, azeite, margarina, manteiga
- **Temperos e Condimentos:** Sal, açúcar, vinagre, molho de soja
- **Vegetais e Legumes:** Cebola, alho, batata, tomate, cenoura
- **Frutas:** Banana, maçã, laranja, limão, abacaxi
- **Carnes e Proteínas:** Frango, carne bovina, peixe, linguiça
- **Laticínios e Ovos:** Leite, queijos, iogurte, ovos
- **Pães e Padaria:** Pão francês, biscoitos, torradas
- **Molhos e Conservas:** Molho de tomate, maionese, azeitonas
- **Especiarias e Ervas:** Orégano, manjericão, pimenta do reino
- **Bebidas:** Café, chás, sucos, água mineral
- **Produtos de Limpeza:** Detergente, esponjas, álcool

### Categorias Complementares (add_kitchen_products.php):
- **Produtos Congelados:** Batata frita, hambúrguer, polpas
- **Enlatados:** Sardinha, atum, palmito, cogumelos
- **Produtos Secos:** Castanhas, amendoim, uva passa
- **Panificação:** Fermentos, essências, chocolate em pó
- **Diet/Light:** Adoçantes, açúcar demerara
- **Orientais:** Shoyu, gergelim, wasabi, nori
- **Sobremesas:** Gelatinas, pudins, mousses
- **Funcionais:** Chia, linhaça, goji berry
- **Temperos Especiais:** Curry, açafrão, alecrim

### Categorias de Rotisseria (add_rotisserie_products.php):
- **🥩 Carnes para Assados:** Frango inteiro, pernil, costela, picanha, cordeiro
- **🧂 Temperos Especiais:** Temperos específicos, sal grosso, páprica defumada
- **🍯 Molhos e Glazes:** Barbecue, teriyaki, mel, molho inglês, chimichurri
- **🥔 Acompanhamentos:** Batatas especiais, milho, vegetais para grelhar
- **🍞 Pães Especiais:** Pão de alho, sírio, hambúrguer artesanal
- **🥗 Saladas Gourmet:** Mix de folhas, tomate cereja, palmito fresco
- **🧀 Queijos e Frios:** Coalho, provolone, presunto parma, salame
- **🍺 Bebidas Especiais:** Cerveja para cozinhar, cachaça, vinhos
- **🍍 Sobremesas:** Frutas para grelhar, canela, açúcar mascavo
- **🔥 Utensílios:** Papel alumínio, espetos, carvão, lenha seca

## Status das Requisições

- **Pendente**: Requisição criada, aguardando aprovação
- **Aprovada**: Requisição aprovada pelo administrador
- **Rejeitada**: Requisição rejeitada pelo administrador
- **Entregue**: Itens foram entregues ao solicitante

## Segurança

- Senhas são criptografadas com `password_hash()`
- Validação de sessão em todas as páginas
- Proteção contra SQL Injection usando prepared statements
- Escape de dados de saída com `htmlspecialchars()`
- Verificação de permissões por role (admin/staff)

## Problemas Corrigidos

1. ✅ Arquivo `logout.php` criado
2. ✅ Arquivo `manage_items.php` criado
3. ✅ Arquivo `view_request.php` completado
4. ✅ Navbar duplicado removido do `index.php`
5. ✅ Validação de array em `request_form.php` corrigida
6. ✅ Arquivos duplicados removidos
7. ✅ Sistema de verificação implementado

## Manutenção

- Execute `system_check.php` periodicamente para verificar a integridade do sistema
- Faça backup regular do banco de dados
- Monitore os logs de erro do servidor web

## Suporte

Para problemas ou dúvidas, verifique:
1. `system_check.php` - para diagnóstico automático
2. Logs de erro do PHP
3. Logs do servidor web
4. Configurações do banco de dados
