<?php
/**
 * Gerador de Códigos de Barras para Sistema de Requisição
 * Suporte para Code128 e EAN-13
 */

class BarcodeGenerator {
    
    /**
     * Gerar código interno para requisições
     */
    public static function generateRequestCode($id) {
        return 'REQ' . str_pad($id, 6, '0', STR_PAD_LEFT);
    }
    
    /**
     * Gerar código interno para itens
     */
    public static function generateItemCode($id) {
        return 'ITEM' . str_pad($id, 5, '0', STR_PAD_LEFT);
    }
    
    /**
     * Gerar código de barras numérico
     */
    public static function generateBarcode($prefix, $id) {
        // Converter prefix para números
        $prefixNum = '';
        switch(strtoupper($prefix)) {
            case 'REQ':
                $prefixNum = '789'; // Código para requisições
                break;
            case 'ITEM':
                $prefixNum = '456'; // Código para itens
                break;
            default:
                $prefixNum = '123';
        }
        
        // Montar código base (12 dígitos)
        $code = $prefixNum . str_pad($id, 8, '0', STR_PAD_LEFT) . '0';
        
        // Calcular dígito verificador EAN-13
        $checkDigit = self::calculateEAN13CheckDigit($code);
        
        return $code . $checkDigit;
    }
    
    /**
     * Calcular dígito verificador EAN-13
     */
    private static function calculateEAN13CheckDigit($code) {
        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $digit = (int)$code[$i];
            $sum += ($i % 2 == 0) ? $digit : $digit * 3;
        }
        return (10 - ($sum % 10)) % 10;
    }
    
    /**
     * Gerar código de barras visual (ASCII)
     */
    public static function generateBarcodeASCII($code) {
        $barcode = '';
        
        // Padrão simples de barras (representação ASCII)
        for ($i = 0; $i < strlen($code); $i++) {
            $digit = (int)$code[$i];
            
            // Padrão de barras baseado no dígito
            switch($digit) {
                case 0: $barcode .= '||  ||  '; break;
                case 1: $barcode .= '| || | |'; break;
                case 2: $barcode .= '| | || |'; break;
                case 3: $barcode .= '|| | | |'; break;
                case 4: $barcode .= '|  || ||'; break;
                case 5: $barcode .= '||  | ||'; break;
                case 6: $barcode .= '| ||  ||'; break;
                case 7: $barcode .= '| | | ||'; break;
                case 8: $barcode .= '|| | | |'; break;
                case 9: $barcode .= '|  | |||'; break;
            }
        }
        
        return $barcode;
    }
    
    /**
     * Gerar código de barras SVG
     */
    public static function generateBarcodeSVG($code, $width = 200, $height = 50) {
        $barWidth = $width / (strlen($code) * 4);
        $svg = '<svg width="' . $width . '" height="' . $height . '" xmlns="http://www.w3.org/2000/svg">';
        $svg .= '<rect width="' . $width . '" height="' . $height . '" fill="white"/>';
        
        $x = 0;
        for ($i = 0; $i < strlen($code); $i++) {
            $digit = (int)$code[$i];
            
            // Padrão de barras para cada dígito
            $pattern = self::getBarPattern($digit);
            
            for ($j = 0; $j < strlen($pattern); $j++) {
                if ($pattern[$j] == '1') {
                    $svg .= '<rect x="' . $x . '" y="0" width="' . $barWidth . '" height="' . ($height - 15) . '" fill="black"/>';
                }
                $x += $barWidth;
            }
        }
        
        // Adicionar texto do código
        $svg .= '<text x="' . ($width/2) . '" y="' . ($height - 2) . '" text-anchor="middle" font-family="monospace" font-size="10" fill="black">' . $code . '</text>';
        $svg .= '</svg>';
        
        return $svg;
    }
    
    /**
     * Obter padrão de barras para um dígito
     */
    private static function getBarPattern($digit) {
        $patterns = [
            0 => '0001101',
            1 => '0011001',
            2 => '0010011',
            3 => '0111101',
            4 => '0100011',
            5 => '0110001',
            6 => '0101111',
            7 => '0111011',
            8 => '0110111',
            9 => '0001011'
        ];
        
        return $patterns[$digit] ?? '0001101';
    }
    
    /**
     * Validar código de barras EAN-13
     */
    public static function validateEAN13($code) {
        if (strlen($code) != 13) {
            return false;
        }
        
        if (!ctype_digit($code)) {
            return false;
        }
        
        $checkDigit = self::calculateEAN13CheckDigit(substr($code, 0, 12));
        return $checkDigit == (int)$code[12];
    }
    
    /**
     * Formatar código de barras para exibição
     */
    public static function formatBarcode($code) {
        if (strlen($code) == 13) {
            // Formato EAN-13: 1 234567 890123
            return substr($code, 0, 1) . ' ' . 
                   substr($code, 1, 6) . ' ' . 
                   substr($code, 7, 6);
        }
        
        return $code;
    }
    
    /**
     * Gerar QR Code simples (URL para serviço externo)
     */
    public static function generateQRCodeURL($data, $size = 150) {
        $encodedData = urlencode($data);
        return "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data={$encodedData}";
    }
    
    /**
     * Gerar código único baseado em timestamp
     */
    public static function generateUniqueCode($prefix = '') {
        return $prefix . date('YmdHis') . rand(100, 999);
    }
    
    /**
     * Verificar se código interno já existe
     */
    public static function checkCodeExists($pdo, $table, $code, $excludeId = null) {
        $sql = "SELECT id FROM {$table} WHERE internal_code = ?";
        $params = [$code];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetch() !== false;
    }
    
    /**
     * Gerar próximo código disponível
     */
    public static function getNextAvailableCode($pdo, $table, $prefix) {
        $sql = "SELECT MAX(CAST(SUBSTRING(internal_code, " . (strlen($prefix) + 1) . ") AS UNSIGNED)) as max_num 
                FROM {$table} 
                WHERE internal_code LIKE ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$prefix . '%']);
        $result = $stmt->fetch();
        
        $nextNum = ($result['max_num'] ?? 0) + 1;
        
        if ($prefix == 'REQ') {
            return $prefix . str_pad($nextNum, 6, '0', STR_PAD_LEFT);
        } else {
            return $prefix . str_pad($nextNum, 5, '0', STR_PAD_LEFT);
        }
    }
}

/**
 * Funções auxiliares globais
 */

function generateRequestInternalCode($pdo) {
    return BarcodeGenerator::getNextAvailableCode($pdo, 'requests', 'REQ');
}

function generateItemInternalCode($pdo) {
    return BarcodeGenerator::getNextAvailableCode($pdo, 'items', 'ITEM');
}

function generateRequestBarcode($id) {
    return BarcodeGenerator::generateBarcode('REQ', $id);
}

function generateItemBarcode($id) {
    return BarcodeGenerator::generateBarcode('ITEM', $id);
}

function displayBarcode($code, $type = 'svg', $width = 200, $height = 50) {
    switch($type) {
        case 'svg':
            return BarcodeGenerator::generateBarcodeSVG($code, $width, $height);
        case 'ascii':
            return '<pre style="font-family: monospace; font-size: 8px;">' . 
                   BarcodeGenerator::generateBarcodeASCII($code) . '</pre>';
        case 'formatted':
            return BarcodeGenerator::formatBarcode($code);
        default:
            return $code;
    }
}

function generateQRCode($data, $size = 150) {
    return BarcodeGenerator::generateQRCodeURL($data, $size);
}
?>
