<?php
// Exportação para Excel com formatação HTML profissional
if (!isset($exportData)) {
    exit('Dados de exportação não encontrados');
}

$request = $exportData['request'];
$items = $exportData['items'];
$statusLabel = $exportData['status_label'];

// Nome do arquivo mais descritivo
$requestName = $request['title'] ?? 'Requisicao_' . $request['id'];
$safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $requestName);
$filename = $safeName . '_' . date('Y-m-d_H-i-s') . '.xls';

// Headers para Excel com HTML
header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// Calcular estatísticas
$totalItems = count($items);
$totalQuantity = array_sum(array_column($items, 'quantity'));
$averageQuantity = $totalItems > 0 ? round($totalQuantity / $totalItems, 1) : 0;

// Categorização inteligente
$categories = [];
foreach ($items as $item) {
    $category = explode(' ', $item['name'])[0];
    if (!isset($categories[$category])) {
        $categories[$category] = ['count' => 0, 'quantity' => 0];
    }
    $categories[$category]['count']++;
    $categories[$category]['quantity'] += $item['quantity'];
}

// Gerar HTML formatado para Excel - Versão Compacta
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page { size: A4; margin: 1cm; }
        body { font-family: Arial, sans-serif; margin: 0; padding: 10px; font-size: 12px; }
        .header { background: #007bff; color: white; padding: 8px; text-align: center; margin-bottom: 10px; }
        .header h1 { margin: 0; font-size: 16px; }
        .header h2 { margin: 2px 0 0 0; font-size: 14px; font-weight: normal; }
        .info-compact { display: table; width: 100%; border-collapse: collapse; margin-bottom: 10px; }
        .info-row { display: table-row; }
        .info-cell { display: table-cell; padding: 4px 8px; border: 1px solid #dee2e6; font-size: 11px; }
        .info-label { background: #f8f9fa; font-weight: bold; width: 25%; }
        .table-compact { width: 100%; border-collapse: collapse; font-size: 11px; }
        .table-compact th { background: #007bff; color: white; padding: 6px 4px; text-align: center; border: 1px solid #0056b3; font-size: 10px; }
        .table-compact td { padding: 4px; border: 1px solid #dee2e6; text-align: center; }
        .table-compact tbody tr:nth-child(even) { background: #f8f9fa; }
        .signature-compact { display: table; width: 100%; margin-top: 15px; border-collapse: collapse; }
        .signature-cell { display: table-cell; width: 33.33%; padding: 8px; text-align: center; border: 1px solid #dee2e6; font-size: 10px; }
        .signature-line { border-bottom: 1px solid #000; margin: 10px 0; height: 1px; }
        .footer-compact { text-align: center; font-size: 9px; color: #666; margin-top: 10px; }
        .status-compact { padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: bold; }
        .status-pending { background: #ffc107; color: #000; }
        .status-approved { background: #28a745; color: white; }
        .status-rejected { background: #dc3545; color: white; }
        .status-delivered { background: #17a2b8; color: white; }
        .priority-high { color: #dc3545; font-weight: bold; }
        .priority-medium { color: #ffc107; font-weight: bold; }
        .priority-low { color: #28a745; font-weight: bold; }
        .priority-urgent { color: #dc3545; font-weight: bold; text-decoration: underline; }
    </style>
</head>
<body>

    <!-- Cabeçalho Compacto -->
    <div class="header">
        <h1>🍽️ REQUISIÇÃO DE MATERIAL</h1>
        <h2>' . htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']) . '</h2>
    </div>

    <!-- Informações Essenciais -->
    <div class="info-compact">
        <div class="info-row">
            <div class="info-cell info-label">ID</div>
            <div class="info-cell">#' . $request['id'] . '</div>
            <div class="info-cell info-label">Solicitante</div>
            <div class="info-cell">' . htmlspecialchars($request['username']) . '</div>
        </div>
        <div class="info-row">
            <div class="info-cell info-label">Data</div>
            <div class="info-cell">' . date('d/m/Y H:i', strtotime($request['request_date'])) . '</div>
            <div class="info-cell info-label">Status</div>
            <div class="info-cell">
                <span class="status-compact status-' . $request['status'] . '">' . strtoupper($statusLabel) . '</span>
            </div>
        </div>';

// Adicionar campos extras se existirem
if (!empty($request['priority']) || !empty($request['department'])) {
    echo '<div class="info-row">';
    if (!empty($request['priority'])) {
        $priorityClass = 'priority-' . $request['priority'];
        $priorityLabel = ['low' => 'Baixa', 'medium' => 'Média', 'high' => 'Alta', 'urgent' => 'URGENTE'][$request['priority']] ?? ucfirst($request['priority']);
        echo '<div class="info-cell info-label">Prioridade</div>
              <div class="info-cell"><span class="' . $priorityClass . '">' . $priorityLabel . '</span></div>';
    } else {
        echo '<div class="info-cell"></div><div class="info-cell"></div>';
    }

    if (!empty($request['department'])) {
        echo '<div class="info-cell info-label">Departamento</div>
              <div class="info-cell">' . htmlspecialchars($request['department']) . '</div>';
    } else {
        echo '<div class="info-cell"></div><div class="info-cell"></div>';
    }
    echo '</div>';
}

echo '    </div>';

// Tabela de itens compacta
echo '<table class="table-compact">
    <thead>
        <tr>
            <th>#</th>
            <th>Item</th>
            <th>Descrição</th>
            <th>Qtd</th>
            <th>Un</th>
        </tr>
    </thead>
    <tbody>';

$itemNumber = 1;
foreach ($items as $item) {
    echo '<tr>
        <td>' . $itemNumber . '</td>
        <td><strong>' . htmlspecialchars($item['name']) . '</strong></td>
        <td>' . htmlspecialchars(substr($item['description'], 0, 40)) . (strlen($item['description']) > 40 ? '...' : '') . '</td>
        <td style="text-align: center; font-weight: bold;">' . $item['quantity'] . '</td>
        <td style="text-align: center;">' . htmlspecialchars($item['unit']) . '</td>
    </tr>';
    $itemNumber++;
}

echo '</tbody>
</table>';

// Resumo compacto
echo '<div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-left: 3px solid #007bff; font-size: 11px;">
    <strong>RESUMO:</strong> ' . count($items) . ' tipos de itens • ' . $totalQuantity . ' unidades totais • Gerado em ' . date('d/m/Y H:i') . '
</div>';

// Observações se existirem
if (!empty($request['notes'])) {
    echo '<div style="margin-top: 10px; padding: 6px; background: #fff3cd; border-left: 3px solid #ffc107; font-size: 11px;">
        <strong>OBSERVAÇÕES:</strong> ' . htmlspecialchars($request['notes']) . '
    </div>';
}

// Assinaturas compactas
echo '<div class="signature-compact" style="margin-top: 15px;">
    <div class="signature-cell">
        <strong>SOLICITANTE</strong><br>
        <div class="signature-line"></div>
        ' . htmlspecialchars($request['username']) . '<br>
        <small>' . date('d/m/Y', strtotime($request['request_date'])) . '</small>
    </div>
    <div class="signature-cell">
        <strong>APROVAÇÃO</strong><br>
        <div class="signature-line"></div>
        _____________________<br>
        <small>___/___/______</small>
    </div>
    <div class="signature-cell">
        <strong>ENTREGA</strong><br>
        <div class="signature-line"></div>
        _____________________<br>
        <small>___/___/______</small>
    </div>
</div>';

// Rodapé compacto
echo '<div class="footer-compact">
    Sistema de Requisição • Documento gerado em ' . date('d/m/Y H:i') . ' • ID: #' . $request['id'] . ' • Status: ' . strtoupper($statusLabel) . '
</div>

</body>
</html>';

exit;
?>
