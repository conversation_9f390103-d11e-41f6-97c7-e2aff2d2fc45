<?php
// Exportação para Excel com formatação HTML profissional
if (!isset($exportData)) {
    exit('Dados de exportação não encontrados');
}

$request = $exportData['request'];
$items = $exportData['items'];
$statusLabel = $exportData['status_label'];

// Nome do arquivo mais descritivo
$requestName = $request['title'] ?? 'Requisicao_' . $request['id'];
$safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $requestName);
$filename = $safeName . '_' . date('Y-m-d_H-i-s') . '.xls';

// Headers para Excel com HTML
header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// Calcular estatísticas
$totalItems = count($items);
$totalQuantity = array_sum(array_column($items, 'quantity'));
$averageQuantity = $totalItems > 0 ? round($totalQuantity / $totalItems, 1) : 0;

// Categorização inteligente
$categories = [];
foreach ($items as $item) {
    $category = explode(' ', $item['name'])[0];
    if (!isset($categories[$category])) {
        $categories[$category] = ['count' => 0, 'quantity' => 0];
    }
    $categories[$category]['count']++;
    $categories[$category]['quantity'] += $item['quantity'];
}

// Gerar HTML formatado para Excel
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 20px; text-align: center; border-radius: 10px; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 24px; }
        .header h2 { margin: 5px 0 0 0; font-size: 18px; font-weight: normal; }
        .section { margin: 20px 0; }
        .section-title { background: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; font-weight: bold; font-size: 16px; margin-bottom: 10px; }
        .info-grid { display: table; width: 100%; border-collapse: collapse; }
        .info-row { display: table-row; }
        .info-label { display: table-cell; padding: 8px; background: #e9ecef; font-weight: bold; border: 1px solid #dee2e6; width: 30%; }
        .info-value { display: table-cell; padding: 8px; border: 1px solid #dee2e6; }
        .stats-grid { display: table; width: 100%; border-collapse: collapse; margin: 10px 0; }
        .stats-row { display: table-row; }
        .stats-cell { display: table-cell; padding: 15px; text-align: center; border: 2px solid #007bff; background: #f8f9fa; font-weight: bold; }
        .stats-number { font-size: 24px; color: #007bff; display: block; }
        .stats-label { font-size: 12px; color: #666; }
        .table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .table th { background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 12px; text-align: center; border: 1px solid #0056b3; font-weight: bold; }
        .table td { padding: 10px; border: 1px solid #dee2e6; text-align: center; }
        .table tbody tr:nth-child(even) { background: #f8f9fa; }
        .table tbody tr:hover { background: #e3f2fd; }
        .item-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; font-weight: bold; }
        .quantity-high { background: #dc3545; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold; }
        .quantity-medium { background: #ffc107; color: #212529; padding: 4px 8px; border-radius: 4px; font-weight: bold; }
        .quantity-low { background: #6c757d; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold; }
        .status-badge { padding: 6px 12px; border-radius: 20px; font-weight: bold; text-transform: uppercase; }
        .status-pending { background: #ffc107; color: #212529; }
        .status-approved { background: #28a745; color: white; }
        .status-rejected { background: #dc3545; color: white; }
        .status-delivered { background: #17a2b8; color: white; }
        .signature-section { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .signature-grid { display: table; width: 100%; }
        .signature-col { display: table-cell; width: 33.33%; padding: 15px; text-align: center; border: 1px solid #dee2e6; }
        .signature-title { font-weight: bold; color: #007bff; margin-bottom: 10px; }
        .signature-line { border-bottom: 2px solid #007bff; margin: 20px 0; height: 2px; }
        .footer { background: #343a40; color: white; padding: 15px; text-align: center; border-radius: 10px; margin-top: 30px; }
        .watermark { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); font-size: 100px; color: rgba(0, 123, 255, 0.1); z-index: -1; font-weight: bold; }
    </style>
</head>
<body>
    <div class="watermark">REQUISIÇÃO</div>

    <!-- Cabeçalho Principal -->
    <div class="header">
        <h1>🍽️ SISTEMA DE REQUISIÇÃO DE MATERIAL DE COZINHA</h1>
        <h2>' . htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']) . '</h2>
    </div>

    <!-- Informações Gerais -->
    <div class="section">
        <div class="section-title">📋 INFORMAÇÕES GERAIS</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">ID da Requisição</div>
                <div class="info-value">#' . $request['id'] . '</div>
            </div>
            <div class="info-row">
                <div class="info-label">Nome da Requisição</div>
                <div class="info-value">' . htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']) . '</div>
            </div>
            <div class="info-row">
                <div class="info-label">Solicitante</div>
                <div class="info-value">' . htmlspecialchars($request['username']) . '</div>
            </div>
            <div class="info-row">
                <div class="info-label">Data da Requisição</div>
                <div class="info-value">' . date('d/m/Y H:i', strtotime($request['request_date'])) . '</div>
            </div>
            <div class="info-row">
                <div class="info-label">Status Atual</div>
                <div class="info-value">
                    <span class="status-badge status-' . $request['status'] . '">' . strtoupper($statusLabel) . '</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Estatísticas -->
    <div class="section">
        <div class="section-title">📊 ESTATÍSTICAS DA REQUISIÇÃO</div>
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stats-cell">
                    <span class="stats-number">' . $totalItems . '</span>
                    <span class="stats-label">TIPOS DE ITENS</span>
                </div>
                <div class="stats-cell">
                    <span class="stats-number">' . $totalQuantity . '</span>
                    <span class="stats-label">QUANTIDADE TOTAL</span>
                </div>
                <div class="stats-cell">
                    <span class="stats-number">' . count($categories) . '</span>
                    <span class="stats-label">CATEGORIAS</span>
                </div>
                <div class="stats-cell">
                    <span class="stats-number">' . $averageQuantity . '</span>
                    <span class="stats-label">MÉDIA POR ITEM</span>
                </div>
            </div>
        </div>
    </div>';

// Resumo por categoria (se houver múltiplas)
if (count($categories) > 1) {
    echo '<div class="section">
        <div class="section-title">🏷️ RESUMO POR CATEGORIA</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Categoria</th>
                    <th>Tipos de Itens</th>
                    <th>Quantidade Total</th>
                    <th>Percentual</th>
                </tr>
            </thead>
            <tbody>';

    foreach ($categories as $cat => $data) {
        $percentage = round(($data['quantity'] / $totalQuantity) * 100, 1);
        echo '<tr>
                <td><strong>' . htmlspecialchars($cat) . '</strong></td>
                <td>' . $data['count'] . '</td>
                <td>' . $data['quantity'] . '</td>
                <td>' . $percentage . '%</td>
              </tr>';
    }

    echo '</tbody>
        </table>
    </div>';
}

// Tabela de itens detalhada
echo '<div class="section">
        <div class="section-title">📦 ITENS SOLICITADOS - DETALHAMENTO COMPLETO</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Nº</th>
                    <th>Nome do Item</th>
                    <th>Descrição Completa</th>
                    <th>Quantidade</th>
                    <th>Unidade</th>
                    <th>Classificação</th>
                </tr>
            </thead>
            <tbody>';

$itemNumber = 1;
foreach ($items as $item) {
    // Classificação por quantidade
    if ($item['quantity'] >= 10) {
        $quantityClass = 'quantity-high';
        $quantityLabel = 'ALTA';
    } elseif ($item['quantity'] >= 5) {
        $quantityClass = 'quantity-medium';
        $quantityLabel = 'MÉDIA';
    } else {
        $quantityClass = 'quantity-low';
        $quantityLabel = 'BAIXA';
    }

    echo '<tr>
            <td><span class="item-number">' . $itemNumber . '</span></td>
            <td><strong>' . htmlspecialchars($item['name']) . '</strong></td>
            <td>' . htmlspecialchars($item['description']) . '</td>
            <td><strong>' . $item['quantity'] . '</strong></td>
            <td>' . htmlspecialchars($item['unit']) . '</td>
            <td><span class="' . $quantityClass . '">' . $quantityLabel . '</span></td>
          </tr>';
    $itemNumber++;
}

echo '</tbody>
        </table>
    </div>';

// Seção de assinaturas
echo '<div class="section">
        <div class="section-title">✍️ CONTROLE E ASSINATURAS</div>
        <div class="signature-section">
            <div class="signature-grid">
                <div class="signature-col">
                    <div class="signature-title">SOLICITADO POR</div>
                    <div><strong>' . htmlspecialchars($request['username']) . '</strong></div>
                    <div>' . date('d/m/Y H:i', strtotime($request['request_date'])) . '</div>
                    <div class="signature-line"></div>
                    <div>Assinatura</div>
                </div>
                <div class="signature-col">
                    <div class="signature-title">APROVADO POR</div>
                    <div>_________________________</div>
                    <div>Data: ___/___/______</div>
                    <div class="signature-line"></div>
                    <div>Assinatura</div>
                </div>
                <div class="signature-col">
                    <div class="signature-title">ENTREGUE POR</div>
                    <div>_________________________</div>
                    <div>Data: ___/___/______</div>
                    <div class="signature-line"></div>
                    <div>Assinatura</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Observações -->
    <div class="section">
        <div class="section-title">📝 OBSERVAÇÕES GERAIS</div>
        <div style="border: 1px solid #dee2e6; padding: 20px; min-height: 100px; background: white;">
            <div style="border-bottom: 1px solid #dee2e6; margin-bottom: 15px; height: 20px;"></div>
            <div style="border-bottom: 1px solid #dee2e6; margin-bottom: 15px; height: 20px;"></div>
            <div style="border-bottom: 1px solid #dee2e6; margin-bottom: 15px; height: 20px;"></div>
            <div style="border-bottom: 1px solid #dee2e6; margin-bottom: 15px; height: 20px;"></div>
        </div>
    </div>

    <!-- Rodapé -->
    <div class="footer">
        <div><strong>📊 INFORMAÇÕES DO RELATÓRIO</strong></div>
        <div style="margin-top: 10px;">
            Sistema de Requisição de Material de Cozinha v2.0 |
            Gerado em: ' . date('d/m/Y H:i:s') . ' |
            Por: ' . ($_SESSION['username'] ?? 'Sistema') . ' |
            Formato: Excel HTML
        </div>
        <div style="margin-top: 5px; font-size: 12px; opacity: 0.8;">
            Este documento possui validade legal para controle interno de materiais
        </div>
    </div>
</body>
</html>';

exit;
?>
