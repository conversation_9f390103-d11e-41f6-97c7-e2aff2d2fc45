<?php
// Exportação para Excel (CSV com separador de ponto e vírgula para compatibilidade)
if (!isset($exportData)) {
    exit('Dados de exportação não encontrados');
}

$request = $exportData['request'];
$items = $exportData['items'];
$statusLabel = $exportData['status_label'];

// Nome do arquivo mais descritivo
$requestName = $request['title'] ?? 'Requisicao_' . $request['id'];
$safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $requestName);
$filename = $safeName . '_' . date('Y-m-d_H-i-s') . '.csv';

// Headers para download
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// Abrir output
$output = fopen('php://output', 'w');

// BOM para UTF-8 (para Excel reconhecer acentos)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Cabeçalho principal com formatação
fputcsv($output, ['=== SISTEMA DE REQUISIÇÃO DE MATERIAL DE COZINHA ==='], ';');
fputcsv($output, [''], ';');
fputcsv($output, ['RELATÓRIO DE REQUISIÇÃO'], ';');
fputcsv($output, [''], ';');

// Seção de informações gerais
fputcsv($output, ['INFORMAÇÕES GERAIS'], ';');
fputcsv($output, ['================'], ';');
fputcsv($output, ['ID da Requisição:', '#' . $request['id']], ';');
fputcsv($output, ['Nome da Requisição:', $request['title'] ?? 'Requisição #' . $request['id']], ';');
fputcsv($output, ['Solicitante:', $request['username']], ';');
fputcsv($output, ['Data da Requisição:', date('d/m/Y H:i', strtotime($request['request_date']))], ';');
fputcsv($output, ['Status Atual:', strtoupper($statusLabel)], ';');
fputcsv($output, [''], ';');

// Seção de estatísticas
fputcsv($output, ['ESTATÍSTICAS'], ';');
fputcsv($output, ['============'], ';');
fputcsv($output, ['Total de Tipos de Itens:', count($items)], ';');
fputcsv($output, ['Quantidade Total Solicitada:', array_sum(array_column($items, 'quantity'))], ';');

// Calcular estatísticas por categoria (se houver)
$totalValue = 0;
$categories = [];
foreach ($items as $item) {
    // Simular categorização básica por primeira palavra
    $category = explode(' ', $item['name'])[0];
    if (!isset($categories[$category])) {
        $categories[$category] = ['count' => 0, 'quantity' => 0];
    }
    $categories[$category]['count']++;
    $categories[$category]['quantity'] += $item['quantity'];
}

fputcsv($output, ['Categorias Diferentes:', count($categories)], ';');
fputcsv($output, [''], ';');

// Detalhamento por categoria
if (count($categories) > 1) {
    fputcsv($output, ['RESUMO POR CATEGORIA'], ';');
    fputcsv($output, ['===================='], ';');
    fputcsv($output, ['Categoria', 'Tipos de Itens', 'Quantidade Total'], ';');
    foreach ($categories as $cat => $data) {
        fputcsv($output, [$cat, $data['count'], $data['quantity']], ';');
    }
    fputcsv($output, [''], ';');
}

// Cabeçalho da tabela de itens detalhada
fputcsv($output, ['ITENS SOLICITADOS - DETALHAMENTO COMPLETO'], ';');
fputcsv($output, ['=========================================='], ';');
fputcsv($output, ['Nº', 'Nome do Item', 'Descrição Completa', 'Quantidade', 'Unidade', 'Observações'], ';');

// Dados dos itens com numeração e observações
$itemNumber = 1;
foreach ($items as $item) {
    $observations = '';

    // Adicionar observações baseadas na quantidade
    if ($item['quantity'] >= 10) {
        $observations = 'QUANTIDADE ALTA';
    } elseif ($item['quantity'] >= 5) {
        $observations = 'Quantidade Média';
    } else {
        $observations = 'Quantidade Baixa';
    }

    fputcsv($output, [
        $itemNumber,
        $item['name'],
        $item['description'],
        $item['quantity'],
        $item['unit'],
        $observations
    ], ';');
    $itemNumber++;
}

// Seção de assinaturas e aprovações
fputcsv($output, [''], ';');
fputcsv($output, ['CONTROLE E ASSINATURAS'], ';');
fputcsv($output, ['======================='], ';');
fputcsv($output, ['Solicitado por:', $request['username']], ';');
fputcsv($output, ['Data da Solicitação:', date('d/m/Y H:i', strtotime($request['request_date']))], ';');
fputcsv($output, [''], ';');
fputcsv($output, ['Aprovado por:', '________________________'], ';');
fputcsv($output, ['Data da Aprovação:', '________________________'], ';');
fputcsv($output, [''], ';');
fputcsv($output, ['Entregue por:', '________________________'], ';');
fputcsv($output, ['Data da Entrega:', '________________________'], ';');
fputcsv($output, ['Recebido por:', '________________________'], ';');
fputcsv($output, [''], ';');

// Rodapé profissional
fputcsv($output, ['INFORMAÇÕES DO RELATÓRIO'], ';');
fputcsv($output, ['========================'], ';');
fputcsv($output, ['Sistema:', 'Sistema de Requisição de Material de Cozinha'], ';');
fputcsv($output, ['Relatório gerado em:', date('d/m/Y H:i:s')], ';');
fputcsv($output, ['Gerado por:', $_SESSION['username'] ?? 'Sistema'], ';');
fputcsv($output, ['Formato:', 'Excel/CSV'], ';');
fputcsv($output, ['Versão:', '2.0'], ';');

fclose($output);
exit;
?>
