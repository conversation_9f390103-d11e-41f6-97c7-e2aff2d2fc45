-- Atualização do banco de dados para adicionar nome da requisição
-- Execute este script para adicionar a coluna 'title' na tabela requests

ALTER TABLE requests ADD COLUMN title VARCHAR(255) DEFAULT NULL AFTER user_id;

-- Atualizar requisições existentes com títulos padrão
UPDATE requests SET title = CONCAT('Requisição #', id) WHERE title IS NULL;

-- Comentário: A coluna title permite que os usuários deem nomes personalizados às suas requisições
-- Exemplo: "Requisição para Festa Junina", "Estoque Semanal", "Ingredientes para Pizza"
