# 🔧 Guia de Resolução de Problemas - Sistema de Requisição

## 🚨 Erro "Internal Server Error" - Soluções

### **1. Problema com .htaccess**

#### **Solução Rápida:**
1. **Renomear .htaccess** temporariamente:
   ```bash
   mv .htaccess .htaccess.backup
   ```

2. **Criar .htaccess simples:**
   ```apache
   # Configurações básicas
   AddDefaultCharset UTF-8
   DirectoryIndex index.php
   Options -Indexes
   ```

3. **Testar o sistema** sem configurações avançadas

#### **Verificar Módulos Apache:**
- `mod_rewrite` (para URLs amigáveis)
- `mod_headers` (para headers de segurança)
- `mod_expires` (para cache)
- `mod_deflate` (para compressão)

### **2. Problema com SEO Meta Tags**

#### **Arquivos de Teste Criados:**
- **`test_seo.php`** - Teste isolado do SEO
- **`index_simple.php`** - Versão sem SEO
- **`debug.php`** - Diagnóstico completo

#### **Verificar:**
1. **Arquivo `includes/seo_meta.php`** existe
2. **Permissões** de leitura nos diretórios
3. **Variáveis $_SERVER** disponíveis

### **3. Problema com Banco de Dados**

#### **Verificar Conexão:**
```php
try {
    require_once 'config/db_connect.php';
    echo "✅ Conexão OK";
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage();
}
```

#### **Verificar Tabelas:**
- `users` (usuários)
- `requests` (requisições)
- `items` (itens)
- `request_items` (itens das requisições)

### **4. Problema com Permissões**

#### **Verificar Permissões de Diretórios:**
```bash
# Linux/Mac
chmod 755 .
chmod 755 config includes exports
chmod 644 *.php

# Windows (via propriedades)
# Dar permissão de leitura/escrita para IIS_IUSRS
```

### **5. Problema com PHP**

#### **Verificar Versão PHP:**
- **Mínimo:** PHP 7.4
- **Recomendado:** PHP 8.0+

#### **Extensões Necessárias:**
- `pdo_mysql` (banco de dados)
- `mbstring` (strings multibyte)
- `json` (manipulação JSON)

### **6. Logs de Erro**

#### **Verificar Logs:**
- **Apache:** `/var/log/apache2/error.log`
- **WAMP:** `wamp64/logs/apache_error.log`
- **XAMPP:** `xampp/apache/logs/error.log`

#### **Habilitar Logs PHP:**
```php
// No início do arquivo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
```

## 🛠️ Ferramentas de Diagnóstico

### **Arquivos Criados para Debug:**

#### **1. `debug.php`**
- ✅ Verifica arquivos essenciais
- ✅ Testa conexão com banco
- ✅ Verifica SEO meta tags
- ✅ Mostra variáveis de servidor
- ✅ Verifica permissões

#### **2. `test_seo.php`**
- ✅ Teste isolado do sistema SEO
- ✅ Captura erros de inclusão
- ✅ Fallback para erro

#### **3. `index_simple.php`**
- ✅ Versão sem SEO para teste
- ✅ Funcionalidades básicas
- ✅ Links para diagnóstico

## 🔄 Passos de Resolução

### **Passo 1: Identificar o Problema**
1. Acessar `debug.php`
2. Verificar logs de erro
3. Testar `index_simple.php`

### **Passo 2: Isolar o Problema**
1. **Se `index_simple.php` funciona:** Problema no SEO
2. **Se `debug.php` mostra erros:** Problema na configuração
3. **Se nada funciona:** Problema no servidor/PHP

### **Passo 3: Aplicar Solução**

#### **Para Problemas de SEO:**
```php
// Comentar temporariamente no index.php
// <?php include 'includes/seo_meta.php'; ?>

// Usar meta tags básicas
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Sistema de Requisição</title>
```

#### **Para Problemas de .htaccess:**
```apache
# .htaccess mínimo
AddDefaultCharset UTF-8
DirectoryIndex index.php
Options -Indexes
```

#### **Para Problemas de Banco:**
```php
// Verificar config/db_connect.php
$host = 'localhost';
$dbname = 'os_cozinha';
$username = 'root';
$password = '';
```

## 📋 Checklist de Verificação

### **Arquivos Essenciais:**
- [ ] `config/db_connect.php` existe
- [ ] `includes/seo_meta.php` existe
- [ ] `includes/navbar.php` existe
- [ ] `.htaccess` não causa erro

### **Configurações:**
- [ ] PHP 7.4+ instalado
- [ ] Extensões PHP necessárias
- [ ] Apache com módulos necessários
- [ ] Banco de dados criado

### **Permissões:**
- [ ] Diretórios legíveis
- [ ] Arquivos PHP executáveis
- [ ] Banco de dados acessível

### **Funcionalidades:**
- [ ] Login funciona
- [ ] Dashboard carrega
- [ ] Navegação funciona
- [ ] Banco conecta

## 🚀 Soluções Rápidas

### **Solução 1: Desabilitar SEO Temporariamente**
```php
// No index.php, comentar:
// <?php include 'includes/seo_meta.php'; ?>

// Adicionar meta tags básicas:
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Sistema de Requisição</title>
```

### **Solução 2: .htaccess Mínimo**
```apache
AddDefaultCharset UTF-8
DirectoryIndex index.php
Options -Indexes
```

### **Solução 3: Debug Mode**
```php
// Adicionar no início dos arquivos PHP
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## 📞 Suporte

### **Se o problema persistir:**
1. **Verificar logs** do servidor web
2. **Testar em ambiente** diferente
3. **Verificar versões** PHP/Apache/MySQL
4. **Consultar documentação** do servidor

### **Arquivos de Backup:**
- `index_simple.php` - Versão funcional básica
- `debug.php` - Ferramenta de diagnóstico
- `test_seo.php` - Teste isolado SEO

---

**💡 Dica:** Sempre teste em ambiente de desenvolvimento antes de aplicar em produção!
