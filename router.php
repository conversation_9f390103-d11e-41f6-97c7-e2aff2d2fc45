<?php
/**
 * Sistema de Roteamento com Querystring
 * Gerencia navegação entre páginas e redireciona para 404 quando necessário
 */

// Iniciar sessão se não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Definir páginas válidas do sistema
$validPages = [
    // Páginas principais
    'dashboard' => [
        'file' => 'index.php',
        'title' => 'Dashboard',
        'auth_required' => true,
        'admin_only' => false
    ],
    'home' => [
        'file' => 'index.php',
        'title' => 'Dashboard',
        'auth_required' => true,
        'admin_only' => false
    ],
    
    // Requisições
    'nova-requisicao' => [
        'file' => 'request_form.php',
        'title' => 'Nova Requisição',
        'auth_required' => true,
        'admin_only' => false
    ],
    'minhas-requisicoes' => [
        'file' => 'my_requests.php',
        'title' => 'Minhas Requisições',
        'auth_required' => true,
        'admin_only' => false
    ],
    'ver-requisicao' => [
        'file' => 'view_request.php',
        'title' => 'Detalhes da Requisição',
        'auth_required' => true,
        'admin_only' => false,
        'params' => ['id']
    ],
    'editar-requisicao' => [
        'file' => 'edit_request.php',
        'title' => 'Editar Requisição',
        'auth_required' => true,
        'admin_only' => false,
        'params' => ['id']
    ],
    
    // Páginas administrativas
    'gerenciar-requisicoes' => [
        'file' => 'manage_requests.php',
        'title' => 'Gerenciar Requisições',
        'auth_required' => true,
        'admin_only' => true
    ],
    'gerenciar-itens' => [
        'file' => 'manage_items.php',
        'title' => 'Gerenciar Itens',
        'auth_required' => true,
        'admin_only' => true
    ],
    'gerenciar-usuarios' => [
        'file' => 'manage_users.php',
        'title' => 'Gerenciar Usuários',
        'auth_required' => true,
        'admin_only' => true
    ],
    'ver-usuario' => [
        'file' => 'view_user.php',
        'title' => 'Detalhes do Usuário',
        'auth_required' => true,
        'admin_only' => true,
        'params' => ['id']
    ],
    
    // Perfil e configurações
    'perfil' => [
        'file' => 'profile.php',
        'title' => 'Meu Perfil',
        'auth_required' => true,
        'admin_only' => false
    ],
    
    // Exportação
    'exportar-requisicao' => [
        'file' => 'export_request.php',
        'title' => 'Exportar Requisição',
        'auth_required' => true,
        'admin_only' => false,
        'params' => ['id', 'format']
    ],
    
    // Códigos de barras
    'codigo-barras' => [
        'file' => 'view_barcode.php',
        'title' => 'Código de Barras',
        'auth_required' => true,
        'admin_only' => false,
        'params' => ['type', 'id']
    ],
    
    // Configuração
    'configurar-produtos' => [
        'file' => 'setup_products.php',
        'title' => 'Configurar Produtos',
        'auth_required' => true,
        'admin_only' => true
    ],
    'adicionar-produtos-cozinha' => [
        'file' => 'add_kitchen_products.php',
        'title' => 'Adicionar Produtos de Cozinha',
        'auth_required' => true,
        'admin_only' => true
    ],
    
    // Páginas de teste e debug
    'teste-layout' => [
        'file' => 'test_layout.php',
        'title' => 'Teste de Layout',
        'auth_required' => true,
        'admin_only' => true
    ],
    'debug-sistema' => [
        'file' => 'debug_layout.php',
        'title' => 'Debug do Sistema',
        'auth_required' => true,
        'admin_only' => true
    ],
    'verificar-sistema' => [
        'file' => 'test_system.php',
        'title' => 'Verificação do Sistema',
        'auth_required' => true,
        'admin_only' => true
    ]
];

/**
 * Função para verificar se usuário está autenticado
 */
if (!function_exists('isAuthenticated')) {
    function isAuthenticated() {
        // Iniciar sessão se não foi iniciada
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        return isset($_SESSION['user_id']);
    }
}



/**
 * Função para redirecionar para página 404
 */
if (!function_exists('redirect404')) {
    function redirect404() {
    // Verificar se arquivo 404.php existe
    if (file_exists('404.php')) {
        http_response_code(404);
        include '404.php';
    } else {
        // Fallback para 404 simples
        http_response_code(404);
        ?>
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>404 - Página não encontrada</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>
        <body class="bg-light">
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-6 text-center">
                        <h1 class="display-1 text-muted">404</h1>
                        <h2>Página não encontrada</h2>
                        <p class="lead">A página que você está procurando não existe.</p>
                        <a href="?page=dashboard" class="btn btn-primary">Voltar ao Dashboard</a>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
    }
    exit;
    }
}

/**
 * Função para redirecionar para login
 */
if (!function_exists('redirectLogin')) {
    function redirectLogin() {
        header('Location: login.php');
        exit;
    }
}

/**
 * Função para redirecionar para acesso negado
 */
if (!function_exists('redirectAccessDenied')) {
    function redirectAccessDenied() {
        header('Location: ?page=dashboard&error=access_denied');
        exit;
    }
}

/**
 * Função principal de roteamento
 */
function route() {
    global $validPages;
    
    // Obter página solicitada
    $requestedPage = $_GET['page'] ?? 'dashboard';
    
    // Verificar se página existe
    if (!isset($validPages[$requestedPage])) {
        redirect404();
        return;
    }
    
    $pageConfig = $validPages[$requestedPage];
    
    // Verificar autenticação
    if ($pageConfig['auth_required'] && !isAuthenticated()) {
        redirectLogin();
        return;
    }
    
    // Verificar permissão de admin
    if ($pageConfig['admin_only'] && !isAdmin()) {
        redirectAccessDenied();
        return;
    }
    
    // Verificar se arquivo existe
    if (!file_exists($pageConfig['file'])) {
        redirect404();
        return;
    }
    
    // Verificar parâmetros obrigatórios
    if (isset($pageConfig['params'])) {
        foreach ($pageConfig['params'] as $param) {
            if (!isset($_GET[$param]) || empty($_GET[$param])) {
                redirect404();
                return;
            }
        }
    }
    
    // Incluir arquivo da página
    $filePath = $pageConfig['file'];

    // Verificar se o arquivo existe novamente (segurança extra)
    if (!file_exists($filePath)) {
        error_log("Router: Arquivo não encontrado: " . $filePath);
        redirect404();
        return;
    }

    // Incluir o arquivo
    try {
        include $filePath;
    } catch (Exception $e) {
        error_log("Router: Erro ao incluir arquivo " . $filePath . ": " . $e->getMessage());
        redirect404();
    } catch (Error $e) {
        error_log("Router: Erro fatal ao incluir arquivo " . $filePath . ": " . $e->getMessage());
        redirect404();
    }
}

/**
 * Função para gerar URL amigável
 */
if (!function_exists('generateUrl')) {
    function generateUrl($page, $params = []) {
    // Mapear páginas para URLs amigáveis
    $friendlyUrls = [
        'dashboard' => 'dashboard',
        'nova-requisicao' => 'nova-requisicao',
        'minhas-requisicoes' => 'minhas-requisicoes',
        'perfil' => 'perfil',
        'gerenciar-requisicoes' => 'gerenciar-requisicoes',
        'gerenciar-itens' => 'gerenciar-itens',
        'gerenciar-usuarios' => 'gerenciar-usuarios',
        'configurar-produtos' => 'configurar-produtos',
        'ver-requisicao' => 'requisicao',
        'editar-requisicao' => 'requisicao',
        'ver-usuario' => 'usuario',
        'exportar-requisicao' => 'requisicao',
        'codigo-barras' => 'codigo-barras'
    ];

    // Se a página tem URL amigável
    if (isset($friendlyUrls[$page])) {
        $baseUrl = $friendlyUrls[$page];

        // URLs especiais com parâmetros
        switch ($page) {
            case 'ver-requisicao':
                if (isset($params['id'])) {
                    return $baseUrl . '/' . $params['id'];
                }
                break;

            case 'editar-requisicao':
                if (isset($params['id'])) {
                    return $baseUrl . '/' . $params['id'] . '/editar';
                }
                break;

            case 'ver-usuario':
                if (isset($params['id'])) {
                    return $baseUrl . '/' . $params['id'];
                }
                break;

            case 'exportar-requisicao':
                if (isset($params['id']) && isset($params['format'])) {
                    return 'requisicao/' . $params['id'] . '/exportar/' . $params['format'];
                }
                break;

            case 'codigo-barras':
                if (isset($params['type']) && isset($params['id'])) {
                    return $baseUrl . '/' . $params['type'] . '/' . $params['id'];
                }
                break;

            default:
                // URL simples sem parâmetros
                return $baseUrl;
        }
    }

    // Fallback para querystring se não há URL amigável
    $url = '?page=' . urlencode($page);
    foreach ($params as $key => $value) {
        $url .= '&' . urlencode($key) . '=' . urlencode($value);
    }

    return $url;
    }
}

/**
 * Função para gerar URL com querystring (compatibilidade)
 */
if (!function_exists('generateQueryUrl')) {
    function generateQueryUrl($page, $params = []) {
        $url = '?page=' . urlencode($page);

        foreach ($params as $key => $value) {
            $url .= '&' . urlencode($key) . '=' . urlencode($value);
        }

        return $url;
    }
}

/**
 * Função para verificar se o usuário é admin (necessária para o router)
 */
if (!function_exists('isAdmin')) {
    function isAdmin() {
        return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
    }
}

/**
 * Função para obter título da página atual
 */
function getCurrentPageTitle() {
    global $validPages;
    $currentPage = $_GET['page'] ?? 'dashboard';
    
    return $validPages[$currentPage]['title'] ?? 'Sistema de Requisições';
}

/**
 * Função para verificar se página atual é válida
 */
function isValidPage($page) {
    global $validPages;
    return isset($validPages[$page]);
}

/**
 * Função para obter lista de páginas para menu
 */
function getMenuPages() {
    global $validPages;
    $menuPages = [];

    foreach ($validPages as $slug => $config) {
        // Pular páginas que requerem parâmetros ou são de teste
        if (isset($config['params']) ||
            in_array($slug, ['teste-layout', 'debug-sistema', 'verificar-sistema'])) {
            continue;
        }

        // Verificar permissões
        if ($config['admin_only'] && !isAdmin()) {
            continue;
        }

        $menuPages[$slug] = $config;
    }

    return $menuPages;
}

/**
 * Função para verificar se estamos usando URLs amigáveis
 */
function isUsingFriendlyUrls() {
    // Verificar se a URL atual não contém querystring 'page'
    return !isset($_GET['page']) && $_SERVER['REQUEST_URI'] !== '/';
}

/**
 * Função para obter URL base do sistema
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);

    // Remover barra dupla se existir
    $path = rtrim($path, '/');

    return $protocol . '://' . $host . $path . '/';
}

/**
 * Função para redirecionar para URL amigável
 */
function redirectToFriendlyUrl($page, $params = []) {
    $friendlyUrl = generateUrl($page, $params);

    // Se a URL gerada não é uma querystring, redirecionar
    if (strpos($friendlyUrl, '?') !== 0) {
        $baseUrl = getBaseUrl();
        header('Location: ' . $baseUrl . $friendlyUrl, true, 301);
        exit;
    }
}

// Executar roteamento se este arquivo for chamado diretamente
if (basename($_SERVER['PHP_SELF']) === 'router.php') {
    route();
}
?>
