<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

$requestId = (int)($_GET['id'] ?? 0);
$success = '';
$error = '';

if ($requestId <= 0) {
    header('Location: my_requests.php');
    exit;
}

// Buscar dados da requisição
$stmt = $pdo->prepare("
    SELECT r.*, u.username 
    FROM requests r 
    JOIN users u ON r.user_id = u.id 
    WHERE r.id = ?
");
$stmt->execute([$requestId]);
$request = $stmt->fetch();

if (!$request) {
    header('Location: my_requests.php');
    exit;
}

// Verificar se o usuário pode editar esta requisição
if ($_SESSION['role'] != 'admin' && $request['user_id'] != $_SESSION['user_id']) {
    header('Location: my_requests.php');
    exit;
}

// Verificar se a requisição pode ser editada (apenas pendentes)
if ($request['status'] != 'pending') {
    $error = 'Apenas requisições pendentes podem ser editadas';
}

// Buscar itens atuais da requisição
$stmt = $pdo->prepare("
    SELECT ri.*, i.name, i.description, i.unit 
    FROM request_items ri 
    JOIN items i ON ri.item_id = i.id 
    WHERE ri.request_id = ?
    ORDER BY i.name
");
$stmt->execute([$requestId]);
$currentItems = $stmt->fetchAll();

// Criar array com quantidades atuais para facilitar o preenchimento
$currentQuantities = [];
foreach ($currentItems as $item) {
    $currentQuantities[$item['item_id']] = $item['quantity'];
}

// Parâmetros de pesquisa e paginação para itens disponíveis
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 15;
$offset = ($page - 1) * $itemsPerPage;

// Construir query de busca para itens disponíveis
$whereClause = '';
$params = [];

if (!empty($search)) {
    $whereClause = "WHERE name LIKE ? OR description LIKE ?";
    $params = ["%$search%", "%$search%"];
}

// Contar total de itens para paginação
$countSql = "SELECT COUNT(*) FROM items $whereClause";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalItems = $countStmt->fetchColumn();
$totalPages = ceil($totalItems / $itemsPerPage);

// Buscar itens com paginação
$sql = "SELECT * FROM items $whereClause ORDER BY name LIMIT $itemsPerPage OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$availableItems = $stmt->fetchAll();

// Processar atualização da requisição
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_request']) && $request['status'] == 'pending') {
    try {
        $pdo->beginTransaction();
        
        // Atualizar título da requisição
        $title = trim($_POST['title'] ?? '');
        if (empty($title)) {
            $title = 'Requisição #' . $request['id'];
        }
        
        // Verificar se coluna title existe e atualizar
        try {
            $stmt = $pdo->prepare("UPDATE requests SET title = ? WHERE id = ?");
            $stmt->execute([$title, $requestId]);
        } catch (PDOException $e) {
            // Se coluna title não existir, ignorar atualização do título
            if (strpos($e->getMessage(), 'title') === false) {
                throw $e;
            }
        }
        
        // Remover todos os itens atuais da requisição
        $stmt = $pdo->prepare("DELETE FROM request_items WHERE request_id = ?");
        $stmt->execute([$requestId]);
        
        // Adicionar novos itens à requisição
        $itemCount = 0;
        if (isset($_POST['items']) && is_array($_POST['items'])) {
            foreach ($_POST['items'] as $itemId => $quantity) {
                $quantity = (int)$quantity;
                if ($quantity > 0) {
                    // Verificar se o item existe
                    $checkStmt = $pdo->prepare("SELECT id FROM items WHERE id = ?");
                    $checkStmt->execute([$itemId]);
                    if ($checkStmt->fetch()) {
                        $stmt = $pdo->prepare("INSERT INTO request_items (request_id, item_id, quantity) VALUES (?, ?, ?)");
                        $stmt->execute([$requestId, $itemId, $quantity]);
                        $itemCount++;
                    }
                }
            }
        }
        
        if ($itemCount == 0) {
            throw new Exception("Nenhum item selecionado ou itens inválidos");
        }
        
        $pdo->commit();
        $success = "Requisição atualizada com sucesso! $itemCount item(ns) selecionado(s).";
        
        // Recarregar itens atuais
        $stmt = $pdo->prepare("
            SELECT ri.*, i.name, i.description, i.unit 
            FROM request_items ri 
            JOIN items i ON ri.item_id = i.id 
            WHERE ri.request_id = ?
            ORDER BY i.name
        ");
        $stmt->execute([$requestId]);
        $currentItems = $stmt->fetchAll();
        
        // Atualizar array de quantidades
        $currentQuantities = [];
        foreach ($currentItems as $item) {
            $currentQuantities[$item['item_id']] = $item['quantity'];
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "Erro ao atualizar requisição: " . $e->getMessage();
    }
}

// Função para destacar termos de pesquisa
function highlightSearchEdit($text, $search) {
    if (empty($search) || empty($text)) {
        return htmlspecialchars($text);
    }
    
    $highlighted = htmlspecialchars($text);
    $searchTerms = explode(' ', $search);
    
    foreach ($searchTerms as $term) {
        if (strlen(trim($term)) > 2) {
            $highlighted = preg_replace(
                '/(' . preg_quote(trim($term), '/') . ')/i',
                '<mark>$1</mark>',
                $highlighted
            );
        }
    }
    
    return $highlighted;
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Editar: <?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?> - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        .current-items {
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        .quantity-input {
            width: 100px !important;
        }
        .table-hover tbody tr:hover {
            background-color: #f8f9fa;
        }
        .item-row {
            transition: background-color 0.2s;
        }
        .item-row.selected {
            background-color: #e3f2fd !important;
        }
        mark {
            background-color: #ffeb3b;
            padding: 1px 2px;
            border-radius: 2px;
            font-weight: bold;
        }
        .readonly-mode {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h2>✏️ Editar: <?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></h2>
                <small class="text-muted">Editando Requisição #<?php echo $request['id']; ?></small>
            </div>
            <div class="col-md-4 text-right">
                <a href="<?php echo $_SESSION['role'] == 'admin' ? 'manage_requests.php' : 'my_requests.php'; ?>" class="btn btn-secondary">⬅ Voltar</a>
                <a href="view_request.php?id=<?php echo $request['id']; ?>" class="btn btn-info">👁️ Visualizar</a>
            </div>
        </div>
        
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        <?php endif; ?>
        
        <?php if ($request['status'] != 'pending'): ?>
            <div class="alert alert-warning">
                <h5>⚠️ Requisição Não Editável</h5>
                <p>Esta requisição está com status "<strong><?php echo ucfirst($request['status']); ?></strong>" e não pode ser editada.</p>
                <p>Apenas requisições com status "Pendente" podem ser modificadas.</p>
            </div>
            
            <!-- Modo somente leitura -->
            <div class="readonly-mode">
                <h5>📋 Itens Atuais da Requisição</h5>
                <?php if (empty($currentItems)): ?>
                    <p class="text-muted">Nenhum item encontrado.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Descrição</th>
                                    <th>Quantidade</th>
                                    <th>Unidade</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($currentItems as $item): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($item['name']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($item['description']); ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td><?php echo htmlspecialchars($item['unit']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Itens Atuais da Requisição -->
            <?php if (!empty($currentItems)): ?>
            <div class="card mb-4 current-items">
                <div class="card-header">
                    <h5>📋 Itens Atuais da Requisição</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Descrição</th>
                                    <th>Quantidade</th>
                                    <th>Unidade</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($currentItems as $item): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($item['name']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($item['description']); ?></td>
                                    <td><span class="badge badge-primary"><?php echo $item['quantity']; ?></span></td>
                                    <td><?php echo htmlspecialchars($item['unit']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <small class="text-muted">
                        💡 <strong>Dica:</strong> Os itens acima serão substituídos pelos novos itens selecionados abaixo.
                    </small>
                </div>
            </div>
            <?php endif; ?>

            <!-- Formulário de Edição -->
            <form method="post">
                <!-- Campo para nome da requisição -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>📝 Informações da Requisição</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="title">Nome da Requisição</label>
                            <input type="text" name="title" id="title" class="form-control"
                                   value="<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?>"
                                   placeholder="Ex: Estoque Semanal, Festa Junina, Ingredientes para Pizza..."
                                   maxlength="255">
                            <small class="text-muted">
                                Deixe em branco para manter: "Requisição #<?php echo $request['id']; ?>"
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Campo de Pesquisa -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>🔍 Pesquisar e Selecionar Produtos</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <input type="text" name="search_field" class="form-control"
                                       placeholder="Digite o nome ou descrição do produto..."
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       onchange="searchProducts(this.value)">
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-primary btn-block" onclick="searchProducts()">
                                    🔍 Pesquisar
                                </button>
                            </div>
                        </div>
                        <?php if (!empty($search)): ?>
                        <div class="mt-2">
                            <small class="text-muted">
                                Pesquisando por: "<strong><?php echo htmlspecialchars($search); ?></strong>"
                                - <?php echo $totalItems; ?> produto(s) encontrado(s)
                            </small>
                            <a href="edit_request.php?id=<?php echo $requestId; ?>" class="btn btn-sm btn-outline-secondary ml-2">
                                ✖ Limpar Pesquisa
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <input type="hidden" name="update_request" value="1">

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th width="25%">Item</th>
                                <th width="35%">Descrição</th>
                                <th width="15%">Unidade</th>
                                <th width="25%">Quantidade</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($availableItems)): ?>
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <?php if (!empty($search)): ?>
                                        🔍 Nenhum produto encontrado para "<strong><?php echo htmlspecialchars($search); ?></strong>"
                                        <br><small>Tente pesquisar com outros termos</small>
                                    <?php else: ?>
                                        📦 Nenhum produto cadastrado no sistema
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php else: ?>
                                <?php foreach ($availableItems as $item): ?>
                                <tr class="item-row" data-item-id="<?php echo $item['id']; ?>">
                                    <td>
                                        <strong><?php echo highlightSearchEdit($item['name'], $search); ?></strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo highlightSearchEdit($item['description'], $search); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">
                                            <?php echo htmlspecialchars($item['unit']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <input type="number" name="items[<?php echo $item['id']; ?>]"
                                               class="form-control form-control-sm quantity-input"
                                               min="0" value="<?php echo $currentQuantities[$item['id']] ?? 0; ?>"
                                               onchange="updateRowHighlight(this)">
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <?php if (!empty($availableItems)): ?>
                <!-- Controles de Paginação -->
                <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center mb-4">
                    <nav aria-label="Navegação de páginas">
                        <ul class="pagination">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?id=<?php echo $requestId; ?>&page=1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    ⏮ Primeira
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?id=<?php echo $requestId; ?>&page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    ⏪ Anterior
                                </a>
                            </li>
                            <?php endif; ?>

                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);
                            for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?id=<?php echo $requestId; ?>&page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?id=<?php echo $requestId; ?>&page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    Próxima ⏩
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?id=<?php echo $requestId; ?>&page=<?php echo $totalPages; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    Última ⏭
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>

                <!-- Botão de Atualização -->
                <div class="text-center">
                    <button type="submit" class="btn btn-success btn-lg">
                        💾 Atualizar Requisição
                    </button>
                    <a href="<?php echo $_SESSION['role'] == 'admin' ? 'manage_requests.php' : 'my_requests.php'; ?>" class="btn btn-outline-secondary btn-lg ml-2">
                        ❌ Cancelar
                    </a>
                </div>
                <?php endif; ?>
            </form>
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateRowHighlight(input) {
            const row = input.closest('tr');
            const quantity = parseInt(input.value) || 0;

            if (quantity > 0) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
            updateSelectedCount();
        }

        function updateSelectedCount() {
            const selectedInputs = document.querySelectorAll('.quantity-input');
            let selectedCount = 0;
            let totalQuantity = 0;

            selectedInputs.forEach(input => {
                const quantity = parseInt(input.value) || 0;
                if (quantity > 0) {
                    selectedCount++;
                    totalQuantity += quantity;
                }
            });

            const counter = document.getElementById('selected-counter');
            if (counter) {
                counter.textContent = `${selectedCount} item(ns) selecionado(s) - Total: ${totalQuantity}`;
            }
        }

        function searchProducts(searchTerm) {
            const currentSearch = searchTerm || document.querySelector('input[name="search_field"]').value;
            if (currentSearch.trim()) {
                window.location.href = `?id=<?php echo $requestId; ?>&search=${encodeURIComponent(currentSearch)}`;
            } else {
                window.location.href = `?id=<?php echo $requestId; ?>`;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form[method="post"]');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const selectedInputs = document.querySelectorAll('.quantity-input');
                    let hasSelection = false;

                    selectedInputs.forEach(input => {
                        if (parseInt(input.value) > 0) {
                            hasSelection = true;
                        }
                    });

                    if (!hasSelection) {
                        alert('⚠️ Selecione pelo menos um item para atualizar a requisição!');
                        e.preventDefault();
                        return;
                    }

                    if (!confirm('💾 Confirma a atualização da requisição com os itens selecionados?')) {
                        e.preventDefault();
                    }
                });
            }

            // Adicionar contador
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton) {
                const counter = document.createElement('div');
                counter.id = 'selected-counter';
                counter.className = 'text-center text-muted mt-2';
                submitButton.parentNode.insertBefore(counter, submitButton);
            }

            // Destacar linhas já preenchidas
            document.querySelectorAll('.quantity-input').forEach(input => {
                updateRowHighlight(input);
            });

            // Enter para pesquisar
            document.querySelector('input[name="search_field"]')?.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchProducts();
                }
            });
        });
    </script>
</body>
</html>
