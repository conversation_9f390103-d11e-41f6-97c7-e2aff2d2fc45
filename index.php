<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

// Buscar estatísticas para o dashboard
$userStats = [];
try {
    // Estatísticas do usuário atual
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_requests,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
            SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_requests
        FROM requests
        WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $userStats = $stmt->fetch() ?: ['total_requests' => 0, 'pending_requests' => 0, 'approved_requests' => 0, 'delivered_requests' => 0];

    // Estatísticas gerais (apenas para admin)
    $systemStats = [];
    if ($_SESSION['role'] == 'admin') {
        $stmt = $pdo->query("
            SELECT
                (SELECT COUNT(*) FROM requests) as total_system_requests,
                (SELECT COUNT(*) FROM users) as total_users,
                (SELECT COUNT(*) FROM items) as total_items,
                (SELECT COUNT(*) FROM requests WHERE status = 'pending') as pending_system_requests
        ");
        $systemStats = $stmt->fetch() ?: [];
    }
} catch (PDOException $e) {
    $userStats = ['total_requests' => 0, 'pending_requests' => 0, 'approved_requests' => 0, 'delivered_requests' => 0];
    $systemStats = [];
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <?php include 'includes/seo_meta.php'; ?>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .dashboard-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
            border-radius: 10px;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .welcome-section {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .quick-actions {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <main class="container mt-4" role="main">
        <!-- Seção de Boas-vindas -->
        <header class="welcome-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-3">
                        <i class="fas fa-home" aria-hidden="true"></i>
                        Bem-vindo, <?php echo htmlspecialchars($_SESSION['username'] ?? 'Usuário'); ?>!
                    </h1>
                    <p class="lead mb-0">
                        Formulário de solicitação de insumos para produção -
                        <?php echo $_SESSION['role'] == 'admin' ? 'Painel Administrativo' : 'Painel do Funcionário'; ?>
                    </p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-utensils fa-4x opacity-50" aria-hidden="true"></i>
                </div>
            </div>
        </header>

        <!-- Estatísticas do Usuário -->
        <section aria-labelledby="user-stats-title">
            <h2 id="user-stats-title" class="mb-4">
                <i class="fas fa-chart-bar text-primary" aria-hidden="true"></i>
                Suas Estatísticas
            </h2>
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card bg-primary text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-clipboard-list stat-icon" aria-hidden="true"></i>
                            <h3 class="card-title"><?php echo $userStats['total_requests']; ?></h3>
                            <p class="card-text">Total de Requisições</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card bg-warning text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-clock stat-icon" aria-hidden="true"></i>
                            <h3 class="card-title"><?php echo $userStats['pending_requests']; ?></h3>
                            <p class="card-text">Pendentes</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card bg-success text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle stat-icon" aria-hidden="true"></i>
                            <h3 class="card-title"><?php echo $userStats['approved_requests']; ?></h3>
                            <p class="card-text">Aprovadas</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card bg-info text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-truck stat-icon" aria-hidden="true"></i>
                            <h3 class="card-title"><?php echo $userStats['delivered_requests']; ?></h3>
                            <p class="card-text">Entregues</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <?php if ($_SESSION['role'] == 'admin' && !empty($systemStats)): ?>
        <!-- Estatísticas do Sistema (apenas admin) -->
        <section aria-labelledby="system-stats-title">
            <h2 id="system-stats-title" class="mb-4">
                <i class="fas fa-cogs text-success" aria-hidden="true"></i>
                Estatísticas do Sistema
            </h2>
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card bg-dark text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-clipboard-list stat-icon" aria-hidden="true"></i>
                            <h3 class="card-title"><?php echo $systemStats['total_system_requests']; ?></h3>
                            <p class="card-text">Total Requisições</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card bg-secondary text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-users stat-icon" aria-hidden="true"></i>
                            <h3 class="card-title"><?php echo $systemStats['total_users']; ?></h3>
                            <p class="card-text">Usuários Cadastrados</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card bg-info text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-boxes stat-icon" aria-hidden="true"></i>
                            <h3 class="card-title"><?php echo $systemStats['total_items']; ?></h3>
                            <p class="card-text">Itens Cadastrados</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card bg-warning text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle stat-icon" aria-hidden="true"></i>
                            <h3 class="card-title"><?php echo $systemStats['pending_system_requests']; ?></h3>
                            <p class="card-text">Aguardando Aprovação</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Ações Rápidas -->
        <section aria-labelledby="quick-actions-title" class="quick-actions">
            <h2 id="quick-actions-title" class="mb-4">
                <i class="fas fa-bolt text-warning" aria-hidden="true"></i>
                Ações Rápidas
            </h2>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <a href="request_form.php" class="btn btn-primary btn-lg btn-block h-100 d-flex align-items-center justify-content-center text-decoration-none">
                        <div class="text-center">
                            <i class="fas fa-plus-circle fa-2x mb-2" aria-hidden="true"></i>
                            <br>Nova Requisição
                        </div>
                    </a>
                </div>
                <div class="col-md-4 mb-3">
                    <a href="my_requests.php" class="btn btn-info btn-lg btn-block h-100 d-flex align-items-center justify-content-center text-decoration-none">
                        <div class="text-center">
                            <i class="fas fa-list fa-2x mb-2" aria-hidden="true"></i>
                            <br>Minhas Requisições
                        </div>
                    </a>
                </div>
                <?php if ($_SESSION['role'] == 'admin'): ?>
                <div class="col-md-4 mb-3">
                    <a href="manage_requests.php" class="btn btn-success btn-lg btn-block h-100 d-flex align-items-center justify-content-center text-decoration-none">
                        <div class="text-center">
                            <i class="fas fa-tasks fa-2x mb-2" aria-hidden="true"></i>
                            <br>Gerenciar Requisições
                        </div>
                    </a>
                </div>
                <?php else: ?>
                <div class="col-md-4 mb-3">
                    <div class="card dashboard-card bg-light h-100">
                        <div class="card-body text-center d-flex align-items-center justify-content-center">
                            <div>
                                <i class="fas fa-info-circle fa-2x text-muted mb-2" aria-hidden="true"></i>
                                <p class="text-muted mb-0">Use o menu acima para navegar</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </section>

        <!-- Dicas e Ajuda -->
        <section aria-labelledby="help-section-title" class="mt-5">
            <h2 id="help-section-title" class="mb-4">
                <i class="fas fa-question-circle text-info" aria-hidden="true"></i>
                Dicas de Uso
            </h2>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card dashboard-card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-lightbulb text-warning" aria-hidden="true"></i>
                                Como Criar uma Requisição
                            </h5>
                            <p class="card-text">
                                1. Clique em "Nova Requisição"<br>
                                2. Dê um nome descritivo à sua requisição<br>
                                3. Pesquise e selecione os itens desejados<br>
                                4. Defina as quantidades necessárias<br>
                                5. Envie para aprovação
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card dashboard-card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-edit text-primary" aria-hidden="true"></i>
                                Editando Requisições
                            </h5>
                            <p class="card-text">
                                • Apenas requisições <strong>pendentes</strong> podem ser editadas<br>
                                • Você pode alterar o nome e os itens<br>
                                • Use a pesquisa para encontrar novos produtos<br>
                                • Salve as alterações quando terminar
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="mt-5 py-4 bg-light text-center" role="contentinfo">
        <div class="container">
            <p class="text-muted mb-0">
                <i class="fas fa-utensils" aria-hidden="true"></i>
                Sistema de Requisição de Material de Cozinha &copy; <?php echo date('Y'); ?>
            </p>
            <small class="text-muted">
                Versão 2.0 - Desenvolvido para otimizar o controle de materiais
            </small>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Schema.org para página inicial -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Dashboard - Sistema de Requisição",
        "description": "Painel principal do sistema de requisição de material de cozinha",
        "url": "<?php echo 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>",
        "mainEntity": {
            "@type": "SoftwareApplication",
            "name": "Sistema de Requisição de Material de Cozinha",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web Browser"
        }
    }
    </script>
</body>
</html>