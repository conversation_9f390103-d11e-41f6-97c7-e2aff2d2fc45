<?php
// Iniciar sessão se não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Incluir configuração de página
require_once 'includes/page_config.php';

// Aplicar configuração automática da página
initPage();

// Incluir layout
require_once 'includes/layout.php';

// Incluir conexão com banco
require_once 'config/db_connect.php';

// Buscar estatísticas para o dashboard
$userStats = [];
try {
    // Estatísticas do usuário atual
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_requests,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
            SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_requests
        FROM requests
        WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $userStats = $stmt->fetch() ?: ['total_requests' => 0, 'pending_requests' => 0, 'approved_requests' => 0, 'delivered_requests' => 0];

    // Estatísticas gerais (apenas para admin)
    $systemStats = [];
    if ($_SESSION['role'] == 'admin') {
        $stmt = $pdo->query("
            SELECT
                (SELECT COUNT(*) FROM requests) as total_system_requests,
                (SELECT COUNT(*) FROM users) as total_users,
                (SELECT COUNT(*) FROM items) as total_items,
                (SELECT COUNT(*) FROM requests WHERE status = 'pending') as pending_system_requests
        ");
        $systemStats = $stmt->fetch() ?: [];
    }
} catch (PDOException $e) {
    $userStats = ['total_requests' => 0, 'pending_requests' => 0, 'approved_requests' => 0, 'delivered_requests' => 0];
    $systemStats = [];
}
?>

<!-- Seção de Boas-vindas -->
<div class="content-card">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-3">
                <i class="fas fa-home text-primary" aria-hidden="true"></i>
                Bem-vindo, <?php echo htmlspecialchars($_SESSION['username'] ?? 'Usuário'); ?>!
            </h2>
            <p class="lead mb-0 text-muted">
                Formulário de solicitação de insumos para produção -
                <?php echo $_SESSION['role'] == 'admin' ? 'Painel Administrativo' : 'Painel do Funcionário'; ?>
            </p>
        </div>
        <div class="col-md-4 text-center">
            <i class="fas fa-utensils fa-4x text-primary opacity-25" aria-hidden="true"></i>
        </div>
    </div>
</div>

<!-- Estatísticas do Usuário -->
<div class="content-card">
    <h3 class="mb-4">
        <i class="fas fa-chart-bar text-primary" aria-hidden="true"></i>
        Suas Estatísticas
    </h3>
    <div class="row">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white h-100 border-0">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-list fa-2x mb-3" aria-hidden="true"></i>
                    <h4 class="card-title"><?php echo $userStats['total_requests']; ?></h4>
                    <p class="card-text">Total de Requisições</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white h-100 border-0">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-3" aria-hidden="true"></i>
                    <h4 class="card-title"><?php echo $userStats['pending_requests']; ?></h4>
                    <p class="card-text">Pendentes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white h-100 border-0">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-3" aria-hidden="true"></i>
                    <h4 class="card-title"><?php echo $userStats['approved_requests']; ?></h4>
                    <p class="card-text">Aprovadas</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100 border-0">
                <div class="card-body text-center">
                    <i class="fas fa-truck fa-2x mb-3" aria-hidden="true"></i>
                    <h4 class="card-title"><?php echo $userStats['delivered_requests']; ?></h4>
                    <p class="card-text">Entregues</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($_SESSION['role'] == 'admin' && !empty($systemStats)): ?>
<!-- Estatísticas do Sistema (apenas admin) -->
<div class="content-card">
    <h3 class="mb-4">
        <i class="fas fa-cogs text-success" aria-hidden="true"></i>
        Estatísticas do Sistema
    </h3>
    <div class="row">
        <div class="col-md-3 mb-3">
            <div class="card bg-dark text-white h-100 border-0">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-list fa-2x mb-3" aria-hidden="true"></i>
                    <h4 class="card-title"><?php echo $systemStats['total_system_requests']; ?></h4>
                    <p class="card-text">Total Requisições</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-secondary text-white h-100 border-0">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-3" aria-hidden="true"></i>
                    <h4 class="card-title"><?php echo $systemStats['total_users']; ?></h4>
                    <p class="card-text">Usuários Cadastrados</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100 border-0">
                <div class="card-body text-center">
                    <i class="fas fa-boxes fa-2x mb-3" aria-hidden="true"></i>
                    <h4 class="card-title"><?php echo $systemStats['total_items']; ?></h4>
                    <p class="card-text">Itens Cadastrados</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white h-100 border-0">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3" aria-hidden="true"></i>
                    <h4 class="card-title"><?php echo $systemStats['pending_system_requests']; ?></h4>
                    <p class="card-text">Aguardando Aprovação</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Ações Rápidas -->
<div class="content-card">
    <h3 class="mb-4">
        <i class="fas fa-bolt text-warning" aria-hidden="true"></i>
        Ações Rápidas
    </h3>
    <div class="row">
        <div class="col-md-4 mb-3">
            <a href="nova-requisicao" class="btn btn-primary btn-custom btn-lg w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                <div class="text-center">
                    <i class="fas fa-plus-circle fa-2x mb-2" aria-hidden="true"></i>
                    <br>Nova Requisição
                </div>
            </a>
        </div>
        <div class="col-md-4 mb-3">
            <a href="minhas-requisicoes" class="btn btn-info btn-custom btn-lg w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                <div class="text-center">
                    <i class="fas fa-list fa-2x mb-2" aria-hidden="true"></i>
                    <br>Minhas Requisições
                </div>
            </a>
        </div>
        <?php if ($_SESSION['role'] == 'admin'): ?>
        <div class="col-md-4 mb-3">
            <a href="gerenciar-requisicoes" class="btn btn-success btn-custom btn-lg w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                <div class="text-center">
                    <i class="fas fa-tasks fa-2x mb-2" aria-hidden="true"></i>
                    <br>Gerenciar Requisições
                </div>
            </a>
        </div>
        <?php else: ?>
        <div class="col-md-4 mb-3">
            <div class="card bg-light h-100 border-0">
                <div class="card-body text-center d-flex align-items-center justify-content-center">
                    <div>
                        <i class="fas fa-info-circle fa-2x text-muted mb-2" aria-hidden="true"></i>
                        <p class="text-muted mb-0">Use o menu lateral para navegar</p>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Dicas e Ajuda -->
<div class="content-card">
    <h3 class="mb-4">
        <i class="fas fa-question-circle text-info" aria-hidden="true"></i>
        Dicas de Uso
    </h3>
    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="card h-100 border-0 bg-light">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-lightbulb text-warning" aria-hidden="true"></i>
                        Como Criar uma Requisição
                    </h5>
                    <p class="card-text">
                        1. Clique em "Nova Requisição"<br>
                        2. Dê um nome descritivo à sua requisição<br>
                        3. Pesquise e selecione os itens desejados<br>
                        4. Defina as quantidades necessárias<br>
                        5. Envie para aprovação
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card h-100 border-0 bg-light">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-edit text-primary" aria-hidden="true"></i>
                        Editando Requisições
                    </h5>
                    <p class="card-text">
                        • Apenas requisições <strong>pendentes</strong> podem ser editadas<br>
                        • Você pode alterar o nome e os itens<br>
                        • Use a pesquisa para encontrar novos produtos<br>
                        • Salve as alterações quando terminar
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Scripts específicos da página
$page_scripts = '
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Dashboard - Sistema de Requisição",
    "description": "Painel principal do sistema de requisição de material de cozinha",
    "url": "' . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . '",
    "mainEntity": {
        "@type": "SoftwareApplication",
        "name": "Sistema de Requisição de Material de Cozinha",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web Browser"
    }
}
</script>
';

// Incluir footer do layout
require_once 'includes/layout_footer.php';
?>