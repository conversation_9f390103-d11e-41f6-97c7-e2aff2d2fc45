<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';
require_once 'includes/barcode_generator.php';

$message = '';
$error = '';

// Capturar mensagem de sucesso da URL
if (isset($_GET['success'])) {
    $message = $_GET['success'];
}

// Processar ações
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $quantity = (int)($_POST['quantity'] ?? 0);
                $unit = trim($_POST['unit'] ?? '');
                $category = trim($_POST['category'] ?? '');
                $supplier = trim($_POST['supplier'] ?? '');
                $minStock = (int)($_POST['min_stock'] ?? 0);
                $currentStock = (int)($_POST['current_stock'] ?? 0);
                $costPrice = (float)($_POST['cost_price'] ?? 0);
                $barcode = trim($_POST['barcode'] ?? '');

                if (empty($name) || empty($unit)) {
                    $error = 'Nome e unidade são obrigatórios';
                } else {
                    // Verificar se já existe um item com o mesmo nome
                    $checkStmt = $pdo->prepare("SELECT id, name FROM items WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))");
                    $checkStmt->execute([$name]);
                    $existingItem = $checkStmt->fetch();

                    if ($existingItem) {
                        $error = "Já existe um item com o nome \"" . htmlspecialchars($existingItem['name']) . "\" (ID: " . $existingItem['id'] . "). Por favor, escolha um nome diferente.";
                    } else {
                    try {
                        // Gerar código interno
                        $internalCode = generateItemInternalCode($pdo);

                        // Gerar código de barras se não fornecido
                        if (empty($barcode)) {
                            $barcode = generateItemBarcode(0); // Será atualizado após inserção
                        }

                        // Tentar inserir com todos os campos novos
                        try {
                            $stmt = $pdo->prepare("
                                INSERT INTO items (name, description, quantity, unit, internal_code, barcode,
                                                 category, supplier, min_stock, current_stock, cost_price)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ");
                            $stmt->execute([$name, $description, $quantity, $unit, $internalCode, $barcode,
                                          $category, $supplier, $minStock, $currentStock, $costPrice]);
                            $itemId = $pdo->lastInsertId();

                            // Atualizar código de barras com ID real se foi gerado automaticamente
                            if (empty($_POST['barcode'])) {
                                $finalBarcode = generateItemBarcode($itemId);
                                $updateStmt = $pdo->prepare("UPDATE items SET barcode = ? WHERE id = ?");
                                $updateStmt->execute([$finalBarcode, $itemId]);
                            }

                            $message = "Item adicionado com sucesso! Código interno: $internalCode";

                            // Redirecionar para limpar o formulário após sucesso
                            header("Location: manage_items.php?success=" . urlencode($message));
                            exit;

                        } catch (PDOException $e) {
                            // Fallback para versão antiga do banco
                            $stmt = $pdo->prepare("INSERT INTO items (name, description, quantity, unit) VALUES (?, ?, ?, ?)");
                            if ($stmt->execute([$name, $description, $quantity, $unit])) {
                                $message = 'Item adicionado com sucesso! (Versão básica - atualize o banco para recursos completos)';

                                // Redirecionar para limpar o formulário após sucesso
                                header("Location: manage_items.php?success=" . urlencode($message));
                                exit;
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao adicionar item: ' . $e->getMessage();
                    }
                    } // Fechar o else da verificação de duplicatas
                }
                break;
                
            case 'update':
                $id = (int)($_POST['id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $quantity = (int)($_POST['quantity'] ?? 0);
                $unit = trim($_POST['unit'] ?? '');
                $category = trim($_POST['category'] ?? '');
                $supplier = trim($_POST['supplier'] ?? '');
                $minStock = (int)($_POST['min_stock'] ?? 0);
                $currentStock = (int)($_POST['current_stock'] ?? 0);
                $costPrice = (float)($_POST['cost_price'] ?? 0);
                $barcode = trim($_POST['barcode'] ?? '');

                if (empty($name) || empty($unit) || $id <= 0) {
                    $error = 'Dados inválidos para atualização';
                } else {
                    // Verificar se já existe outro item com o mesmo nome (excluindo o item atual)
                    $checkStmt = $pdo->prepare("SELECT id, name FROM items WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND id != ?");
                    $checkStmt->execute([$name, $id]);
                    $existingItem = $checkStmt->fetch();

                    if ($existingItem) {
                        $error = "Já existe outro item com o nome \"" . htmlspecialchars($existingItem['name']) . "\" (ID: " . $existingItem['id'] . "). Por favor, escolha um nome diferente.";
                    } else {
                    try {
                        // Tentar atualizar com todos os campos
                        try {
                            $stmt = $pdo->prepare("
                                UPDATE items SET name = ?, description = ?, quantity = ?, unit = ?,
                                               barcode = ?, category = ?, supplier = ?, min_stock = ?,
                                               current_stock = ?, cost_price = ?
                                WHERE id = ?
                            ");
                            if ($stmt->execute([$name, $description, $quantity, $unit, $barcode,
                                              $category, $supplier, $minStock, $currentStock, $costPrice, $id])) {
                                $message = 'Item atualizado com sucesso!';
                            }
                        } catch (PDOException $e) {
                            // Fallback para versão antiga
                            $stmt = $pdo->prepare("UPDATE items SET name = ?, description = ?, quantity = ?, unit = ? WHERE id = ?");
                            if ($stmt->execute([$name, $description, $quantity, $unit, $id])) {
                                $message = 'Item atualizado com sucesso! (Versão básica)';
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao atualizar item: ' . $e->getMessage();
                    }
                    } // Fechar o else da verificação de duplicatas
                }
                break;
                
            case 'delete':
                $id = (int)($_POST['id'] ?? 0);
                if ($id > 0) {
                    try {
                        // Verificar se o item está sendo usado em requisições
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM request_items WHERE item_id = ?");
                        $stmt->execute([$id]);
                        $count = $stmt->fetchColumn();
                        
                        if ($count > 0) {
                            $error = 'Não é possível excluir este item pois ele está sendo usado em requisições';
                        } else {
                            $stmt = $pdo->prepare("DELETE FROM items WHERE id = ?");
                            if ($stmt->execute([$id])) {
                                $message = 'Item excluído com sucesso!';
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao excluir item: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Buscar todos os itens
$stmt = $pdo->query("SELECT * FROM items ORDER BY name");
$items = $stmt->fetchAll();

// Buscar item para edição se solicitado (mas não se acabou de adicionar um item)
$editItem = null;
if (isset($_GET['edit']) && !isset($_GET['success'])) {
    $editId = (int)$_GET['edit'];
    $stmt = $pdo->prepare("SELECT * FROM items WHERE id = ?");
    $stmt->execute([$editId]);
    $editItem = $stmt->fetch();
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <title>Gerenciar Itens - Sistema de Requisição de Material de Cozinha</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .font-monospace { font-family: 'Courier New', monospace; }
        .btn-xs { padding: 0.125rem 0.25rem; font-size: 0.75rem; }
        .table th { border-top: none; }
        .card-header h6 { margin-bottom: 0; }
        .barcode-display {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            letter-spacing: 2px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        /* Estilos para validação de nome */
        .name-validation-container {
            position: relative;
        }

        #name-validation {
            animation: fadeIn 0.3s ease-in;
        }

        #name-suggestions {
            animation: slideDown 0.3s ease-in;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
        }

        .input-with-validation {
            transition: border-color 0.3s ease;
        }

        .input-with-validation.checking {
            border-color: #17a2b8 !important;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }

        .input-with-validation.valid {
            border-color: #28a745 !important;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .input-with-validation.invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .suggestion-btn {
            transition: all 0.2s ease;
        }

        .suggestion-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .duplicate-alert {
            background: linear-gradient(45deg, #fff3cd, #ffeaa7);
            border-left: 4px solid #ffc107;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <h2>Gerenciar Itens</h2>
        
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                <strong>Sucesso!</strong> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle"></i>
                <strong>Pronto para o próximo!</strong> Os campos foram limpos automaticamente. Você pode adicionar um novo item agora.
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Formulário para adicionar/editar item -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="row">
                    <div class="col-md-8">
                        <h5>
                            <?php if ($editItem): ?>
                                <i class="fas fa-edit text-warning"></i>
                                Editar Item: <?php echo htmlspecialchars($editItem['name']); ?>
                            <?php else: ?>
                                <i class="fas fa-plus text-success"></i>
                                Adicionar Novo Item
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="col-md-4 text-right">
                        <?php if ($editItem): ?>
                            <a href="manage_items.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus"></i> Novo Item
                            </a>
                            <a href="manage_items.php" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i> Cancelar
                            </a>
                        <?php else: ?>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Preencha os campos abaixo
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?php echo $editItem ? 'update' : 'add'; ?>">
                    <?php if ($editItem): ?>
                        <input type="hidden" name="id" value="<?php echo $editItem['id']; ?>">
                    <?php endif; ?>
                    
                    <!-- Informações Básicas -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group name-validation-container">
                                <label>
                                    <i class="fas fa-tag text-primary"></i>
                                    Nome do Item *
                                </label>
                                <input type="text" name="name" class="form-control input-with-validation"
                                       value="<?php echo $editItem ? htmlspecialchars($editItem['name']) : ''; ?>"
                                       placeholder="Ex: Açúcar Cristal, Farinha de Trigo..."
                                       required
                                       autocomplete="off"
                                       data-toggle="tooltip"
                                       data-placement="top"
                                       title="O nome será verificado automaticamente para evitar duplicatas">
                                <div id="name-validation"></div>
                                <div id="name-suggestions"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>
                                    <i class="fas fa-layer-group text-info"></i>
                                    Categoria
                                </label>
                                <input type="text" name="category" class="form-control"
                                       value="<?php echo $editItem ? htmlspecialchars($editItem['category'] ?? '') : ''; ?>"
                                       placeholder="Ex: Açúcar, Farinha, Temperos...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>
                                    <i class="fas fa-balance-scale text-secondary"></i>
                                    Unidade *
                                </label>
                                <select name="unit" class="form-control" required>
                                    <option value="">Selecione...</option>
                                    <option value="kg" <?php echo ($editItem && $editItem['unit'] == 'kg') ? 'selected' : ''; ?>>kg (quilograma)</option>
                                    <option value="g" <?php echo ($editItem && $editItem['unit'] == 'g') ? 'selected' : ''; ?>>g (grama)</option>
                                    <option value="l" <?php echo ($editItem && $editItem['unit'] == 'l') ? 'selected' : ''; ?>>l (litro)</option>
                                    <option value="ml" <?php echo ($editItem && $editItem['unit'] == 'ml') ? 'selected' : ''; ?>>ml (mililitro)</option>
                                    <option value="unid" <?php echo ($editItem && $editItem['unit'] == 'unid') ? 'selected' : ''; ?>>unid (unidade)</option>
                                    <option value="pct" <?php echo ($editItem && $editItem['unit'] == 'pct') ? 'selected' : ''; ?>>pct (pacote)</option>
                                    <option value="cx" <?php echo ($editItem && $editItem['unit'] == 'cx') ? 'selected' : ''; ?>>cx (caixa)</option>
                                    <option value="sc" <?php echo ($editItem && $editItem['unit'] == 'sc') ? 'selected' : ''; ?>>sc (saco)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class="fas fa-align-left text-muted"></i>
                            Descrição Detalhada
                        </label>
                        <textarea name="description" class="form-control" rows="2"
                                  placeholder="Descrição completa do produto, marca, especificações..."><?php echo $editItem ? htmlspecialchars($editItem['description']) : ''; ?></textarea>
                    </div>

                    <!-- Códigos e Identificação -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-barcode text-dark"></i>
                                Códigos e Identificação
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-hashtag text-primary"></i>
                                            Código Interno
                                        </label>
                                        <input type="text" class="form-control"
                                               value="<?php echo $editItem ? htmlspecialchars($editItem['internal_code'] ?? 'Será gerado automaticamente') : 'Será gerado automaticamente'; ?>"
                                               readonly>
                                        <small class="text-muted">Gerado automaticamente (ex: ITEM00001)</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-barcode text-dark"></i>
                                            Código de Barras
                                        </label>
                                        <input type="text" name="barcode" class="form-control"
                                               value="<?php echo $editItem ? htmlspecialchars($editItem['barcode'] ?? '') : ''; ?>"
                                               placeholder="Deixe vazio para gerar automaticamente">
                                        <small class="text-muted">Opcional - será gerado se vazio</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Estoque e Fornecedor -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-warehouse text-success"></i>
                                Controle de Estoque
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-boxes text-warning"></i>
                                            Estoque Atual
                                        </label>
                                        <input type="number" name="current_stock" class="form-control" min="0" step="0.01"
                                               value="<?php echo $editItem ? ($editItem['current_stock'] ?? '0') : '0'; ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-exclamation-triangle text-danger"></i>
                                            Estoque Mínimo
                                        </label>
                                        <input type="number" name="min_stock" class="form-control" min="0" step="0.01"
                                               value="<?php echo $editItem ? ($editItem['min_stock'] ?? '0') : '0'; ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-dollar-sign text-success"></i>
                                            Preço de Custo
                                        </label>
                                        <input type="number" name="cost_price" class="form-control" min="0" step="0.01"
                                               value="<?php echo $editItem ? ($editItem['cost_price'] ?? '0.00') : '0.00'; ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-archive text-info"></i>
                                            Quantidade (Legado)
                                        </label>
                                        <input type="number" name="quantity" class="form-control" min="0"
                                               value="<?php echo $editItem ? $editItem['quantity'] : '0'; ?>">
                                        <small class="text-muted">Campo antigo - use Estoque Atual</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>
                                    <i class="fas fa-truck text-primary"></i>
                                    Fornecedor Principal
                                </label>
                                <input type="text" name="supplier" class="form-control"
                                       value="<?php echo $editItem ? htmlspecialchars($editItem['supplier'] ?? '') : ''; ?>"
                                       placeholder="Nome do fornecedor principal">
                            </div>
                        </div>
                    </div>
                    
                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <?php if ($editItem): ?>
                                <i class="fas fa-save"></i> Atualizar Item
                            <?php else: ?>
                                <i class="fas fa-plus"></i> Adicionar Item
                            <?php endif; ?>
                        </button>

                        <?php if ($editItem): ?>
                            <a href="manage_items.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Cancelar
                            </a>
                        <?php else: ?>
                            <button type="button" class="btn btn-outline-secondary btn-lg" onclick="clearForm()">
                                <i class="fas fa-eraser"></i> Limpar Campos
                            </button>
                        <?php endif; ?>

                        <a href="manage_items.php" class="btn btn-success btn-lg">
                            <i class="fas fa-plus"></i> Novo Item
                        </a>
                    </div>

                    <?php if (!$editItem): ?>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <strong>Dica:</strong> Após adicionar um item, os campos serão limpos automaticamente para facilitar a adição de novos produtos.
                        </small>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <!-- Lista de itens -->
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-md-8">
                        <h5>Itens Cadastrados</h5>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="btn-group" role="group">
                            <a href="add_kitchen_products.php" class="btn btn-sm btn-info">
                                🍳 Produtos de Cozinha
                            </a>
                            <a href="add_rotisserie_products.php" class="btn btn-sm btn-warning">
                                🍖 Produtos de Rotisseria
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($items)): ?>
                    <p>Nenhum item cadastrado ainda.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Código</th>
                                    <th>Nome</th>
                                    <th>Categoria</th>
                                    <th>Estoque</th>
                                    <th>Unidade</th>
                                    <th>Fornecedor</th>
                                    <th>Código de Barras</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                <tr>
                                    <td>
                                        <small class="text-muted">ID: <?php echo $item['id']; ?></small><br>
                                        <strong><?php echo htmlspecialchars($item['internal_code'] ?? 'ITEM' . str_pad($item['id'], 5, '0', STR_PAD_LEFT)); ?></strong>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                                        <?php if (!empty($item['description'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($item['description'], 0, 50)) . (strlen($item['description']) > 50 ? '...' : ''); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($item['category'])): ?>
                                            <span class="badge badge-secondary"><?php echo htmlspecialchars($item['category']); ?></span>
                                        <?php else: ?>
                                            <small class="text-muted">Não definida</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $currentStock = $item['current_stock'] ?? $item['quantity'];
                                        $minStock = $item['min_stock'] ?? 0;
                                        $stockClass = '';
                                        if ($currentStock <= $minStock && $minStock > 0) {
                                            $stockClass = 'text-danger font-weight-bold';
                                        } elseif ($currentStock <= ($minStock * 1.5) && $minStock > 0) {
                                            $stockClass = 'text-warning font-weight-bold';
                                        } else {
                                            $stockClass = 'text-success';
                                        }
                                        ?>
                                        <span class="<?php echo $stockClass; ?>">
                                            <?php echo $currentStock; ?>
                                        </span>
                                        <?php if ($minStock > 0): ?>
                                            <br><small class="text-muted">Mín: <?php echo $minStock; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-info"><?php echo htmlspecialchars($item['unit']); ?></span>
                                    </td>
                                    <td>
                                        <?php if (!empty($item['supplier'])): ?>
                                            <small><?php echo htmlspecialchars($item['supplier']); ?></small>
                                        <?php else: ?>
                                            <small class="text-muted">Não informado</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($item['barcode'])): ?>
                                            <small class="font-monospace"><?php echo htmlspecialchars($item['barcode']); ?></small>
                                            <br><button class="btn btn-xs btn-outline-secondary" onclick="showBarcode('<?php echo htmlspecialchars($item['barcode']); ?>', '<?php echo htmlspecialchars($item['name']); ?>')">
                                                <i class="fas fa-barcode"></i>
                                            </button>
                                        <?php else: ?>
                                            <small class="text-muted">Não gerado</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical btn-group-sm">
                                            <a href="?edit=<?php echo $item['id']; ?>" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit"></i> Editar
                                            </a>
                                            <form method="post" style="display: inline;"
                                                  onsubmit="return confirm('Tem certeza que deseja excluir este item?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-trash"></i> Excluir
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Modal para exibir código de barras -->
    <div class="modal fade" id="barcodeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-barcode"></i>
                        Código de Barras
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <h6 id="barcodeItemName"></h6>
                    <div id="barcodeDisplay" class="barcode-display mb-3"></div>
                    <div id="barcodeQR"></div>
                    <small class="text-muted">Código de barras para impressão e controle</small>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-primary" onclick="printBarcode()">
                        <i class="fas fa-print"></i> Imprimir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function showBarcode(barcode, itemName) {
            try {
                const barcodeNameEl = document.getElementById('barcodeItemName');
                const barcodeDisplayEl = document.getElementById('barcodeDisplay');
                const barcodeQREl = document.getElementById('barcodeQR');

                if (!barcodeNameEl || !barcodeDisplayEl || !barcodeQREl) {
                    console.error('Elementos do modal de código de barras não encontrados');
                    return;
                }

                barcodeNameEl.textContent = itemName;
                barcodeDisplayEl.textContent = barcode;

                // Gerar QR Code
                const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(barcode)}`;
                barcodeQREl.innerHTML = `<img src="${qrUrl}" alt="QR Code" class="img-fluid">`;

                $('#barcodeModal').modal('show');
            } catch (error) {
                console.error('Erro ao exibir código de barras:', error);
                alert('Erro ao exibir código de barras');
            }
        }

        function printBarcode() {
            try {
                const barcodeNameEl = document.getElementById('barcodeItemName');
                const barcodeDisplayEl = document.getElementById('barcodeDisplay');
                const barcodeQREl = document.getElementById('barcodeQR');

                if (!barcodeNameEl || !barcodeDisplayEl || !barcodeQREl) {
                    console.error('Elementos do modal não encontrados para impressão');
                    return;
                }

                const itemName = barcodeNameEl.textContent;
                const barcode = barcodeDisplayEl.textContent;
                const qrImg = barcodeQREl.innerHTML;

                const printWindow = window.open('', '_blank');
                if (!printWindow) {
                    alert('Não foi possível abrir janela de impressão. Verifique se pop-ups estão bloqueados.');
                    return;
                }

                printWindow.document.write(`
                    <html>
                    <head>
                        <title>Código de Barras - ${itemName}</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                            .barcode { font-family: 'Courier New', monospace; font-size: 18px; letter-spacing: 3px; margin: 20px 0; }
                            .item-name { font-size: 16px; font-weight: bold; margin-bottom: 10px; }
                            .qr-code { margin: 20px 0; }
                        </style>
                    </head>
                    <body>
                        <div class="item-name">${itemName}</div>
                        <div class="barcode">${barcode}</div>
                        <div class="qr-code">${qrImg}</div>
                        <script>
                            window.onload = function() {
                                window.print();
                                setTimeout(function() {
                                    window.close();
                                }, 1000);
                            };
                        </script>
                    </body>
                    </html>
                `);
                printWindow.document.close();
            } catch (error) {
                console.error('Erro ao imprimir código de barras:', error);
                alert('Erro ao imprimir código de barras');
            }
        }

        function clearForm() {
            if (confirm('Tem certeza que deseja limpar todos os campos do formulário?')) {
                try {
                    // Limpar todos os campos de input
                    document.querySelectorAll('input[type="text"], input[type="number"], textarea, select').forEach(field => {
                        if (!field.readOnly && !field.disabled) {
                            field.value = '';
                        }
                    });

                    // Resetar select de unidade para primeira opção
                    const unitSelect = document.querySelector('select[name="unit"]');
                    if (unitSelect) {
                        unitSelect.selectedIndex = 0;
                    }

                    // Limpar validações
                    clearNameValidation();
                    clearNameSuggestions();

                    // Focar no primeiro campo
                    const firstField = document.querySelector('input[name="name"]');
                    if (firstField) {
                        firstField.focus();
                    }

                    // Mostrar mensagem de confirmação
                    showTemporaryMessage('Campos limpos com sucesso!', 'success');
                } catch (error) {
                    console.error('Erro ao limpar formulário:', error);
                    alert('Erro ao limpar formulário');
                }
            }
        }

        function showTemporaryMessage(message, type = 'info') {
            try {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    ${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                `;

                // Inserir no topo da página
                const container = document.querySelector('.container');
                if (container && container.firstElementChild) {
                    const firstChild = container.firstElementChild;
                    container.insertBefore(alertDiv, firstChild.nextSibling);
                } else {
                    console.error('Container não encontrado para exibir mensagem');
                    return;
                }

                // Auto-remover após 3 segundos
                setTimeout(() => {
                    if (alertDiv && alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 3000);
            } catch (error) {
                console.error('Erro ao exibir mensagem temporária:', error);
            }
        }

        // Variáveis globais para controle de validação
        let nameCheckTimeout = null;
        let isNameValid = true;
        let isCheckingName = false;

        // Função para verificar nome duplicado via AJAX
        function checkDuplicateName(name, excludeId = 0) {
            if (name.length < 2) {
                clearNameValidation();
                return;
            }

            isCheckingName = true;
            showNameValidation('Verificando...', 'info');

            const formData = new FormData();
            formData.append('name', name);
            if (excludeId > 0) {
                formData.append('exclude_id', excludeId);
            }

            fetch('check_duplicate_item.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                isCheckingName = false;

                if (data.error) {
                    showNameValidation('Erro na verificação', 'danger');
                    isNameValid = false;
                } else if (data.exists) {
                    const message = `Nome já existe! Item: "${data.item.name}" (${data.item.internal_code})`;
                    showNameValidation(message, 'danger');
                    isNameValid = false;

                    // Mostrar sugestões se disponíveis
                    if (data.suggestion && data.suggestion.length > 0) {
                        showNameSuggestions(data.suggestion);
                    }
                } else {
                    showNameValidation('Nome disponível ✓', 'success');
                    isNameValid = true;
                    clearNameSuggestions();
                }
            })
            .catch(error => {
                isCheckingName = false;
                console.error('Erro na verificação:', error);
                showNameValidation('Erro na verificação - continuando...', 'warning');
                isNameValid = true; // Permitir continuar em caso de erro
            });
        }

        // Mostrar status da validação do nome
        function showNameValidation(message, type) {
            try {
                let validationDiv = document.getElementById('name-validation');

                if (!validationDiv) {
                    validationDiv = document.createElement('div');
                    validationDiv.id = 'name-validation';
                    validationDiv.className = 'mt-1';

                    const nameInput = document.querySelector('input[name="name"]');
                    if (nameInput && nameInput.parentNode) {
                        nameInput.parentNode.appendChild(validationDiv);
                    } else {
                        console.error('Campo de nome não encontrado para validação');
                        return;
                    }
                }

                const iconMap = {
                    'info': 'fas fa-spinner fa-spin',
                    'success': 'fas fa-check-circle',
                    'danger': 'fas fa-exclamation-triangle',
                    'warning': 'fas fa-exclamation-circle'
                };

                validationDiv.innerHTML = `
                    <small class="text-${type}">
                        <i class="${iconMap[type] || 'fas fa-info-circle'}"></i>
                        ${message}
                    </small>
                `;
            } catch (error) {
                console.error('Erro ao exibir validação do nome:', error);
            }
        }

        // Limpar validação do nome
        function clearNameValidation() {
            try {
                const validationDiv = document.getElementById('name-validation');
                if (validationDiv) {
                    validationDiv.innerHTML = '';
                }
                isNameValid = true;
            } catch (error) {
                console.error('Erro ao limpar validação:', error);
            }
        }

        // Mostrar sugestões de nomes alternativos
        function showNameSuggestions(suggestions) {
            try {
                if (!Array.isArray(suggestions) || suggestions.length === 0) {
                    return;
                }

                let suggestionsDiv = document.getElementById('name-suggestions');

                if (!suggestionsDiv) {
                    suggestionsDiv = document.createElement('div');
                    suggestionsDiv.id = 'name-suggestions';
                    suggestionsDiv.className = 'mt-2';

                    const nameInput = document.querySelector('input[name="name"]');
                    if (nameInput && nameInput.parentNode) {
                        nameInput.parentNode.appendChild(suggestionsDiv);
                    } else {
                        console.error('Campo de nome não encontrado para sugestões');
                        return;
                    }
                }

                let html = '<small class="text-muted"><strong>Sugestões:</strong></small><br>';
                suggestions.forEach(suggestion => {
                    const escapedSuggestion = suggestion.replace(/'/g, "\\'").replace(/"/g, '\\"');
                    html += `
                        <button type="button" class="btn btn-sm btn-outline-primary mr-1 mb-1 suggestion-btn"
                                onclick="applySuggestion('${escapedSuggestion}')">
                            ${suggestion}
                        </button>
                    `;
                });

                suggestionsDiv.innerHTML = html;
            } catch (error) {
                console.error('Erro ao exibir sugestões:', error);
            }
        }

        // Limpar sugestões
        function clearNameSuggestions() {
            try {
                const suggestionsDiv = document.getElementById('name-suggestions');
                if (suggestionsDiv) {
                    suggestionsDiv.innerHTML = '';
                }
            } catch (error) {
                console.error('Erro ao limpar sugestões:', error);
            }
        }

        // Aplicar sugestão de nome
        function applySuggestion(suggestion) {
            try {
                const nameInput = document.querySelector('input[name="name"]');
                if (!nameInput) {
                    console.error('Campo de nome não encontrado');
                    return;
                }

                nameInput.value = suggestion;
                nameInput.focus();

                // Verificar o novo nome
                clearTimeout(nameCheckTimeout);
                nameCheckTimeout = setTimeout(() => {
                    const excludeIdInput = document.querySelector('input[name="id"]');
                    const excludeId = excludeIdInput ? excludeIdInput.value : 0;
                    checkDuplicateName(suggestion, excludeId);
                }, 500);

                clearNameSuggestions();
            } catch (error) {
                console.error('Erro ao aplicar sugestão:', error);
            }
        }

        // Função para validar formulário antes do envio
        function validateForm() {
            try {
                const nameInput = document.querySelector('input[name="name"]');
                const unitSelect = document.querySelector('select[name="unit"]');

                if (!nameInput || !unitSelect) {
                    console.error('Campos obrigatórios não encontrados');
                    return false;
                }

                const name = nameInput.value.trim();
                const unit = unitSelect.value;

                if (!name) {
                    alert('Por favor, preencha o nome do item.');
                    nameInput.focus();
                    return false;
                }

                if (!unit) {
                    alert('Por favor, selecione uma unidade.');
                    unitSelect.focus();
                    return false;
                }

                if (isCheckingName) {
                    alert('Aguarde a verificação do nome terminar.');
                    return false;
                }

                if (!isNameValid) {
                    alert('O nome do item já existe. Por favor, escolha um nome diferente ou use uma das sugestões.');
                    nameInput.focus();
                    return false;
                }

                return true;
            } catch (error) {
                console.error('Erro na validação do formulário:', error);
                return false;
            }
        }

        // Inicialização quando a página carrega
        $(document).ready(function() {
            try {
                // Auto-focus no campo nome se não estiver editando
                <?php if (!$editItem): ?>
                const nameField = document.querySelector('input[name="name"]');
                if (nameField) {
                    nameField.focus();
                }
                <?php endif; ?>

                // Adicionar validação ao formulário
                const form = document.querySelector('form[method="post"]');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        if (!validateForm()) {
                            e.preventDefault();
                        }
                    });
                } else {
                    console.warn('Formulário não encontrado');
                }

                // Auto-remover alertas de sucesso após 5 segundos
                setTimeout(() => {
                    try {
                        document.querySelectorAll('.alert-success, .alert-info').forEach(alert => {
                            const closeBtn = alert.querySelector('.close');
                            if (closeBtn) {
                                closeBtn.click();
                            }
                        });
                    } catch (error) {
                        console.error('Erro ao remover alertas:', error);
                    }
                }, 5000);

                // Adicionar efeito de destaque nos campos obrigatórios
                document.querySelectorAll('input[required], select[required]').forEach(field => {
                    field.addEventListener('blur', function() {
                        try {
                            if (!this.value.trim()) {
                                this.classList.add('border-warning');
                            } else {
                                this.classList.remove('border-warning');
                            }
                        } catch (error) {
                            console.error('Erro no evento blur:', error);
                        }
                    });
                });

                // Configurar validação em tempo real para o campo nome
                const nameInput = document.querySelector('input[name="name"]');
                if (nameInput) {
                    // Verificar ao digitar (com delay)
                    nameInput.addEventListener('input', function() {
                        try {
                            const name = this.value.trim();
                            const excludeIdInput = document.querySelector('input[name="id"]');
                            const excludeId = excludeIdInput ? excludeIdInput.value : 0;

                            clearTimeout(nameCheckTimeout);
                            clearNameSuggestions();

                            if (name.length >= 2) {
                                nameCheckTimeout = setTimeout(() => {
                                    checkDuplicateName(name, excludeId);
                                }, 800); // Delay de 800ms para evitar muitas requisições
                            } else {
                                clearNameValidation();
                            }
                        } catch (error) {
                            console.error('Erro no evento input:', error);
                        }
                    });

                    // Verificar ao sair do campo
                    nameInput.addEventListener('blur', function() {
                        try {
                            const name = this.value.trim();
                            const excludeIdInput = document.querySelector('input[name="id"]');
                            const excludeId = excludeIdInput ? excludeIdInput.value : 0;

                            if (name.length >= 2) {
                                clearTimeout(nameCheckTimeout);
                                checkDuplicateName(name, excludeId);
                            }
                        } catch (error) {
                            console.error('Erro no evento blur do nome:', error);
                        }
                    });

                    // Limpar validação ao focar
                    nameInput.addEventListener('focus', function() {
                        try {
                            this.classList.remove('border-warning', 'border-danger');
                        } catch (error) {
                            console.error('Erro no evento focus:', error);
                        }
                    });
                } else {
                    console.warn('Campo de nome não encontrado para validação');
                }
            } catch (error) {
                console.error('Erro na inicialização:', error);
            }
        });

        // Atalhos de teclado
        document.addEventListener('keydown', function(e) {
            try {
                // Ctrl + N = Novo item
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    window.location.href = 'manage_items.php';
                }

                // Ctrl + L = Limpar campos (apenas se não estiver editando)
                <?php if (!$editItem): ?>
                if (e.ctrlKey && e.key === 'l') {
                    e.preventDefault();
                    clearForm();
                }
                <?php endif; ?>
            } catch (error) {
                console.error('Erro nos atalhos de teclado:', error);
            }
        });

        // Verificar se jQuery está carregado
        if (typeof $ === 'undefined') {
            console.error('jQuery não foi carregado corretamente');
        }

        // Verificar se Bootstrap está carregado
        if (typeof bootstrap === 'undefined' && typeof $.fn.modal === 'undefined') {
            console.warn('Bootstrap JavaScript pode não estar carregado corretamente');
        }
    </script>
</body>
</html>
