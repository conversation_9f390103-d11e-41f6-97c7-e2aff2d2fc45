<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

$message = '';
$error = '';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $quantity = (int)($_POST['quantity'] ?? 0);
                $unit = trim($_POST['unit'] ?? '');
                
                if (empty($name) || empty($unit)) {
                    $error = 'Nome e unidade são obrigatórios';
                } else {
                    try {
                        $stmt = $pdo->prepare("INSERT INTO items (name, description, quantity, unit) VALUES (?, ?, ?, ?)");
                        if ($stmt->execute([$name, $description, $quantity, $unit])) {
                            $message = 'Item adicionado com sucesso!';
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao adicionar item: ' . $e->getMessage();
                    }
                }
                break;
                
            case 'update':
                $id = (int)($_POST['id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $quantity = (int)($_POST['quantity'] ?? 0);
                $unit = trim($_POST['unit'] ?? '');
                
                if (empty($name) || empty($unit) || $id <= 0) {
                    $error = 'Dados inválidos para atualização';
                } else {
                    try {
                        $stmt = $pdo->prepare("UPDATE items SET name = ?, description = ?, quantity = ?, unit = ? WHERE id = ?");
                        if ($stmt->execute([$name, $description, $quantity, $unit, $id])) {
                            $message = 'Item atualizado com sucesso!';
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao atualizar item: ' . $e->getMessage();
                    }
                }
                break;
                
            case 'delete':
                $id = (int)($_POST['id'] ?? 0);
                if ($id > 0) {
                    try {
                        // Verificar se o item está sendo usado em requisições
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM request_items WHERE item_id = ?");
                        $stmt->execute([$id]);
                        $count = $stmt->fetchColumn();
                        
                        if ($count > 0) {
                            $error = 'Não é possível excluir este item pois ele está sendo usado em requisições';
                        } else {
                            $stmt = $pdo->prepare("DELETE FROM items WHERE id = ?");
                            if ($stmt->execute([$id])) {
                                $message = 'Item excluído com sucesso!';
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao excluir item: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Buscar todos os itens
$stmt = $pdo->query("SELECT * FROM items ORDER BY name");
$items = $stmt->fetchAll();

// Buscar item para edição se solicitado
$editItem = null;
if (isset($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    $stmt = $pdo->prepare("SELECT * FROM items WHERE id = ?");
    $stmt->execute([$editId]);
    $editItem = $stmt->fetch();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Gerenciar Itens - Sistema de Requisição de Material de Cozinha</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <h2>Gerenciar Itens</h2>
        
        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Formulário para adicionar/editar item -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><?php echo $editItem ? 'Editar Item' : 'Adicionar Novo Item'; ?></h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?php echo $editItem ? 'update' : 'add'; ?>">
                    <?php if ($editItem): ?>
                        <input type="hidden" name="id" value="<?php echo $editItem['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nome do Item *</label>
                                <input type="text" name="name" class="form-control" 
                                       value="<?php echo $editItem ? htmlspecialchars($editItem['name']) : ''; ?>" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Quantidade</label>
                                <input type="number" name="quantity" class="form-control" min="0"
                                       value="<?php echo $editItem ? $editItem['quantity'] : '0'; ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Unidade *</label>
                                <input type="text" name="unit" class="form-control" 
                                       value="<?php echo $editItem ? htmlspecialchars($editItem['unit']) : ''; ?>" 
                                       placeholder="kg, unid, litros..." required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Descrição</label>
                        <textarea name="description" class="form-control" rows="3"><?php echo $editItem ? htmlspecialchars($editItem['description']) : ''; ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <?php echo $editItem ? 'Atualizar Item' : 'Adicionar Item'; ?>
                    </button>
                    <?php if ($editItem): ?>
                        <a href="manage_items.php" class="btn btn-secondary">Cancelar</a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <!-- Lista de itens -->
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-md-8">
                        <h5>Itens Cadastrados</h5>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="btn-group" role="group">
                            <a href="add_kitchen_products.php" class="btn btn-sm btn-info">
                                🍳 Produtos de Cozinha
                            </a>
                            <a href="add_rotisserie_products.php" class="btn btn-sm btn-warning">
                                🍖 Produtos de Rotisseria
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($items)): ?>
                    <p>Nenhum item cadastrado ainda.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Descrição</th>
                                    <th>Quantidade</th>
                                    <th>Unidade</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                <tr>
                                    <td><?php echo $item['id']; ?></td>
                                    <td><?php echo htmlspecialchars($item['name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['description']); ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td><?php echo htmlspecialchars($item['unit']); ?></td>
                                    <td>
                                        <a href="?edit=<?php echo $item['id']; ?>" class="btn btn-sm btn-warning">Editar</a>
                                        <form method="post" style="display: inline;" 
                                              onsubmit="return confirm('Tem certeza que deseja excluir este item?')">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger">Excluir</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
