<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';
require_once 'includes/barcode_generator.php';

$message = '';
$error = '';

// Capturar mensagem de sucesso da URL
if (isset($_GET['success'])) {
    $message = $_GET['success'];
}

// Processar ações
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $quantity = (int)($_POST['quantity'] ?? 0);
                $unit = trim($_POST['unit'] ?? '');
                $category = trim($_POST['category'] ?? '');
                $supplier = trim($_POST['supplier'] ?? '');
                $minStock = (int)($_POST['min_stock'] ?? 0);
                $currentStock = (int)($_POST['current_stock'] ?? 0);
                $costPrice = (float)($_POST['cost_price'] ?? 0);
                $barcode = trim($_POST['barcode'] ?? '');

                if (empty($name) || empty($unit)) {
                    $error = 'Nome e unidade são obrigatórios';
                } else {
                    // Verificar se já existe um item com o mesmo nome
                    $checkStmt = $pdo->prepare("SELECT id, name FROM items WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))");
                    $checkStmt->execute([$name]);
                    $existingItem = $checkStmt->fetch();

                    if ($existingItem) {
                        $error = "Já existe um item com o nome \"" . htmlspecialchars($existingItem['name']) . "\" (ID: " . $existingItem['id'] . "). Por favor, escolha um nome diferente.";
                    } else {
                    try {
                        // Gerar código interno
                        $internalCode = generateItemInternalCode($pdo);

                        // Gerar código de barras se não fornecido
                        if (empty($barcode)) {
                            $barcode = generateItemBarcode(0); // Será atualizado após inserção
                        }

                        // Tentar inserir com todos os campos novos
                        try {
                            $stmt = $pdo->prepare("
                                INSERT INTO items (name, description, quantity, unit, internal_code, barcode,
                                                 category, supplier, min_stock, current_stock, cost_price)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ");
                            $stmt->execute([$name, $description, $quantity, $unit, $internalCode, $barcode,
                                          $category, $supplier, $minStock, $currentStock, $costPrice]);
                            $itemId = $pdo->lastInsertId();

                            // Atualizar código de barras com ID real se foi gerado automaticamente
                            if (empty($_POST['barcode'])) {
                                $finalBarcode = generateItemBarcode($itemId);
                                $updateStmt = $pdo->prepare("UPDATE items SET barcode = ? WHERE id = ?");
                                $updateStmt->execute([$finalBarcode, $itemId]);
                            }

                            $message = "Item adicionado com sucesso! Código interno: $internalCode";

                            // Redirecionar para limpar o formulário após sucesso
                            header("Location: manage_items.php?success=" . urlencode($message));
                            exit;

                        } catch (PDOException $e) {
                            // Fallback para versão antiga do banco
                            $stmt = $pdo->prepare("INSERT INTO items (name, description, quantity, unit) VALUES (?, ?, ?, ?)");
                            if ($stmt->execute([$name, $description, $quantity, $unit])) {
                                $message = 'Item adicionado com sucesso! (Versão básica - atualize o banco para recursos completos)';

                                // Redirecionar para limpar o formulário após sucesso
                                header("Location: manage_items.php?success=" . urlencode($message));
                                exit;
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao adicionar item: ' . $e->getMessage();
                    }
                    } // Fechar o else da verificação de duplicatas
                }
                break;
                
            case 'update':
                $id = (int)($_POST['id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $quantity = (int)($_POST['quantity'] ?? 0);
                $unit = trim($_POST['unit'] ?? '');
                $category = trim($_POST['category'] ?? '');
                $supplier = trim($_POST['supplier'] ?? '');
                $minStock = (int)($_POST['min_stock'] ?? 0);
                $currentStock = (int)($_POST['current_stock'] ?? 0);
                $costPrice = (float)($_POST['cost_price'] ?? 0);
                $barcode = trim($_POST['barcode'] ?? '');

                if (empty($name) || empty($unit) || $id <= 0) {
                    $error = 'Dados inválidos para atualização';
                } else {
                    // Verificar se já existe outro item com o mesmo nome (excluindo o item atual)
                    $checkStmt = $pdo->prepare("SELECT id, name FROM items WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND id != ?");
                    $checkStmt->execute([$name, $id]);
                    $existingItem = $checkStmt->fetch();

                    if ($existingItem) {
                        $error = "Já existe outro item com o nome \"" . htmlspecialchars($existingItem['name']) . "\" (ID: " . $existingItem['id'] . "). Por favor, escolha um nome diferente.";
                    } else {
                    try {
                        // Tentar atualizar com todos os campos
                        try {
                            $stmt = $pdo->prepare("
                                UPDATE items SET name = ?, description = ?, quantity = ?, unit = ?,
                                               barcode = ?, category = ?, supplier = ?, min_stock = ?,
                                               current_stock = ?, cost_price = ?
                                WHERE id = ?
                            ");
                            if ($stmt->execute([$name, $description, $quantity, $unit, $barcode,
                                              $category, $supplier, $minStock, $currentStock, $costPrice, $id])) {
                                $message = 'Item atualizado com sucesso!';
                            }
                        } catch (PDOException $e) {
                            // Fallback para versão antiga
                            $stmt = $pdo->prepare("UPDATE items SET name = ?, description = ?, quantity = ?, unit = ? WHERE id = ?");
                            if ($stmt->execute([$name, $description, $quantity, $unit, $id])) {
                                $message = 'Item atualizado com sucesso! (Versão básica)';
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao atualizar item: ' . $e->getMessage();
                    }
                    } // Fechar o else da verificação de duplicatas
                }
                break;
                
            case 'delete':
                $id = (int)($_POST['id'] ?? 0);
                if ($id > 0) {
                    try {
                        // Verificar se o item está sendo usado em requisições
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM request_items WHERE item_id = ?");
                        $stmt->execute([$id]);
                        $count = $stmt->fetchColumn();
                        
                        if ($count > 0) {
                            $error = 'Não é possível excluir este item pois ele está sendo usado em requisições';
                        } else {
                            $stmt = $pdo->prepare("DELETE FROM items WHERE id = ?");
                            if ($stmt->execute([$id])) {
                                $message = 'Item excluído com sucesso!';
                            }
                        }
                    } catch (PDOException $e) {
                        $error = 'Erro ao excluir item: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Buscar todos os itens
$stmt = $pdo->query("SELECT * FROM items ORDER BY name");
$items = $stmt->fetchAll();

// Buscar item para edição se solicitado (mas não se acabou de adicionar um item)
$editItem = null;
if (isset($_GET['edit']) && !isset($_GET['success'])) {
    $editId = (int)$_GET['edit'];
    $stmt = $pdo->prepare("SELECT * FROM items WHERE id = ?");
    $stmt->execute([$editId]);
    $editItem = $stmt->fetch();
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sistema de Gerenciamento de Itens para Requisição de Material de Cozinha">
    <meta name="author" content="Sistema de Requisição de Material de Cozinha">

    <title>Gerenciar Itens - Sistema de Requisição de Material de Cozinha</title>

    <!-- CSS Frameworks -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
          integrity="sha384-JcKb8q3iqJ61gNV9KGb8thSsNjpSL0n8PARn9HuZOnIxN0hoP+VmmDGMN5t9UJ0Z"
          crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
          integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ=="
          crossorigin="anonymous">

    <!-- CSS Customizado -->
    <link rel="stylesheet" href="assets/css/manage-items.css">
    <link rel="stylesheet" href="assets/css/components.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <h2>Gerenciar Itens</h2>
        
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                <strong>Sucesso!</strong> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle"></i>
                <strong>Pronto para o próximo!</strong> Os campos foram limpos automaticamente. Você pode adicionar um novo item agora.
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Formulário para adicionar/editar item -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="row">
                    <div class="col-md-8">
                        <h5>
                            <?php if ($editItem): ?>
                                <i class="fas fa-edit text-warning"></i>
                                Editar Item: <?php echo htmlspecialchars($editItem['name']); ?>
                            <?php else: ?>
                                <i class="fas fa-plus text-success"></i>
                                Adicionar Novo Item
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="col-md-4 text-right">
                        <?php if ($editItem): ?>
                            <a href="manage_items.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus"></i> Novo Item
                            </a>
                            <a href="manage_items.php" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i> Cancelar
                            </a>
                        <?php else: ?>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Preencha os campos abaixo
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?php echo $editItem ? 'update' : 'add'; ?>">
                    <?php if ($editItem): ?>
                        <input type="hidden" name="id" value="<?php echo $editItem['id']; ?>">
                    <?php endif; ?>
                    
                    <!-- Informações Básicas -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group name-validation-container">
                                <label>
                                    <i class="fas fa-tag text-primary"></i>
                                    Nome do Item *
                                </label>
                                <input type="text" name="name" class="form-control input-with-validation"
                                       value="<?php echo $editItem ? htmlspecialchars($editItem['name']) : ''; ?>"
                                       placeholder="Ex: Açúcar Cristal, Farinha de Trigo..."
                                       required
                                       autocomplete="off"
                                       data-toggle="tooltip"
                                       data-placement="top"
                                       title="O nome será verificado automaticamente para evitar duplicatas">
                                <div id="name-validation"></div>
                                <div id="name-suggestions"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>
                                    <i class="fas fa-layer-group text-info"></i>
                                    Categoria
                                </label>
                                <input type="text" name="category" class="form-control"
                                       value="<?php echo $editItem ? htmlspecialchars($editItem['category'] ?? '') : ''; ?>"
                                       placeholder="Ex: Açúcar, Farinha, Temperos...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>
                                    <i class="fas fa-balance-scale text-secondary"></i>
                                    Unidade *
                                </label>
                                <select name="unit" class="form-control" required>
                                    <option value="">Selecione...</option>
                                    <option value="kg" <?php echo ($editItem && $editItem['unit'] == 'kg') ? 'selected' : ''; ?>>kg (quilograma)</option>
                                    <option value="g" <?php echo ($editItem && $editItem['unit'] == 'g') ? 'selected' : ''; ?>>g (grama)</option>
                                    <option value="l" <?php echo ($editItem && $editItem['unit'] == 'l') ? 'selected' : ''; ?>>l (litro)</option>
                                    <option value="ml" <?php echo ($editItem && $editItem['unit'] == 'ml') ? 'selected' : ''; ?>>ml (mililitro)</option>
                                    <option value="unid" <?php echo ($editItem && $editItem['unit'] == 'unid') ? 'selected' : ''; ?>>unid (unidade)</option>
                                    <option value="pct" <?php echo ($editItem && $editItem['unit'] == 'pct') ? 'selected' : ''; ?>>pct (pacote)</option>
                                    <option value="cx" <?php echo ($editItem && $editItem['unit'] == 'cx') ? 'selected' : ''; ?>>cx (caixa)</option>
                                    <option value="sc" <?php echo ($editItem && $editItem['unit'] == 'sc') ? 'selected' : ''; ?>>sc (saco)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class="fas fa-align-left text-muted"></i>
                            Descrição Detalhada
                        </label>
                        <textarea name="description" class="form-control" rows="2"
                                  placeholder="Descrição completa do produto, marca, especificações..."><?php echo $editItem ? htmlspecialchars($editItem['description']) : ''; ?></textarea>
                    </div>

                    <!-- Códigos e Identificação -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-barcode text-dark"></i>
                                Códigos e Identificação
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-hashtag text-primary"></i>
                                            Código Interno
                                        </label>
                                        <input type="text" class="form-control"
                                               value="<?php echo $editItem ? htmlspecialchars($editItem['internal_code'] ?? 'Será gerado automaticamente') : 'Será gerado automaticamente'; ?>"
                                               readonly>
                                        <small class="text-muted">Gerado automaticamente (ex: ITEM00001)</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-barcode text-dark"></i>
                                            Código de Barras
                                        </label>
                                        <input type="text" name="barcode" class="form-control"
                                               value="<?php echo $editItem ? htmlspecialchars($editItem['barcode'] ?? '') : ''; ?>"
                                               placeholder="Deixe vazio para gerar automaticamente">
                                        <small class="text-muted">Opcional - será gerado se vazio</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Estoque e Fornecedor -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-warehouse text-success"></i>
                                Controle de Estoque
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-boxes text-warning"></i>
                                            Estoque Atual
                                        </label>
                                        <input type="number" name="current_stock" class="form-control" min="0" step="0.01"
                                               value="<?php echo $editItem ? ($editItem['current_stock'] ?? '0') : '0'; ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-exclamation-triangle text-danger"></i>
                                            Estoque Mínimo
                                        </label>
                                        <input type="number" name="min_stock" class="form-control" min="0" step="0.01"
                                               value="<?php echo $editItem ? ($editItem['min_stock'] ?? '0') : '0'; ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-dollar-sign text-success"></i>
                                            Preço de Custo
                                        </label>
                                        <input type="number" name="cost_price" class="form-control" min="0" step="0.01"
                                               value="<?php echo $editItem ? ($editItem['cost_price'] ?? '0.00') : '0.00'; ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>
                                            <i class="fas fa-archive text-info"></i>
                                            Quantidade (Legado)
                                        </label>
                                        <input type="number" name="quantity" class="form-control" min="0"
                                               value="<?php echo $editItem ? $editItem['quantity'] : '0'; ?>">
                                        <small class="text-muted">Campo antigo - use Estoque Atual</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>
                                    <i class="fas fa-truck text-primary"></i>
                                    Fornecedor Principal
                                </label>
                                <input type="text" name="supplier" class="form-control"
                                       value="<?php echo $editItem ? htmlspecialchars($editItem['supplier'] ?? '') : ''; ?>"
                                       placeholder="Nome do fornecedor principal">
                            </div>
                        </div>
                    </div>
                    
                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <?php if ($editItem): ?>
                                <i class="fas fa-save"></i> Atualizar Item
                            <?php else: ?>
                                <i class="fas fa-plus"></i> Adicionar Item
                            <?php endif; ?>
                        </button>

                        <?php if ($editItem): ?>
                            <a href="manage_items.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Cancelar
                            </a>
                        <?php else: ?>
                            <button type="button" class="btn btn-outline-secondary btn-lg" onclick="clearForm()">
                                <i class="fas fa-eraser"></i> Limpar Campos
                            </button>
                        <?php endif; ?>

                        <a href="manage_items.php" class="btn btn-success btn-lg">
                            <i class="fas fa-plus"></i> Novo Item
                        </a>
                    </div>

                    <?php if (!$editItem): ?>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb text-warning"></i>
                            <strong>Dica:</strong> Após adicionar um item, os campos serão limpos automaticamente para facilitar a adição de novos produtos.
                        </small>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <!-- Lista de itens -->
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-md-8">
                        <h5>Itens Cadastrados</h5>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="btn-group" role="group">
                            <a href="add_kitchen_products.php" class="btn btn-sm btn-info">
                                🍳 Produtos de Cozinha
                            </a>
                            <a href="add_rotisserie_products.php" class="btn btn-sm btn-warning">
                                🍖 Produtos de Rotisseria
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($items)): ?>
                    <p>Nenhum item cadastrado ainda.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Código</th>
                                    <th>Nome</th>
                                    <th>Categoria</th>
                                    <th>Estoque</th>
                                    <th>Unidade</th>
                                    <th>Fornecedor</th>
                                    <th>Código de Barras</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                <tr>
                                    <td>
                                        <small class="text-muted">ID: <?php echo $item['id']; ?></small><br>
                                        <strong><?php echo htmlspecialchars($item['internal_code'] ?? 'ITEM' . str_pad($item['id'], 5, '0', STR_PAD_LEFT)); ?></strong>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                                        <?php if (!empty($item['description'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($item['description'], 0, 50)) . (strlen($item['description']) > 50 ? '...' : ''); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($item['category'])): ?>
                                            <span class="badge badge-secondary"><?php echo htmlspecialchars($item['category']); ?></span>
                                        <?php else: ?>
                                            <small class="text-muted">Não definida</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $currentStock = $item['current_stock'] ?? $item['quantity'];
                                        $minStock = $item['min_stock'] ?? 0;
                                        $stockClass = '';
                                        if ($currentStock <= $minStock && $minStock > 0) {
                                            $stockClass = 'text-danger font-weight-bold';
                                        } elseif ($currentStock <= ($minStock * 1.5) && $minStock > 0) {
                                            $stockClass = 'text-warning font-weight-bold';
                                        } else {
                                            $stockClass = 'text-success';
                                        }
                                        ?>
                                        <span class="<?php echo $stockClass; ?>">
                                            <?php echo $currentStock; ?>
                                        </span>
                                        <?php if ($minStock > 0): ?>
                                            <br><small class="text-muted">Mín: <?php echo $minStock; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-info"><?php echo htmlspecialchars($item['unit']); ?></span>
                                    </td>
                                    <td>
                                        <?php if (!empty($item['supplier'])): ?>
                                            <small><?php echo htmlspecialchars($item['supplier']); ?></small>
                                        <?php else: ?>
                                            <small class="text-muted">Não informado</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($item['barcode'])): ?>
                                            <small class="font-monospace"><?php echo htmlspecialchars($item['barcode']); ?></small>
                                            <br><button class="btn btn-xs btn-outline-secondary" onclick="showBarcode('<?php echo htmlspecialchars($item['barcode']); ?>', '<?php echo htmlspecialchars($item['name']); ?>')">
                                                <i class="fas fa-barcode"></i>
                                            </button>
                                        <?php else: ?>
                                            <small class="text-muted">Não gerado</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical btn-group-sm">
                                            <a href="?edit=<?php echo $item['id']; ?>" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit"></i> Editar
                                            </a>
                                            <form method="post" style="display: inline;"
                                                  onsubmit="return confirm('Tem certeza que deseja excluir este item?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-trash"></i> Excluir
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Modal para exibir código de barras -->
    <div class="modal fade" id="barcodeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-barcode"></i>
                        Código de Barras
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <h6 id="barcodeItemName"></h6>
                    <div id="barcodeDisplay" class="barcode-display mb-3"></div>
                    <div id="barcodeQR"></div>
                    <small class="text-muted">Código de barras para impressão e controle</small>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-primary" onclick="printBarcode()">
                        <i class="fas fa-print"></i> Imprimir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Frameworks -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"
            integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj"
            crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct"
            crossorigin="anonymous"></script>

    <!-- JavaScript Customizado -->
    <script src="assets/js/manage-items.js"></script>

    <!-- Configurações específicas da página -->
    <script>
        // Configurar se está editando item
        window.MANAGE_ITEMS_CONFIG = {
            isEditing: <?php echo $editItem ? 'true' : 'false'; ?>,
            editItemId: <?php echo $editItem ? $editItem['id'] : 'null'; ?>
        };
    </script>
</body>
</html>
