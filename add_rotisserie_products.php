<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_rotisserie_products'])) {
    try {
        // Produtos específicos para rotisseria
        $rotisserieProducts = [
            // CARNES PARA ASSADOS
            ['Frango Inteiro para Assar', 'Frango inteiro temperado para rotisseria', 50, 'unidades'],
            ['Frango Desossado', 'Frango desossado para assados especiais', 30, 'kg'],
            ['Per<PERSON><PERSON> de Porco', 'Pernil de porco para assar', 25, 'kg'],
            ['Costela Bovina', 'Costela bovina para churrasco', 40, 'kg'],
            ['<PERSON><PERSON><PERSON>', 'Pi<PERSON>ha bovina para assar', 20, 'kg'],
            ['Alcatra', 'Alcatra bovina para assados', 25, 'kg'],
            ['Lombo de Porco', 'Lombo de porco para assar', 20, 'kg'],
            ['Cordeiro', 'Perna de cordeiro para assar', 15, 'kg'],
            ['Peru Inteiro', 'Peru inteiro para ocasiões especiais', 10, 'unidades'],
            ['Chester', 'Chester para datas comemorativas', 15, 'unidades'],
            
            // TEMPEROS E MARINADAS PARA ROTISSERIA
            ['Tempero para Frango Assado', 'Tempero especial para frango de rotisseria', 20, 'kg'],
            ['Tempero para Carne de Porco', 'Tempero especial para porco assado', 15, 'kg'],
            ['Tempero para Carne Bovina', 'Tempero especial para carne bovina', 15, 'kg'],
            ['Sal Grosso', 'Sal grosso para churrasco', 50, 'kg'],
            ['Pimenta Calabresa', 'Pimenta calabresa em flocos', 5, 'kg'],
            ['Alho em Pó', 'Alho desidratado em pó', 8, 'kg'],
            ['Cebola em Pó', 'Cebola desidratada em pó', 8, 'kg'],
            ['Páprica Defumada', 'Páprica defumada para assados', 5, 'kg'],
            ['Ervas Finas', 'Mix de ervas para assados', 3, 'kg'],
            ['Alecrim Fresco', 'Alecrim fresco para temperar', 10, 'maços'],
            
            // MOLHOS E GLAZES
            ['Molho Barbecue', 'Molho barbecue para pincelar', 30, 'frascos'],
            ['Molho Teriyaki', 'Molho teriyaki para glazear', 20, 'frascos'],
            ['Molho de Pimenta', 'Molho de pimenta artesanal', 25, 'frascos'],
            ['Mel', 'Mel puro para glazear carnes', 15, 'kg'],
            ['Melado de Cana', 'Melado de cana para caramelizar', 10, 'kg'],
            ['Mostarda Dijon', 'Mostarda dijon para marinadas', 15, 'frascos'],
            ['Molho Inglês', 'Molho inglês para temperar', 20, 'frascos'],
            ['Vinho Branco Seco', 'Vinho branco para cozinhar', 20, 'garrafas'],
            ['Vinho Tinto Seco', 'Vinho tinto para marinadas', 15, 'garrafas'],
            
            // ACOMPANHAMENTOS PARA ROTISSERIA
            ['Batata para Assar', 'Batata especial para assar inteira', 60, 'kg'],
            ['Batata Doce', 'Batata doce para assar', 40, 'kg'],
            ['Mandioca', 'Mandioca para assar', 35, 'kg'],
            ['Milho Verde', 'Milho verde para assar na espiga', 100, 'espigas'],
            ['Pimentão para Assar', 'Pimentão colorido para assar', 20, 'kg'],
            ['Abobrinha para Grelhar', 'Abobrinha italiana para grelhar', 15, 'kg'],
            ['Berinjela para Grelhar', 'Berinjela para grelhar', 12, 'kg'],
            ['Cogumelo Portobello', 'Cogumelo portobello para grelhar', 8, 'kg'],
            
            // PÃES E ACOMPANHAMENTOS
            ['Pão de Alho', 'Pão de alho para acompanhar', 50, 'unidades'],
            ['Pão Sírio', 'Pão sírio para acompanhar', 100, 'unidades'],
            ['Pão Francês para Sanduíche', 'Pão francês para sanduíches', 200, 'unidades'],
            ['Pão de Hambúrguer', 'Pão de hambúrguer artesanal', 100, 'unidades'],
            ['Torrada para Bruschetta', 'Fatias de pão para bruschetta', 50, 'pacotes'],
            
            // SALADAS E VEGETAIS
            ['Mix de Folhas', 'Mix de folhas verdes para salada', 30, 'pacotes'],
            ['Tomate Cereja', 'Tomate cereja para saladas', 15, 'kg'],
            ['Pepino', 'Pepino para saladas', 20, 'kg'],
            ['Rabanete', 'Rabanete para saladas', 8, 'kg'],
            ['Beterraba', 'Beterraba para saladas', 15, 'kg'],
            ['Palmito Fresco', 'Palmito fresco para saladas', 10, 'kg'],
            
            // QUEIJOS E FRIOS
            ['Queijo Coalho', 'Queijo coalho para grelhar', 15, 'kg'],
            ['Queijo Provolone', 'Queijo provolone para derreter', 10, 'kg'],
            ['Mussarela de Búfala', 'Mussarela de búfala fresca', 8, 'kg'],
            ['Queijo Brie', 'Queijo brie para acompanhar', 5, 'kg'],
            ['Salame', 'Salame italiano fatiado', 8, 'kg'],
            ['Presunto Parma', 'Presunto parma fatiado', 6, 'kg'],
            ['Copa', 'Copa italiana fatiada', 4, 'kg'],
            
            // BEBIDAS PARA ROTISSERIA
            ['Cerveja para Cozinhar', 'Cerveja para marinadas e cozimento', 50, 'garrafas'],
            ['Cachaça', 'Cachaça para flambar', 10, 'garrafas'],
            ['Conhaque', 'Conhaque para flambar', 5, 'garrafas'],
            ['Suco de Limão', 'Suco de limão natural', 20, 'litros'],
            ['Água de Coco', 'Água de coco natural', 30, 'litros'],
            
            // SOBREMESAS PARA ROTISSERIA
            ['Abacaxi para Grelhar', 'Abacaxi em fatias para grelhar', 20, 'unidades'],
            ['Banana para Assar', 'Banana prata para assar', 30, 'kg'],
            ['Pêssego para Grelhar', 'Pêssego em calda para grelhar', 25, 'latas'],
            ['Canela em Pau', 'Canela em pau para sobremesas', 2, 'kg'],
            ['Açúcar Mascavo', 'Açúcar mascavo para caramelizar', 15, 'kg'],
            
            // UTENSÍLIOS E CONSUMÍVEIS
            ['Papel Alumínio', 'Papel alumínio para embrulhar', 20, 'rolos'],
            ['Papel Manteiga', 'Papel manteiga para assar', 15, 'rolos'],
            ['Espetos de Madeira', 'Espetos de madeira para espetinhos', 100, 'pacotes'],
            ['Espetos de Metal', 'Espetos de metal reutilizáveis', 50, 'unidades'],
            ['Carvão Vegetal', 'Carvão vegetal para churrasqueira', 100, 'kg'],
            ['Lenha Seca', 'Lenha seca para forno a lenha', 200, 'kg'],
            ['Álcool Gel Acendedor', 'Álcool gel para acender fogo', 20, 'frascos'],
            
            // CONDIMENTOS ESPECIAIS
            ['Chimichurri', 'Molho chimichurri argentino', 15, 'potes'],
            ['Pesto', 'Molho pesto de manjericão', 12, 'potes'],
            ['Tahine', 'Pasta de gergelim tahine', 8, 'potes'],
            ['Harissa', 'Pasta de pimenta harissa', 5, 'potes'],
            ['Molho de Hortelã', 'Molho de hortelã para cordeiro', 10, 'potes']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO items (name, description, quantity, unit) VALUES (?, ?, ?, ?)");
        $addedCount = 0;
        
        foreach ($rotisserieProducts as $product) {
            // Verificar se o produto já existe
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM items WHERE name = ?");
            $checkStmt->execute([$product[0]]);
            
            if ($checkStmt->fetchColumn() == 0) {
                $stmt->execute($product);
                $addedCount++;
            }
        }
        
        $message = "Foram adicionados $addedCount novos produtos de rotisseria ao estoque!";
        
    } catch (PDOException $e) {
        $error = 'Erro ao adicionar produtos de rotisseria: ' . $e->getMessage();
    }
}

// Contar produtos atuais
$stmt = $pdo->query("SELECT COUNT(*) FROM items");
$currentCount = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Adicionar Produtos de Rotisseria - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <h2>🍖 Adicionar Produtos de Rotisseria</h2>
        
        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-header">
                <h5>Produtos Especializados para Rotisseria</h5>
            </div>
            <div class="card-body">
                <p><strong>Produtos atualmente cadastrados:</strong> <?php echo $currentCount; ?></p>
                
                <p>Este script adiciona <strong>80+ produtos especializados</strong> para rotisseria organizados nas seguintes categorias:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <ul>
                            <li><strong>🥩 Carnes para Assados:</strong> Frango inteiro, pernil, costela, picanha, cordeiro</li>
                            <li><strong>🧂 Temperos Especiais:</strong> Temperos específicos, sal grosso, páprica defumada</li>
                            <li><strong>🍯 Molhos e Glazes:</strong> Barbecue, teriyaki, mel, molho inglês</li>
                            <li><strong>🥔 Acompanhamentos:</strong> Batatas especiais, milho, vegetais para grelhar</li>
                            <li><strong>🍞 Pães Especiais:</strong> Pão de alho, sírio, hambúrguer artesanal</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul>
                            <li><strong>🥗 Saladas Gourmet:</strong> Mix de folhas, tomate cereja, palmito fresco</li>
                            <li><strong>🧀 Queijos e Frios:</strong> Coalho, provolone, presunto parma</li>
                            <li><strong>🍺 Bebidas Especiais:</strong> Cerveja para cozinhar, cachaça, vinhos</li>
                            <li><strong>🍍 Sobremesas:</strong> Frutas para grelhar, canela, açúcar mascavo</li>
                            <li><strong>🔥 Utensílios:</strong> Papel alumínio, espetos, carvão, lenha</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <strong>💡 Dica:</strong> Estes produtos são ideais para estabelecimentos que trabalham com:
                    <ul class="mb-0 mt-2">
                        <li>Frango assado e rotisseria</li>
                        <li>Churrascaria e grelhados</li>
                        <li>Restaurante com forno a lenha</li>
                        <li>Buffet de carnes assadas</li>
                        <li>Food truck de assados</li>
                    </ul>
                </div>
                
                <form method="post">
                    <button type="submit" name="add_rotisserie_products" class="btn btn-primary btn-lg" 
                            onclick="return confirm('Deseja adicionar todos os produtos de rotisseria ao estoque?')">
                        🍖 Adicionar Produtos de Rotisseria
                    </button>
                </form>
                
                <div class="mt-3">
                    <a href="manage_items.php" class="btn btn-secondary">Voltar para Gerenciar Itens</a>
                    <a href="add_kitchen_products.php" class="btn btn-info">Produtos de Cozinha</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
