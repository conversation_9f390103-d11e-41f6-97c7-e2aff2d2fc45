<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

// Contar produtos atuais por categoria
$stmt = $pdo->query("SELECT COUNT(*) FROM items");
$totalItems = $stmt->fetchColumn();

// Verificar se existem usuários
$stmt = $pdo->query("SELECT COUNT(*) FROM users");
$totalUsers = $stmt->fetchColumn();

// Verificar se existe admin
$stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
$stmt->execute();
$totalAdmins = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Configuração de Produtos - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        .setup-card {
            transition: transform 0.2s;
        }
        .setup-card:hover {
            transform: translateY(-5px);
        }
        .category-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h1>🛠️ Configuração de Produtos</h1>
                <p class="lead">Configure seu estoque com produtos organizados por categoria</p>
            </div>
            <div class="col-md-4 text-right">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6>Status Atual</h6>
                        <p class="mb-1"><strong>Produtos:</strong> <?php echo $totalItems; ?></p>
                        <p class="mb-1"><strong>Usuários:</strong> <?php echo $totalUsers; ?></p>
                        <p class="mb-0"><strong>Admins:</strong> <?php echo $totalAdmins; ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <!-- Configuração Inicial -->
            <div class="col-md-6 mb-4">
                <div class="card setup-card h-100">
                    <div class="card-body text-center">
                        <div class="category-icon">⚙️</div>
                        <h5 class="card-title">Configuração Inicial</h5>
                        <p class="card-text">Configure usuários e produtos básicos para começar</p>
                        <div class="btn-group-vertical w-100">
                            <a href="system_check.php" class="btn btn-info mb-2">
                                🔍 Verificar Sistema
                            </a>
                            <a href="populate_database.php" class="btn btn-success">
                                🚀 Configuração Completa (80+ produtos)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Produtos de Cozinha -->
            <div class="col-md-6 mb-4">
                <div class="card setup-card h-100">
                    <div class="card-body text-center">
                        <div class="category-icon">🍳</div>
                        <h5 class="card-title">Produtos de Cozinha</h5>
                        <p class="card-text">Produtos complementares para cozinha geral</p>
                        <div class="btn-group-vertical w-100">
                            <a href="add_kitchen_products.php" class="btn btn-primary">
                                🍳 Adicionar Produtos de Cozinha (50+ itens)
                            </a>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            Congelados, enlatados, panificação, orientais, funcionais
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Produtos de Rotisseria -->
            <div class="col-md-6 mb-4">
                <div class="card setup-card h-100">
                    <div class="card-body text-center">
                        <div class="category-icon">🍖</div>
                        <h5 class="card-title">Produtos de Rotisseria</h5>
                        <p class="card-text">Produtos especializados para rotisseria e churrasco</p>
                        <div class="btn-group-vertical w-100">
                            <a href="add_rotisserie_products.php" class="btn btn-warning">
                                🍖 Adicionar Produtos de Rotisseria (80+ itens)
                            </a>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            Carnes especiais, temperos, molhos, utensílios, carvão
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Gerenciamento -->
            <div class="col-md-6 mb-4">
                <div class="card setup-card h-100">
                    <div class="card-body text-center">
                        <div class="category-icon">📋</div>
                        <h5 class="card-title">Gerenciamento</h5>
                        <p class="card-text">Gerencie produtos e usuários do sistema</p>
                        <div class="btn-group-vertical w-100">
                            <a href="manage_items.php" class="btn btn-secondary mb-2">
                                📦 Gerenciar Itens
                            </a>
                            <a href="manage_users.php" class="btn btn-info mb-2">
                                👥 Gerenciar Usuários
                            </a>
                            <a href="create_admin.php" class="btn btn-outline-secondary">
                                👤 Criar Admin
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Resumo das Categorias -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>📊 Resumo das Categorias de Produtos</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>🍳 Produtos Básicos (80+ itens)</h6>
                        <ul class="small">
                            <li>Grãos e cereais</li>
                            <li>Farinhas e massas</li>
                            <li>Óleos e gorduras</li>
                            <li>Vegetais e frutas</li>
                            <li>Carnes e proteínas</li>
                            <li>Laticínios e ovos</li>
                            <li>Temperos básicos</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>🥘 Produtos Complementares (50+ itens)</h6>
                        <ul class="small">
                            <li>Produtos congelados</li>
                            <li>Enlatados e conservas</li>
                            <li>Produtos para panificação</li>
                            <li>Produtos orientais</li>
                            <li>Produtos funcionais</li>
                            <li>Sobremesas</li>
                            <li>Diet/Light</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>🍖 Produtos de Rotisseria (80+ itens)</h6>
                        <ul class="small">
                            <li>Carnes para assados</li>
                            <li>Temperos especializados</li>
                            <li>Molhos e glazes</li>
                            <li>Queijos e frios gourmet</li>
                            <li>Utensílios e consumíveis</li>
                            <li>Carvão e lenha</li>
                            <li>Bebidas especiais</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Dicas de Uso -->
        <div class="alert alert-info mt-4">
            <h6>💡 Dicas de Configuração:</h6>
            <ol class="mb-0">
                <li><strong>Primeira vez:</strong> Execute "Configuração Completa" para ter uma base sólida</li>
                <li><strong>Cozinha geral:</strong> Adicione produtos complementares conforme necessário</li>
                <li><strong>Rotisseria/Churrasco:</strong> Use produtos especializados para esse segmento</li>
                <li><strong>Personalização:</strong> Use "Gerenciar Itens" para adicionar produtos específicos</li>
                <li><strong>Manutenção:</strong> Execute "Verificar Sistema" periodicamente</li>
            </ol>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-outline-primary">🏠 Voltar ao Início</a>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
