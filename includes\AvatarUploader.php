<?php
/**
 * Classe para gerenciar upload de avatars
 * Sistema de Requisição de Material de Cozinha
 */

class AvatarUploader {
    private $uploadDir;
    private $allowedTypes;
    private $maxFileSize;
    private $imageQuality;
    private $avatarSizes;
    private $pdo;

    public function __construct($pdo = null) {
        // Tentar obter conexão PDO
        if ($pdo !== null) {
            $this->pdo = $pdo;
        } else {
            $this->initializePDO();
        }
        $this->uploadDir = 'assets/uploads/avatars/';
        $this->allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $this->maxFileSize = 5 * 1024 * 1024; // 5MB
        $this->imageQuality = 85;
        $this->avatarSizes = [
            'original' => null, // Tamanho original (máximo 800x800)
            'large' => 200,     // Para perfil
            'medium' => 100,    // Para header
            'small' => 50,      // Para comentários/listas
            'thumb' => 32       // Para mini avatars
        ];
        
        // Criar diretório se não existir
        $this->createUploadDirectory();
    }

    /**
     * Inicializar conexão PDO
     */
    private function initializePDO() {
        global $pdo;

        // Tentar usar PDO global
        if (isset($pdo) && $pdo !== null) {
            $this->pdo = $pdo;
            return;
        }

        // Tentar incluir arquivo de conexão
        if (file_exists(__DIR__ . '/../config/db_connect.php')) {
            require_once __DIR__ . '/../config/db_connect.php';
            if (isset($pdo) && $pdo !== null) {
                $this->pdo = $pdo;
                return;
            }
        }

        // Se não conseguir, deixar null (métodos irão tratar)
        $this->pdo = null;
    }
    
    /**
     * Criar diretório de upload se não existir
     */
    private function createUploadDirectory() {
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
        
        // Criar subdiretórios para cada tamanho
        foreach ($this->avatarSizes as $size => $dimension) {
            $sizeDir = $this->uploadDir . $size . '/';
            if (!file_exists($sizeDir)) {
                mkdir($sizeDir, 0755, true);
            }
        }
    }
    
    /**
     * Validar arquivo de upload
     */
    private function validateFile($file) {
        $errors = [];
        
        // Verificar se houve erro no upload
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'Erro no upload do arquivo.';
            return $errors;
        }
        
        // Verificar tamanho do arquivo
        if ($file['size'] > $this->maxFileSize) {
            $errors[] = 'Arquivo muito grande. Máximo permitido: ' . $this->formatBytes($this->maxFileSize);
        }
        
        // Verificar tipo MIME
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $this->allowedTypes)) {
            $errors[] = 'Tipo de arquivo não permitido. Use: JPG, PNG, GIF ou WebP.';
        }
        
        // Verificar se é realmente uma imagem
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            $errors[] = 'Arquivo não é uma imagem válida.';
        }
        
        // Verificar dimensões mínimas
        if ($imageInfo && ($imageInfo[0] < 50 || $imageInfo[1] < 50)) {
            $errors[] = 'Imagem muito pequena. Mínimo: 50x50 pixels.';
        }
        
        // Verificar dimensões máximas
        if ($imageInfo && ($imageInfo[0] > 2000 || $imageInfo[1] > 2000)) {
            $errors[] = 'Imagem muito grande. Máximo: 2000x2000 pixels.';
        }
        
        return $errors;
    }
    
    /**
     * Processar upload do avatar
     */
    public function uploadAvatar($file, $userId) {
        try {
            // Validar arquivo
            $errors = $this->validateFile($file);
            if (!empty($errors)) {
                return ['success' => false, 'errors' => $errors];
            }
            
            // Gerar nome único para o arquivo
            $extension = $this->getImageExtension($file['tmp_name']);
            $filename = 'avatar_' . $userId . '_' . time() . '.' . $extension;
            
            // Carregar imagem original
            $sourceImage = $this->createImageFromFile($file['tmp_name']);
            if (!$sourceImage) {
                return ['success' => false, 'errors' => ['Não foi possível processar a imagem.']];
            }
            
            // Obter dimensões originais
            list($originalWidth, $originalHeight) = getimagesize($file['tmp_name']);
            
            // Processar e salvar em diferentes tamanhos
            $savedFiles = [];
            foreach ($this->avatarSizes as $sizeName => $dimension) {
                if ($sizeName === 'original') {
                    // Para original, redimensionar apenas se for muito grande
                    $maxDimension = 800;
                    if ($originalWidth > $maxDimension || $originalHeight > $maxDimension) {
                        $newDimensions = $this->calculateDimensions($originalWidth, $originalHeight, $maxDimension);
                        $resizedImage = $this->resizeImage($sourceImage, $originalWidth, $originalHeight, $newDimensions['width'], $newDimensions['height']);
                    } else {
                        $resizedImage = $sourceImage;
                    }
                } else {
                    // Para outros tamanhos, criar versões redimensionadas
                    $newDimensions = $this->calculateDimensions($originalWidth, $originalHeight, $dimension);
                    $resizedImage = $this->resizeImage($sourceImage, $originalWidth, $originalHeight, $newDimensions['width'], $newDimensions['height']);
                }
                
                // Salvar arquivo
                $filePath = $this->uploadDir . $sizeName . '/' . $filename;
                if ($this->saveImage($resizedImage, $filePath, $extension)) {
                    $savedFiles[$sizeName] = $sizeName . '/' . $filename;
                }
                
                // Liberar memória
                if ($resizedImage !== $sourceImage) {
                    imagedestroy($resizedImage);
                }
            }
            
            // Liberar memória da imagem original
            imagedestroy($sourceImage);
            
            // Registrar upload no banco de dados
            $this->logAvatarUpload($userId, $file['name'], $filename, $file['size'], $file['type']);
            
            return [
                'success' => true,
                'filename' => $filename,
                'files' => $savedFiles,
                'message' => 'Avatar enviado com sucesso!'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'errors' => ['Erro interno: ' . $e->getMessage()]
            ];
        }
    }
    
    /**
     * Criar imagem a partir do arquivo
     */
    private function createImageFromFile($filePath) {
        $imageInfo = getimagesize($filePath);
        
        switch ($imageInfo['mime']) {
            case 'image/jpeg':
                return imagecreatefromjpeg($filePath);
            case 'image/png':
                return imagecreatefrompng($filePath);
            case 'image/gif':
                return imagecreatefromgif($filePath);
            case 'image/webp':
                return imagecreatefromwebp($filePath);
            default:
                return false;
        }
    }
    
    /**
     * Obter extensão da imagem
     */
    private function getImageExtension($filePath) {
        $imageInfo = getimagesize($filePath);
        
        switch ($imageInfo['mime']) {
            case 'image/jpeg':
                return 'jpg';
            case 'image/png':
                return 'png';
            case 'image/gif':
                return 'gif';
            case 'image/webp':
                return 'webp';
            default:
                return 'jpg';
        }
    }
    
    /**
     * Calcular novas dimensões mantendo proporção
     */
    private function calculateDimensions($originalWidth, $originalHeight, $maxDimension) {
        $ratio = min($maxDimension / $originalWidth, $maxDimension / $originalHeight);
        
        return [
            'width' => round($originalWidth * $ratio),
            'height' => round($originalHeight * $ratio)
        ];
    }
    
    /**
     * Redimensionar imagem
     */
    private function resizeImage($sourceImage, $sourceWidth, $sourceHeight, $newWidth, $newHeight) {
        $newImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preservar transparência para PNG e GIF
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefill($newImage, 0, 0, $transparent);
        
        // Redimensionar
        imagecopyresampled(
            $newImage, $sourceImage,
            0, 0, 0, 0,
            $newWidth, $newHeight,
            $sourceWidth, $sourceHeight
        );
        
        return $newImage;
    }
    
    /**
     * Salvar imagem
     */
    private function saveImage($image, $filePath, $extension) {
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                return imagejpeg($image, $filePath, $this->imageQuality);
            case 'png':
                return imagepng($image, $filePath, 9);
            case 'gif':
                return imagegif($image, $filePath);
            case 'webp':
                return imagewebp($image, $filePath, $this->imageQuality);
            default:
                return false;
        }
    }
    
    /**
     * Registrar upload no banco de dados
     */
    private function logAvatarUpload($userId, $originalFilename, $storedFilename, $fileSize, $mimeType) {
        // Verificar se PDO está disponível
        if ($this->pdo === null) {
            error_log("Erro: Conexão PDO não disponível para registrar upload de avatar");
            return;
        }

        try {
            // Desativar avatars anteriores
            $stmt = $this->pdo->prepare("UPDATE avatar_uploads SET is_active = FALSE WHERE user_id = ?");
            $stmt->execute([$userId]);

            // Registrar novo upload
            $stmt = $this->pdo->prepare("
                INSERT INTO avatar_uploads (user_id, original_filename, stored_filename, file_size, mime_type, is_active)
                VALUES (?, ?, ?, ?, ?, TRUE)
            ");
            $stmt->execute([$userId, $originalFilename, $storedFilename, $fileSize, $mimeType]);

        } catch (PDOException $e) {
            error_log("Erro ao registrar upload de avatar: " . $e->getMessage());
        }
    }
    
    /**
     * Obter URL do avatar do usuário
     */
    public function getAvatarUrl($userId, $size = 'medium') {
        // Verificar se PDO está disponível
        if ($this->pdo === null) {
            return $this->getDefaultAvatar($size);
        }

        try {
            $stmt = $this->pdo->prepare("
                SELECT stored_filename
                FROM avatar_uploads
                WHERE user_id = ? AND is_active = TRUE
                ORDER BY upload_date DESC
                LIMIT 1
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();

            if ($result) {
                return $this->uploadDir . $size . '/' . $result['stored_filename'];
            }

            // Retornar avatar padrão
            return $this->getDefaultAvatar($size);

        } catch (PDOException $e) {
            return $this->getDefaultAvatar($size);
        }
    }
    
    /**
     * Obter avatar padrão
     */
    private function getDefaultAvatar($size) {
        // Retornar null para usar ícone CSS padrão
        return null;
    }
    
    /**
     * Remover avatar do usuário
     */
    public function removeAvatar($userId) {
        global $pdo;

        // Verificar se $pdo está disponível
        if (!isset($pdo) || $pdo === null) {
            // Tentar incluir conexão do banco
            if (file_exists(__DIR__ . '/../config/db_connect.php')) {
                require_once __DIR__ . '/../config/db_connect.php';
            }

            // Se ainda não tiver conexão, retornar erro
            if (!isset($pdo) || $pdo === null) {
                return ['success' => false, 'errors' => ['Erro: Conexão com banco de dados não disponível']];
            }
        }

        try {
            // Obter arquivos para deletar
            $stmt = $pdo->prepare("
                SELECT stored_filename
                FROM avatar_uploads
                WHERE user_id = ? AND is_active = TRUE
            ");
            $stmt->execute([$userId]);
            $files = $stmt->fetchAll();

            // Deletar arquivos físicos
            foreach ($files as $file) {
                foreach ($this->avatarSizes as $sizeName => $dimension) {
                    $filePath = $this->uploadDir . $sizeName . '/' . $file['stored_filename'];
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }
            }

            // Marcar como inativo no banco
            $stmt = $pdo->prepare("UPDATE avatar_uploads SET is_active = FALSE WHERE user_id = ?");
            $stmt->execute([$userId]);

            return ['success' => true, 'message' => 'Avatar removido com sucesso!'];

        } catch (Exception $e) {
            return ['success' => false, 'errors' => ['Erro ao remover avatar: ' . $e->getMessage()]];
        }
    }
    
    /**
     * Formatar bytes para exibição
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
