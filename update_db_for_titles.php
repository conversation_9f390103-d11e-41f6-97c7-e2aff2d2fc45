<?php
require_once 'config/db_connect.php';

echo "<h2>Atualizando banco de dados para suportar títulos de requisições...</h2>";

try {
    // Verificar se a coluna 'title' já existe
    $stmt = $pdo->query("SHOW COLUMNS FROM requests LIKE 'title'");
    $columnExists = $stmt->rowCount() > 0;
    
    if (!$columnExists) {
        // Adicionar coluna title
        $pdo->exec("ALTER TABLE requests ADD COLUMN title VARCHAR(255) DEFAULT NULL AFTER user_id");
        echo "<p>✅ Coluna 'title' adicionada à tabela 'requests'</p>";
        
        // Atualizar requisições existentes com títulos padrão
        $stmt = $pdo->query("UPDATE requests SET title = CONCAT('Requisição #', id) WHERE title IS NULL");
        $updatedRows = $stmt->rowCount();
        echo "<p>✅ $updatedRows requisições existentes atualizadas com títulos padrão</p>";
    } else {
        echo "<p>ℹ️ Coluna 'title' já existe na tabela 'requests'</p>";
    }
    
    // Verificar se há requisições sem título
    $stmt = $pdo->query("SELECT COUNT(*) FROM requests WHERE title IS NULL OR title = ''");
    $nullTitles = $stmt->fetchColumn();
    
    if ($nullTitles > 0) {
        $pdo->exec("UPDATE requests SET title = CONCAT('Requisição #', id) WHERE title IS NULL OR title = ''");
        echo "<p>✅ $nullTitles requisições sem título foram atualizadas</p>";
    }
    
    echo "<h3>✅ Banco de dados atualizado com sucesso!</h3>";
    echo "<p><a href='request_form.php' class='btn btn-primary'>Testar Nova Requisição</a></p>";
    echo "<p><a href='my_requests.php' class='btn btn-secondary'>Ver Minhas Requisições</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Erro ao atualizar banco de dados: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Atualização do Banco de Dados</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-4">
        <!-- Conteúdo já foi exibido acima -->
    </div>
</body>
</html>
