/**
 * Componentes CSS Reutilizáveis
 * Sistema de Requisição de Material de Cozinha
 * Seguindo padrões W3C para separação de tecnologias
 */

/* ==========================================================================
   COMPONENTES DE LOADING
   ========================================================================== */

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ==========================================================================
   COMPONENTES DE STATUS
   ========================================================================== */

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge--success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge--warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge--danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge--info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* ==========================================================================
   COMPONENTES DE FORMULÁRIO
   ========================================================================== */

.form-group--enhanced {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-label--required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-control--with-icon {
    padding-left: 2.5rem;
}

.form-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 4;
}

.form-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
}

.form-feedback--success {
    color: #28a745;
}

.form-feedback--error {
    color: #dc3545;
}

.form-feedback--warning {
    color: #ffc107;
}

/* ==========================================================================
   COMPONENTES DE TABELA
   ========================================================================== */

.table--enhanced {
    border-collapse: separate;
    border-spacing: 0;
}

.table--enhanced th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem 0.75rem;
}

.table--enhanced td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

.table--enhanced tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.15s ease-in-out;
}

/* ==========================================================================
   COMPONENTES DE CARD
   ========================================================================== */

.card--enhanced {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card--enhanced:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header--gradient {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom: none;
}

.card-header--gradient h5,
.card-header--gradient h6 {
    color: white;
    margin-bottom: 0;
}

/* ==========================================================================
   COMPONENTES DE BOTÃO
   ========================================================================== */

.btn--enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn--enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn--enhanced:hover::before {
    left: 100%;
}

.btn-group--vertical-mobile {
    flex-direction: row;
}

@media (max-width: 576px) {
    .btn-group--vertical-mobile {
        flex-direction: column;
    }
    
    .btn-group--vertical-mobile .btn {
        margin-bottom: 0.5rem;
        border-radius: 0.25rem !important;
    }
}

/* ==========================================================================
   COMPONENTES DE MODAL
   ========================================================================== */

.modal--enhanced .modal-content {
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal--enhanced .modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
}

.modal--enhanced .modal-body {
    padding: 1.5rem;
}

.modal--enhanced .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1.5rem;
}

/* ==========================================================================
   COMPONENTES DE CÓDIGO DE BARRAS
   ========================================================================== */

.barcode-container {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.barcode-number {
    font-family: 'Courier New', monospace;
    font-size: 1.25rem;
    font-weight: bold;
    letter-spacing: 0.1em;
    margin: 1rem 0;
    color: #212529;
}

.barcode-visual {
    background: repeating-linear-gradient(
        90deg,
        #000 0px,
        #000 2px,
        #fff 2px,
        #fff 4px
    );
    height: 60px;
    margin: 1rem 0;
}

/* ==========================================================================
   COMPONENTES DE VALIDAÇÃO
   ========================================================================== */

.validation-container {
    position: relative;
}

.validation-message {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 10;
    margin-top: 0.25rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    animation: slideInDown 0.3s ease-out;
}

.validation-message--success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.validation-message--error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.validation-message--warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==========================================================================
   COMPONENTES DE SUGESTÃO
   ========================================================================== */

.suggestions-container {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-top: 0.25rem;
    padding: 0.75rem;
    animation: fadeInUp 0.3s ease-out;
}

.suggestion-item {
    display: inline-block;
    margin: 0.125rem;
    padding: 0.25rem 0.5rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-item:hover {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
    transform: translateY(-1px);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==========================================================================
   UTILITÁRIOS DE ACESSIBILIDADE
   ========================================================================== */

.focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #007bff;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 0;
}
