<?php
// Exportação compacta para DOCX
if (!isset($exportData)) {
    exit('Dados de exportação não encontrados');
}

$request = $exportData['request'];
$items = $exportData['items'];
$statusLabel = $exportData['status_label'];

// Nome do arquivo
$requestName = $request['title'] ?? 'Requisicao_' . $request['id'];
$safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $requestName);
$filename = $safeName . '_' . date('Y-m-d_H-i-s') . '.docx';

// Calcular totais
$totalItems = count($items);
$totalQuantity = array_sum(array_column($items, 'quantity'));

// Verificar se a biblioteca PhpWord está disponível
if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
    // Fallback: gerar RTF compacto
    generateCompactRTF($request, $items, $statusLabel, $filename, $totalItems, $totalQuantity);
    exit;
}

try {
    // Usar PhpWord se disponível
    $phpWord = new \PhpOffice\PhpWord\PhpWord();
    $phpWord->getSettings()->setThemeFontLang(new \PhpOffice\PhpWord\Style\Language('pt-BR'));

    // Definir estilos compactos
    $phpWord->addTitleStyle(1, ['name' => 'Arial', 'size' => 16, 'bold' => true, 'color' => '0066CC']);
    $phpWord->addTitleStyle(2, ['name' => 'Arial', 'size' => 12, 'bold' => true, 'color' => '333333']);

    // Adicionar seção com margens mínimas
    $section = $phpWord->addSection([
        'marginTop' => 576,    // 0.4 inch
        'marginBottom' => 576,
        'marginLeft' => 576,
        'marginRight' => 576
    ]);

    // Cabeçalho compacto
    $section->addText('🍽️ REQUISIÇÃO DE MATERIAL',
        ['name' => 'Arial', 'size' => 16, 'bold' => true, 'color' => '0066CC'],
        ['alignment' => 'center']);
    $section->addText($request['title'] ?? 'Requisição #' . $request['id'],
        ['name' => 'Arial', 'size' => 14, 'bold' => true, 'color' => '333333'],
        ['alignment' => 'center']);
    $section->addTextBreak(1);

    // Seção: Informações Gerais
    $section->addText('📋 INFORMAÇÕES GERAIS',
        ['name' => 'Arial', 'size' => 11, 'bold' => true, 'color' => '0066CC']);
    $section->addTextBreak(0.5);

    // Informações essenciais em tabela compacta
    $infoTable = $section->addTable(['borderSize' => 4, 'borderColor' => 'CCCCCC']);
    
    // Linha 1
    $infoTable->addRow();
    $infoTable->addCell(1800)->addText('ID:', ['bold' => true, 'size' => 9]);
    $infoTable->addCell(1800)->addText('#' . $request['id'], ['color' => '0066CC', 'bold' => true, 'size' => 9]);
    $infoTable->addCell(1800)->addText('Solicitante:', ['bold' => true, 'size' => 9]);
    $infoTable->addCell(3600)->addText($request['username'], ['color' => '0066CC', 'bold' => true, 'size' => 9]);

    // Linha 2
    $infoTable->addRow();
    $infoTable->addCell(1800)->addText('Data:', ['bold' => true, 'size' => 9]);
    $infoTable->addCell(1800)->addText(date('d/m/Y H:i', strtotime($request['request_date'])), ['size' => 9]);
    $infoTable->addCell(1800)->addText('Status:', ['bold' => true, 'size' => 9]);
    $infoTable->addCell(3600)->addText(strtoupper($statusLabel), ['bold' => true, 'color' => '0066CC', 'size' => 9]);

    // Adicionar campos extras se existirem
    if (!empty($request['priority']) || !empty($request['department'])) {
        $infoTable->addRow();
        if (!empty($request['priority'])) {
            $priorityLabels = ['low' => 'Baixa', 'medium' => 'Média', 'high' => 'Alta', 'urgent' => 'URGENTE'];
            $priorityLabel = $priorityLabels[$request['priority']] ?? ucfirst($request['priority']);
            $priorityColor = ($request['priority'] == 'urgent') ? 'DC3545' : '0066CC';
            $infoTable->addCell(1800)->addText('Prioridade:', ['bold' => true, 'size' => 9]);
            $infoTable->addCell(1800)->addText($priorityLabel, ['color' => $priorityColor, 'bold' => true, 'size' => 9]);
        } else {
            $infoTable->addCell(1800)->addText('');
            $infoTable->addCell(1800)->addText('');
        }
        
        if (!empty($request['department'])) {
            $infoTable->addCell(1800)->addText('Departamento:', ['bold' => true, 'size' => 9]);
            $infoTable->addCell(3600)->addText($request['department'], ['size' => 9]);
        } else {
            $infoTable->addCell(1800)->addText('');
            $infoTable->addCell(3600)->addText('');
        }
    }

    $section->addTextBreak(1);

    // Resumo compacto
    $section->addText('RESUMO: ' . $totalItems . ' tipos de itens • ' . $totalQuantity . ' unidades totais • Gerado em ' . date('d/m/Y H:i'),
        ['size' => 9, 'bold' => true, 'color' => '666666'],
        ['alignment' => 'left']);
    $section->addTextBreak(1);

    // Seção: Observações Gerais
    $section->addText('📝 OBSERVAÇÕES GERAIS',
        ['name' => 'Arial', 'size' => 11, 'bold' => true, 'color' => '28A745']);
    $section->addTextBreak(0.5);

    if (!empty($request['notes'])) {
        $section->addText('Observações da Requisição:',
            ['size' => 9, 'bold' => true, 'color' => '333333']);
        $section->addText($request['notes'],
            ['size' => 9, 'color' => '666666']);
        $section->addTextBreak(1);
    } else {
        // Criar tabela para observações em branco
        $obsTable = $section->addTable(['borderSize' => 4, 'borderColor' => 'CCCCCC']);
        $obsTable->addRow(600);
        $obsCell = $obsTable->addCell(9000, ['bgColor' => 'F8F9FA']);
        $obsCell->addText('Espaço para observações adicionais:', ['size' => 8, 'color' => '999999']);
        $obsCell->addTextBreak(2);
        $section->addTextBreak(1);
    }

    // Tabela de itens compacta
    $itemsTable = $section->addTable([
        'borderSize' => 4,
        'borderColor' => '0066CC',
        'width' => 100,
        'unit' => 'pct'
    ]);

    // Cabeçalho da tabela
    $itemsTable->addRow(400);
    $itemsTable->addCell(600, ['bgColor' => '0066CC'])->addText('#', ['bold' => true, 'color' => 'FFFFFF', 'size' => 8], ['alignment' => 'center']);
    $itemsTable->addCell(3000, ['bgColor' => '0066CC'])->addText('Item', ['bold' => true, 'color' => 'FFFFFF', 'size' => 8], ['alignment' => 'center']);
    $itemsTable->addCell(3500, ['bgColor' => '0066CC'])->addText('Descrição', ['bold' => true, 'color' => 'FFFFFF', 'size' => 8], ['alignment' => 'center']);
    $itemsTable->addCell(800, ['bgColor' => '0066CC'])->addText('Qtd', ['bold' => true, 'color' => 'FFFFFF', 'size' => 8], ['alignment' => 'center']);
    $itemsTable->addCell(700, ['bgColor' => '0066CC'])->addText('Un', ['bold' => true, 'color' => 'FFFFFF', 'size' => 8], ['alignment' => 'center']);

    // Dados dos itens
    $itemNumber = 1;
    foreach ($items as $item) {
        $rowColor = ($itemNumber % 2 == 0) ? 'F8F9FA' : 'FFFFFF';
        
        $itemsTable->addRow();
        $itemsTable->addCell(600, ['bgColor' => $rowColor])->addText($itemNumber, ['bold' => true, 'color' => '0066CC', 'size' => 8], ['alignment' => 'center']);
        $itemsTable->addCell(3000, ['bgColor' => $rowColor])->addText($item['name'], ['bold' => true, 'size' => 8]);
        
        // Truncar descrição se muito longa
        $description = strlen($item['description']) > 60 ? substr($item['description'], 0, 60) . '...' : $item['description'];
        $itemsTable->addCell(3500, ['bgColor' => $rowColor])->addText($description, ['size' => 8]);
        
        $itemsTable->addCell(800, ['bgColor' => $rowColor])->addText($item['quantity'], ['bold' => true, 'size' => 8], ['alignment' => 'center']);
        $itemsTable->addCell(700, ['bgColor' => $rowColor])->addText($item['unit'], ['size' => 8], ['alignment' => 'center']);

        $itemNumber++;
    }

    $section->addTextBreak(1);

    // Assinaturas compactas
    $signatureTable = $section->addTable(['borderSize' => 4, 'borderColor' => '0066CC']);
    
    // Cabeçalhos
    $signatureTable->addRow();
    $signatureTable->addCell(3000, ['bgColor' => 'E3F2FD'])->addText('SOLICITANTE', ['bold' => true, 'size' => 9], ['alignment' => 'center']);
    $signatureTable->addCell(3000, ['bgColor' => 'E8F5E8'])->addText('APROVAÇÃO', ['bold' => true, 'size' => 9], ['alignment' => 'center']);
    $signatureTable->addCell(3000, ['bgColor' => 'FFF3E0'])->addText('ENTREGA', ['bold' => true, 'size' => 9], ['alignment' => 'center']);

    // Dados
    $signatureTable->addRow(800);
    $cell1 = $signatureTable->addCell(3000);
    $cell1->addText($request['username'], ['bold' => true, 'size' => 9], ['alignment' => 'center']);
    $cell1->addText('_________________', ['size' => 8], ['alignment' => 'center']);
    $cell1->addText(date('d/m/Y', strtotime($request['request_date'])), ['size' => 8], ['alignment' => 'center']);

    $cell2 = $signatureTable->addCell(3000);
    $cell2->addText('_________________', ['size' => 8], ['alignment' => 'center']);
    $cell2->addText('___/___/______', ['size' => 8], ['alignment' => 'center']);

    $cell3 = $signatureTable->addCell(3000);
    $cell3->addText('_________________', ['size' => 8], ['alignment' => 'center']);
    $cell3->addText('___/___/______', ['size' => 8], ['alignment' => 'center']);

    $section->addTextBreak(1);

    // Rodapé compacto
    $section->addText('Sistema de Requisição • Documento gerado em ' . date('d/m/Y H:i') . ' • ID: #' . $request['id'] . ' • Status: ' . strtoupper($statusLabel),
        ['size' => 7, 'color' => '666666'],
        ['alignment' => 'center']);
    
    // Salvar arquivo
    $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    
    // Headers para download
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $objWriter->save('php://output');
    exit;
    
} catch (Exception $e) {
    // Fallback para RTF compacto
    generateCompactRTF($request, $items, $statusLabel, $filename, $totalItems, $totalQuantity);
    exit;
}

function generateCompactRTF($request, $items, $statusLabel, $filename, $totalItems, $totalQuantity) {
    // Gerar RTF compacto
    $rtfFilename = str_replace('.docx', '.rtf', $filename);

    header('Content-Type: application/rtf');
    header('Content-Disposition: attachment; filename="' . $rtfFilename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    echo '{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Arial;}}';
    echo '{\\colortbl;\\red0\\green102\\blue204;\\red220\\green53\\blue69;}';
    echo '\\f0\\fs20';

    // Cabeçalho compacto
    echo '{\\b\\fs28\\qc\\cf1 REQUISIÇÃO DE MATERIAL}\\par';
    echo '{\\b\\fs22\\qc ' . ($request['title'] ?? 'Requisição #' . $request['id']) . '}\\par\\par';

    // Seção: Informações Gerais
    echo '{\\b\\fs20\\cf1 INFORMAÇÕES GERAIS}\\par';
    echo '\\par';

    // Informações essenciais
    echo '{\\b ID:} #' . $request['id'] . '\\tab {\\b Solicitante:} ' . $request['username'] . '\\par';
    echo '{\\b Data:} ' . date('d/m/Y H:i', strtotime($request['request_date'])) . '\\tab {\\b Status:} {\\b\\cf1 ' . strtoupper($statusLabel) . '}\\par';
    
    // Campos extras se existirem
    if (!empty($request['priority']) || !empty($request['department'])) {
        if (!empty($request['priority'])) {
            $priorityLabels = ['low' => 'Baixa', 'medium' => 'Média', 'high' => 'Alta', 'urgent' => 'URGENTE'];
            $priorityLabel = $priorityLabels[$request['priority']] ?? ucfirst($request['priority']);
            echo '{\\b Prioridade:} ' . $priorityLabel . '\\tab ';
        }
        if (!empty($request['department'])) {
            echo '{\\b Departamento:} ' . $request['department'];
        }
        echo '\\par';
    }
    echo '\\par';

    // Resumo
    echo '{\\b RESUMO:} ' . $totalItems . ' tipos de itens • ' . $totalQuantity . ' unidades totais • Gerado em ' . date('d/m/Y H:i') . '\\par\\par';

    // Seção: Observações Gerais
    echo '{\\b\\fs20\\cf1 OBSERVAÇÕES GERAIS}\\par';
    echo '\\par';

    if (!empty($request['notes'])) {
        echo '{\\b Observações da Requisição:}\\par';
        echo $request['notes'] . '\\par\\par';
    } else {
        echo 'Espaço para observações adicionais:\\par';
        echo '_________________________________________________________________\\par';
        echo '_________________________________________________________________\\par';
        echo '_________________________________________________________________\\par\\par';
    }

    // Cabeçalho da tabela
    echo '{\\b #\\tab Item\\tab\\tab Descrição\\tab\\tab Qtd\\tab Un}\\par';

    // Itens
    $itemNumber = 1;
    foreach ($items as $item) {
        $description = strlen($item['description']) > 40 ? substr($item['description'], 0, 40) . '...' : $item['description'];
        echo $itemNumber . '\\tab {\\b ' . $item['name'] . '}\\tab\\tab ' . $description . '\\tab\\tab {\\b ' . $item['quantity'] . '}\\tab ' . $item['unit'] . '\\par';
        $itemNumber++;
    }

    echo '\\par';

    // Assinaturas
    echo '{\\b SOLICITANTE}\\tab\\tab {\\b APROVAÇÃO}\\tab\\tab {\\b ENTREGA}\\par';
    echo $request['username'] . '\\tab\\tab _____________\\tab\\tab _____________\\par';
    echo date('d/m/Y', strtotime($request['request_date'])) . '\\tab\\tab ___/___/___\\tab\\tab ___/___/___\\par\\par';

    // Rodapé
    echo '{\\i\\qc Sistema de Requisição • ' . date('d/m/Y H:i') . ' • ID: #' . $request['id'] . ' • Status: ' . strtoupper($statusLabel) . '}\\par';

    echo '}';
    exit;
}
?>
