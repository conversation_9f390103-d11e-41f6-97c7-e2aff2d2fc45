<?php
/**
 * Configuração padrão para todas as páginas
 * Este arquivo define o layout padrão baseado no index.php
 */

// Configurações padrão para todas as páginas
$default_config = [
    'layout_type' => 'standard', // Tipo de layout padrão
    'show_header' => true,       // Mostrar header
    'show_sidebar' => true,      // Mostrar sidebar
    'show_footer' => true,       // Mostrar footer
    'container_fluid' => false,  // Usar container fluid
    'page_header' => true,       // Mostrar page header
];

// Configurações específicas por página
$page_configs = [
    'index.php' => [
        'page_title' => 'Dashboard',
        'page_subtitle' => 'Visão geral do sistema de requisições',
        'page_icon' => 'fas fa-home',
        'page_header' => true,
    ],
    'profile.php' => [
        'page_title' => 'Meu Perfil',
        'page_subtitle' => 'Gerencie suas informações pessoais e configurações',
        'page_icon' => 'fas fa-user',
        'page_header' => true,
    ],
    'request_form.php' => [
        'page_title' => 'Nova Requisição',
        'page_subtitle' => 'Criar uma nova requisição de material',
        'page_icon' => 'fas fa-plus-circle',
        'page_header' => true,
    ],
    'my_requests.php' => [
        'page_title' => 'Minhas Requisições',
        'page_subtitle' => 'Visualizar e gerenciar suas requisições',
        'page_icon' => 'fas fa-file-alt',
        'page_header' => true,
    ],
    'manage_requests.php' => [
        'page_title' => 'Gerenciar Requisições',
        'page_subtitle' => 'Aprovar e gerenciar todas as requisições',
        'page_icon' => 'fas fa-tasks',
        'page_header' => true,
        'admin_only' => true,
    ],
    'items_list.php' => [
        'page_title' => 'Lista de Itens',
        'page_subtitle' => 'Visualizar todos os itens disponíveis',
        'page_icon' => 'fas fa-list',
        'page_header' => true,
    ],
    'manage_items.php' => [
        'page_title' => 'Gerenciar Itens',
        'page_subtitle' => 'Adicionar e editar itens do sistema',
        'page_icon' => 'fas fa-boxes',
        'page_header' => true,
        'admin_only' => true,
    ],
    'users.php' => [
        'page_title' => 'Usuários',
        'page_subtitle' => 'Gerenciar usuários do sistema',
        'page_icon' => 'fas fa-users',
        'page_header' => true,
        'admin_only' => true,
    ],
    'settings.php' => [
        'page_title' => 'Configurações',
        'page_subtitle' => 'Configurações gerais do sistema',
        'page_icon' => 'fas fa-cog',
        'page_header' => true,
        'admin_only' => true,
    ],
    'reports.php' => [
        'page_title' => 'Relatórios',
        'page_subtitle' => 'Relatórios e estatísticas do sistema',
        'page_icon' => 'fas fa-chart-bar',
        'page_header' => true,
        'admin_only' => true,
    ],
    'search.php' => [
        'page_title' => 'Buscar',
        'page_subtitle' => 'Buscar itens e requisições',
        'page_icon' => 'fas fa-search',
        'page_header' => true,
    ],
    'help.php' => [
        'page_title' => 'Ajuda',
        'page_subtitle' => 'Central de ajuda e documentação',
        'page_icon' => 'fas fa-question-circle',
        'page_header' => true,
    ],
    'login.php' => [
        'page_title' => 'Login',
        'page_subtitle' => 'Acesse sua conta',
        'page_icon' => 'fas fa-sign-in-alt',
        'layout_type' => 'auth',
        'show_header' => false,
        'show_sidebar' => false,
        'show_footer' => false,
        'page_header' => false,
    ],
    'register.php' => [
        'page_title' => 'Cadastro',
        'page_subtitle' => 'Criar nova conta',
        'page_icon' => 'fas fa-user-plus',
        'layout_type' => 'auth',
        'show_header' => false,
        'show_sidebar' => false,
        'show_footer' => false,
        'page_header' => false,
    ],
];

/**
 * Função para obter configuração da página atual
 */
function getPageConfig($page_name = null) {
    global $page_configs, $default_config;
    
    // Se não foi especificado, detectar automaticamente
    if ($page_name === null) {
        $page_name = basename($_SERVER['PHP_SELF']);
    }
    
    // Mesclar configuração padrão com específica da página
    $config = $default_config;
    if (isset($page_configs[$page_name])) {
        $config = array_merge($config, $page_configs[$page_name]);
    }
    
    return $config;
}

/**
 * Função para aplicar configuração da página
 */
function applyPageConfig($page_name = null) {
    $config = getPageConfig($page_name);
    
    // Aplicar configurações globais
    foreach ($config as $key => $value) {
        $GLOBALS[$key] = $value;
    }
    
    // Verificar permissões de admin se necessário
    if (isset($config['admin_only']) && $config['admin_only']) {
        if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
            header('Location: index.php?error=access_denied');
            exit;
        }
    }
}

/**
 * Função para verificar se a página atual está ativa
 */
function isActivePage($page) {
    $current_page = basename($_SERVER['PHP_SELF']);
    return $current_page === $page ? 'active' : '';
}

/**
 * Função para verificar se o usuário é admin
 */
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * Função para gerar breadcrumb automático
 */
function generateBreadcrumb() {
    $current_page = basename($_SERVER['PHP_SELF']);
    $config = getPageConfig($current_page);
    
    $breadcrumb = [
        ['title' => 'Dashboard', 'url' => 'index.php', 'icon' => 'fas fa-home']
    ];
    
    // Se não for a página inicial, adicionar página atual
    if ($current_page !== 'index.php') {
        $breadcrumb[] = [
            'title' => $config['page_title'] ?? 'Página',
            'url' => null, // Página atual não tem link
            'icon' => $config['page_icon'] ?? 'fas fa-file'
        ];
    }
    
    return $breadcrumb;
}

/**
 * Função para renderizar breadcrumb
 */
function renderBreadcrumb() {
    $breadcrumb = generateBreadcrumb();
    
    if (count($breadcrumb) <= 1) {
        return ''; // Não mostrar breadcrumb na página inicial
    }
    
    $html = '<nav aria-label="breadcrumb" class="mb-3">';
    $html .= '<ol class="breadcrumb">';
    
    foreach ($breadcrumb as $index => $item) {
        $isLast = ($index === count($breadcrumb) - 1);
        
        if ($isLast) {
            $html .= '<li class="breadcrumb-item active" aria-current="page">';
            $html .= '<i class="' . $item['icon'] . ' me-1"></i>' . $item['title'];
            $html .= '</li>';
        } else {
            $html .= '<li class="breadcrumb-item">';
            $html .= '<a href="' . $item['url'] . '">';
            $html .= '<i class="' . $item['icon'] . ' me-1"></i>' . $item['title'];
            $html .= '</a>';
            $html .= '</li>';
        }
    }
    
    $html .= '</ol>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * Função para obter meta tags da página
 */
function getPageMetaTags() {
    $config = getPageConfig();
    $current_page = basename($_SERVER['PHP_SELF']);
    
    $meta_tags = [
        'title' => ($config['page_title'] ?? 'Página') . ' - Sistema de Requisição',
        'description' => $config['page_subtitle'] ?? 'Sistema de Requisição de Material de Cozinha',
        'keywords' => 'requisição, material, cozinha, sistema, gestão',
        'author' => 'Sistema de Requisição',
        'robots' => 'noindex, nofollow', // Sistema interno
    ];
    
    return $meta_tags;
}

/**
 * Função para renderizar meta tags
 */
function renderMetaTags() {
    $meta = getPageMetaTags();
    
    $html = '<title>' . htmlspecialchars($meta['title']) . '</title>' . "\n";
    $html .= '<meta name="description" content="' . htmlspecialchars($meta['description']) . '">' . "\n";
    $html .= '<meta name="keywords" content="' . htmlspecialchars($meta['keywords']) . '">' . "\n";
    $html .= '<meta name="author" content="' . htmlspecialchars($meta['author']) . '">' . "\n";
    $html .= '<meta name="robots" content="' . htmlspecialchars($meta['robots']) . '">' . "\n";
    
    return $html;
}

/**
 * Função para inicializar página com configuração automática
 */
function initPage($custom_config = []) {
    // Aplicar configuração da página
    applyPageConfig();
    
    // Aplicar configurações customizadas
    foreach ($custom_config as $key => $value) {
        $GLOBALS[$key] = $value;
    }
    
    // Incluir layout se não foi incluído ainda
    if (!defined('LAYOUT_INCLUDED')) {
        require_once 'includes/layout.php';
        define('LAYOUT_INCLUDED', true);
    }
}

// Auto-aplicar configuração se a página não definiu manualmente
if (!isset($page_config_applied)) {
    applyPageConfig();
    $page_config_applied = true;
}
?>
