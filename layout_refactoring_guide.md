# 🎨 Guia de Refatoração do Layout - Sistema Padronizado

## 🎯 Objetivo da Refatoração

Criar uma **estrutura de layout padronizada** com **header**, **nav**, **content**, **sidebar** e **footer** para todas as páginas do sistema, garantindo:

- ✅ **Consistência visual** em todo o sistema
- ✅ **Navegação intuitiva** e responsiva
- ✅ **Manutenibilidade** simplificada
- ✅ **Experiência do usuário** aprimorada
- ✅ **Código reutilizável** e organizado

## 🏗️ Estrutura do Novo Layout

### **📁 Arquivos Criados:**

#### **1. includes/layout.php**
- **Função:** Layout base com header, nav e sidebar
- **Conteúdo:**
  - ✅ Header fixo com logo e menu do usuário
  - ✅ Navegação horizontal principal
  - ✅ Sidebar com menu contextual
  - ✅ Início do conteúdo principal
  - ✅ Estilos CSS integrados
  - ✅ JavaScript para funcionalidades

#### **2. includes/layout_footer.php**
- **Função:** Fechamento do layout com footer
- **Conteúdo:**
  - ✅ Footer fixo com informações do sistema
  - ✅ Scripts JavaScript avançados
  - ✅ Funcionalidades de UX/UI
  - ✅ Fechamento das tags HTML

### **🎨 Estrutura Visual:**

```
┌─────────────────────────────────────────────────────────┐
│                    HEADER (Fixo)                        │
│  🍽️ Sistema de Requisição    👤 Usuário ⚙️ Menu       │
├─────────────────────────────────────────────────────────┤
│                 NAVEGAÇÃO (Fixa)                        │
│  🏠 Dashboard  📄 Requisições  📦 Itens  ⚙️ Admin      │
├─────────────┬───────────────────────────────────────────┤
│   SIDEBAR   │              CONTENT                      │
│             │                                           │
│ 🚀 Ações    │  ┌─────────────────────────────────────┐  │
│ Rápidas     │  │         PAGE HEADER                 │  │
│             │  │  📋 Título da Página               │  │
│ 📋 Requis.  │  │  Subtítulo descritivo               │  │
│ • Minhas    │  └─────────────────────────────────────┘  │
│ • Todas     │                                           │
│             │  ┌─────────────────────────────────────┐  │
│ 📦 Itens    │  │         CONTENT CARD                │  │
│ • Lista     │  │  Conteúdo principal da página      │  │
│ • Gerenciar │  │                                     │  │
│             │  └─────────────────────────────────────┘  │
│ ⚙️ Admin    │                                           │
│ • Usuários  │  ┌─────────────────────────────────────┐  │
│ • Config    │  │         CONTENT CARD                │  │
│             │  │  Mais conteúdo...                   │  │
├─────────────┴───┴─────────────────────────────────────┘  │
│                    FOOTER (Fixo)                        │
│  © 2024 Sistema de Requisição • Versão 2.0             │
└─────────────────────────────────────────────────────────┘
```

## 🔧 Como Usar o Novo Layout

### **📄 Estrutura Básica de uma Página:**

```php
<?php
// 1. Configurações da página
$page_title = 'Título da Página';
$page_subtitle = 'Descrição da página';
$page_header = true; // Mostrar cabeçalho da página

// 2. Incluir layout base
require_once 'includes/layout.php';
require_once 'config/db_connect.php';

// 3. Lógica PHP da página
// ... código PHP ...
?>

<!-- 4. Conteúdo HTML da página -->
<div class="content-card">
    <h3>Título da Seção</h3>
    <p>Conteúdo da página...</p>
</div>

<div class="content-card">
    <h3>Outra Seção</h3>
    <p>Mais conteúdo...</p>
</div>

<?php
// 5. Scripts específicos da página (opcional)
$page_scripts = '
<script>
// JavaScript específico da página
console.log("Página carregada!");
</script>
';

// 6. Incluir footer do layout
require_once 'includes/layout_footer.php';
?>
```

### **⚙️ Variáveis de Configuração:**

#### **Obrigatórias:**
```php
$page_title = 'Nome da Página';        // Título no <title> e header
```

#### **Opcionais:**
```php
$page_subtitle = 'Descrição';          // Subtítulo no header
$page_header = true;                    // Mostrar header da página
$page_scripts = '<script>...</script>'; // Scripts específicos
```

## 🎨 Componentes do Layout

### **🔝 Header (Cabeçalho)**

#### **Características:**
- ✅ **Posição:** Fixa no topo
- ✅ **Altura:** 70px
- ✅ **Conteúdo:** Logo, nome do sistema, menu do usuário
- ✅ **Cores:** Gradiente azul (#007bff → #0056b3)
- ✅ **Responsivo:** Menu hambúrguer no mobile

#### **Elementos:**
```html
<header class="main-header">
    <div class="header-content">
        <!-- Logo e nome -->
        <a href="index.php" class="logo">
            <i class="fas fa-utensils"></i>
            Sistema de Requisição
        </a>
        
        <!-- Menu do usuário -->
        <div class="user-menu">
            <div class="user-info">
                <div class="user-avatar">👤</div>
                <div>Nome do Usuário</div>
            </div>
            <div class="dropdown">...</div>
        </div>
    </div>
</header>
```

### **🧭 Navegação Principal**

#### **Características:**
- ✅ **Posição:** Fixa abaixo do header
- ✅ **Altura:** 50px
- ✅ **Conteúdo:** Links principais do sistema
- ✅ **Cores:** Fundo branco, links azuis
- ✅ **Responsivo:** Oculta no mobile

#### **Links Padrão:**
```php
// Para todos os usuários
- 🏠 Dashboard (index.php)
- 📄 Minhas Requisições (my_requests.php)

// Apenas para administradores
- 📋 Gerenciar Requisições (manage_requests.php)
- 📦 Gerenciar Itens (manage_items.php)
```

### **📋 Sidebar (Menu Lateral)**

#### **Características:**
- ✅ **Posição:** Fixa à esquerda
- ✅ **Largura:** 250px
- ✅ **Conteúdo:** Menu contextual organizado por seções
- ✅ **Cores:** Fundo branco, bordas sutis
- ✅ **Responsivo:** Oculta/mostra no mobile

#### **Seções Padrão:**
```
🚀 Ações Rápidas
├── ➕ Nova Requisição
└── ➕ Adicionar Item (admin)

📋 Requisições
├── 📄 Minhas Requisições
├── 📋 Todas as Requisições (admin)
└── 📊 Relatórios (admin)

📦 Itens
├── 📋 Lista de Itens
├── 📦 Gerenciar Itens (admin)
└── 🏷️ Categorias (admin)

⚙️ Administração (admin)
├── 👥 Usuários
├── ⚙️ Configurações
└── 💾 Backup

🔧 Ferramentas
├── 🔍 Buscar
└── ❓ Ajuda
```

### **📄 Conteúdo Principal**

#### **Características:**
- ✅ **Posição:** Centro, com margem da sidebar
- ✅ **Padding:** 30px
- ✅ **Conteúdo:** Cards organizados
- ✅ **Cores:** Fundo cinza claro (#f5f6fa)
- ✅ **Responsivo:** Largura total no mobile

#### **Componentes:**
```html
<!-- Header da página (opcional) -->
<div class="page-header">
    <h1 class="page-title">Título</h1>
    <p class="page-subtitle">Subtítulo</p>
</div>

<!-- Cards de conteúdo -->
<div class="content-card">
    <h3>Título da Seção</h3>
    <p>Conteúdo...</p>
</div>
```

### **🔽 Footer (Rodapé)**

#### **Características:**
- ✅ **Posição:** Fixo na parte inferior
- ✅ **Altura:** 60px
- ✅ **Conteúdo:** Copyright e informações do sistema
- ✅ **Cores:** Fundo escuro (#343a40), texto branco
- ✅ **Responsivo:** Texto centralizado

## 📱 Responsividade

### **🖥️ Desktop (> 768px):**
- ✅ Layout completo com sidebar visível
- ✅ Navegação horizontal completa
- ✅ Conteúdo com margem da sidebar

### **📱 Mobile (≤ 768px):**
- ✅ Sidebar oculta por padrão
- ✅ Menu hambúrguer no header
- ✅ Navegação horizontal oculta
- ✅ Conteúdo em largura total

### **🔄 Interações Mobile:**
```javascript
// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('show');
}

// Fechar sidebar ao clicar fora
document.addEventListener('click', function(event) {
    // Lógica para fechar sidebar
});
```

## 🎨 Classes CSS Principais

### **📦 Layout:**
```css
.main-header          /* Header fixo */
.main-nav            /* Navegação principal */
.main-sidebar        /* Sidebar lateral */
.main-content        /* Conteúdo principal */
.main-footer         /* Footer fixo */
```

### **📄 Conteúdo:**
```css
.page-header         /* Cabeçalho da página */
.page-title          /* Título da página */
.page-subtitle       /* Subtítulo da página */
.content-card        /* Card de conteúdo */
.btn-custom          /* Botão personalizado */
```

### **🎨 Utilitários:**
```css
:root {
    --primary-color: #007bff;
    --sidebar-width: 250px;
    --header-height: 70px;
    --footer-height: 60px;
}
```

## 🔧 Funcionalidades JavaScript

### **🎯 Funcionalidades Incluídas:**

#### **1. Navegação:**
```javascript
// Toggle sidebar mobile
toggleSidebar()

// Navegação suave
// Smooth scrolling para links internos
```

#### **2. UX/UI:**
```javascript
// Auto-hide alerts após 5 segundos
// Loading state para formulários
// Tooltips e popovers automáticos
// Confirmação para exclusões
```

#### **3. Utilitários:**
```javascript
// showToast(message, type)
// copyToClipboard(text)
// exportTableToCSV(tableId, filename)
// printElement(elementId)
// setupTableSearch(inputId, tableId)
```

#### **4. Formatação:**
```javascript
// formatNumber(num)
// formatDate(dateString)
// formatDateTime(dateString)
```

## 📋 Checklist de Refatoração

### **✅ Para Cada Página:**

#### **1. Configuração Inicial:**
```php
// ✅ Definir variáveis de configuração
$page_title = 'Nome da Página';
$page_subtitle = 'Descrição';
$page_header = true;

// ✅ Incluir layout base
require_once 'includes/layout.php';
```

#### **2. Remover HTML Antigo:**
```html
<!-- ❌ Remover -->
<!DOCTYPE html>
<html>
<head>...</head>
<body>
<nav>...</nav>

<!-- ❌ Remover -->
<footer>...</footer>
</body>
</html>
```

#### **3. Atualizar Conteúdo:**
```html
<!-- ✅ Usar content-cards -->
<div class="content-card">
    <h3>Título</h3>
    <p>Conteúdo...</p>
</div>
```

#### **4. Finalizar:**
```php
// ✅ Scripts específicos (opcional)
$page_scripts = '<script>...</script>';

// ✅ Incluir footer
require_once 'includes/layout_footer.php';
```

### **🔄 Classes Bootstrap Atualizadas:**

#### **Bootstrap 4 → Bootstrap 5:**
```css
/* ❌ Bootstrap 4 */
.btn-block → .w-100
.ml-2 → .ms-2
.mr-2 → .me-2
.badge-warning → .bg-warning
.thead-dark → .table-dark
data-toggle → data-bs-toggle
data-dismiss → data-bs-dismiss

/* ✅ Bootstrap 5 */
.w-100, .ms-2, .me-2
.bg-warning, .table-dark
data-bs-toggle, data-bs-dismiss
```

## 🚀 Benefícios Alcançados

### **👥 Para Usuários:**
- ✅ **Navegação consistente** em todas as páginas
- ✅ **Interface moderna** e responsiva
- ✅ **Acesso rápido** às funcionalidades principais
- ✅ **Experiência fluida** entre páginas

### **👨‍💻 Para Desenvolvedores:**
- ✅ **Código reutilizável** e organizado
- ✅ **Manutenção simplificada** do layout
- ✅ **Padrões consistentes** de desenvolvimento
- ✅ **Facilidade para adicionar** novas páginas

### **🎨 Para o Sistema:**
- ✅ **Identidade visual** unificada
- ✅ **Performance otimizada** com CSS/JS centralizados
- ✅ **Acessibilidade** aprimorada
- ✅ **SEO melhorado** com estrutura semântica

## 📝 Próximos Passos

### **🔄 Páginas a Refatorar:**
1. ✅ **index.php** - Concluído
2. ✅ **my_requests.php** - Concluído
3. 🔄 **request_form.php** - Pendente
4. 🔄 **manage_requests.php** - Pendente
5. 🔄 **manage_items.php** - Pendente
6. 🔄 **view_request.php** - Pendente
7. 🔄 **edit_request.php** - Pendente

### **🆕 Funcionalidades Futuras:**
- 🔄 **Temas personalizáveis** (claro/escuro)
- 🔄 **Notificações em tempo real**
- 🔄 **Atalhos de teclado**
- 🔄 **Busca global** no header
- 🔄 **Breadcrumbs** dinâmicos

---

**🎨 LAYOUT PADRONIZADO IMPLEMENTADO COM SUCESSO!**
*Sistema agora possui estrutura consistente com header, nav, content, sidebar e footer em todas as páginas.*
