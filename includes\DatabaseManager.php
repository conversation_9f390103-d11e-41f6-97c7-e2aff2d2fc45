<?php
/**
 * Gerenciador de Banco de Dados
 * Classe centralizada para gerenciar conexões e operações do banco
 */
class DatabaseManager {
    private static $instance = null;
    private $pdo = null;
    private $config = [
        'host' => 'localhost',
        'dbname' => 'kitchen_inventory',
        'username' => 'root',
        'password' => '123mudar',
        'charset' => 'utf8'
    ];
    
    private function __construct() {
        $this->connect();
    }
    
    /**
     * Singleton pattern - retorna única instância
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Estabelece conexão com banco de dados
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};dbname={$this->config['dbname']};charset={$this->config['charset']}";
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password']);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erro de conexão com banco: " . $e->getMessage());
            throw new Exception("Erro ao conectar com banco de dados");
        }
    }
    
    /**
     * Retorna conexão PDO
     */
    public function getConnection() {
        if ($this->pdo === null) {
            $this->connect();
        }
        return $this->pdo;
    }
    
    /**
     * Executa query preparada com parâmetros
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Erro na query: " . $e->getMessage() . " | SQL: " . $sql);
            throw new Exception("Erro ao executar consulta");
        }
    }
    
    /**
     * Busca um único registro
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Busca múltiplos registros
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Busca apenas uma coluna
     */
    public function fetchColumn($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn();
    }
    
    /**
     * Insere registro e retorna ID
     */
    public function insert($table, $data) {
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');
        
        $sql = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $this->query($sql, array_values($data));
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Atualiza registro
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setParts = [];
        foreach (array_keys($data) as $column) {
            $setParts[] = "{$column} = ?";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setParts) . " WHERE {$where}";
        $params = array_merge(array_values($data), $whereParams);
        
        return $this->query($sql, $params);
    }
    
    /**
     * Remove registro
     */
    public function delete($table, $where, $whereParams = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $whereParams);
    }
    
    /**
     * Conta registros
     */
    public function count($table, $where = '', $whereParams = []) {
        $sql = "SELECT COUNT(*) FROM {$table}";
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        return $this->fetchColumn($sql, $whereParams);
    }
    
    /**
     * Busca com paginação otimizada
     */
    public function paginate($table, $page = 1, $perPage = 20, $where = '', $whereParams = [], $orderBy = 'id DESC') {
        $page = max(1, (int)$page);
        $offset = ($page - 1) * $perPage;
        
        // Contar total
        $total = $this->count($table, $where, $whereParams);
        
        // Buscar dados
        $sql = "SELECT * FROM {$table}";
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        $sql .= " ORDER BY {$orderBy}";
        
        // Usar sintaxe compatível com MySQL
        if ($offset > 0) {
            $sql .= " LIMIT ?, ?";
            $params = array_merge($whereParams, [$offset, $perPage]);
        } else {
            $sql .= " LIMIT ?";
            $params = array_merge($whereParams, [$perPage]);
        }
        
        $data = $this->fetchAll($sql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'perPage' => $perPage,
            'totalPages' => ceil($total / $perPage),
            'hasNext' => $page < ceil($total / $perPage),
            'hasPrev' => $page > 1
        ];
    }
    
    /**
     * Inicia transação
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * Confirma transação
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * Desfaz transação
     */
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    /**
     * Verifica se está em transação
     */
    public function inTransaction() {
        return $this->pdo->inTransaction();
    }
    
    /**
     * Testa conexão
     */
    public function testConnection() {
        try {
            $this->fetchColumn("SELECT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Obtém informações do banco
     */
    public function getDatabaseInfo() {
        try {
            $version = $this->fetchColumn("SELECT VERSION()");
            $tables = $this->fetchAll("SHOW TABLES");
            
            return [
                'version' => $version,
                'tables' => array_column($tables, array_keys($tables[0])[0]),
                'connected' => true
            ];
        } catch (Exception $e) {
            return [
                'version' => null,
                'tables' => [],
                'connected' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
?>
