# 🚀 Sistema de Roteamento com Querystring

## 📋 Visão Geral

O sistema agora utiliza um **roteamento centralizado** com querystring para gerenciar todas as páginas, proporcionando:

- ✅ **URLs consistentes** com `?page=nome-da-pagina`
- ✅ **Controle de acesso** automático
- ✅ **Página 404** personalizada
- ✅ **Validação de parâmetros** obrigatórios
- ✅ **Redirecionamentos** automáticos de URLs antigas

---

## 🔧 Arquivos Principais

### **📄 `router.php`**
- **Função:** Define todas as páginas válidas e suas configurações
- **Responsabilidades:** Validação, autenticação, redirecionamentos

### **📄 `app.php`**
- **Função:** Arquivo principal que executa o roteamento
- **URL:** `app.php?page=nome-da-pagina`

### **📄 `.htaccess`**
- **Função:** Redireciona URLs antigas para o novo sistema
- **Benefícios:** Compatibilidade com links existentes

---

## 🗺️ Mapeamento de URLs

### **🏠 Páginas Principais:**
```
ANTES                    →  DEPOIS
index.php               →  app.php?page=dashboard
request_form.php        →  app.php?page=nova-requisicao
my_requests.php         →  app.php?page=minhas-requisicoes
profile.php             →  app.php?page=perfil
```

### **👨‍💼 Páginas Administrativas:**
```
ANTES                    →  DEPOIS
manage_requests.php     →  app.php?page=gerenciar-requisicoes
manage_items.php        →  app.php?page=gerenciar-itens
manage_users.php        →  app.php?page=gerenciar-usuarios
```

### **📄 Páginas com Parâmetros:**
```
ANTES                           →  DEPOIS
view_request.php?id=123        →  app.php?page=ver-requisicao&id=123
edit_request.php?id=123        →  app.php?page=editar-requisicao&id=123
view_user.php?id=456           →  app.php?page=ver-usuario&id=456
```

---

## ⚙️ Configuração de Páginas

### **📝 Estrutura no `router.php`:**
```php
$validPages = [
    'nome-da-pagina' => [
        'file' => 'arquivo.php',           // Arquivo físico
        'title' => 'Título da Página',    // Título para exibição
        'auth_required' => true,           // Requer login?
        'admin_only' => false,             // Apenas admin?
        'params' => ['id', 'format']       // Parâmetros obrigatórios
    ]
];
```

### **🔒 Controle de Acesso:**
```php
// Página pública (sem login)
'auth_required' => false

// Página que requer login
'auth_required' => true

// Página apenas para administradores
'auth_required' => true,
'admin_only' => true
```

### **📋 Parâmetros Obrigatórios:**
```php
// Página que requer ID
'params' => ['id']

// Página que requer múltiplos parâmetros
'params' => ['id', 'format', 'type']
```

---

## 🛡️ Sistema de Segurança

### **🔐 Validações Automáticas:**
1. **Página existe?** → Se não, redireciona para 404
2. **Usuário logado?** → Se não, redireciona para login
3. **É admin?** → Se necessário e não é, nega acesso
4. **Arquivo existe?** → Se não, redireciona para 404
5. **Parâmetros OK?** → Se faltam, redireciona para 404

### **🚫 Proteções Implementadas:**
- ✅ **Validação de entrada** para todos os parâmetros
- ✅ **Controle de acesso** baseado em roles
- ✅ **Verificação de arquivos** antes da inclusão
- ✅ **Sanitização** de URLs e parâmetros

---

## 📄 Página 404 Personalizada

### **🎨 Características:**
- ✅ **Layout padronizado** usando `includes/layout.php`
- ✅ **Sugestões de navegação** contextuais
- ✅ **Links úteis** baseados no status do usuário
- ✅ **Design responsivo** e acessível

### **📍 Localização:**
- **Arquivo:** `404.php`
- **Acesso:** Automático quando página não existe
- **Fallback:** HTML simples se arquivo não existir

---

## 🔄 Redirecionamentos Automáticos

### **📋 URLs Antigas → Novas:**
O arquivo `.htaccess` redireciona automaticamente:

```apache
# Exemplo de redirecionamento
RewriteRule ^index\.php$ app.php?page=dashboard [R=301,L]
RewriteRule ^request_form\.php$ app.php?page=nova-requisicao [R=301,L]
```

### **🎯 Benefícios:**
- ✅ **Links antigos** continuam funcionando
- ✅ **SEO preservado** com redirecionamentos 301
- ✅ **Transição suave** para o novo sistema
- ✅ **Compatibilidade** com bookmarks existentes

---

## 🛠️ Funções Utilitárias

### **🔗 Gerar URLs:**
```php
// Função para gerar URLs
generateUrl('nova-requisicao')
// Resultado: ?page=nova-requisicao

generateUrl('ver-requisicao', ['id' => 123])
// Resultado: ?page=ver-requisicao&id=123
```

### **📋 Verificar Página Ativa:**
```php
// Função atualizada para querystring
isActivePage('dashboard')
// Retorna: 'active' se for a página atual
```

### **📝 Obter Título da Página:**
```php
// Função para obter título
getCurrentPageTitle()
// Retorna: Título da página atual
```

---

## 🧪 Como Testar

### **✅ Testes Básicos:**
1. **Dashboard:** `app.php?page=dashboard`
2. **Nova Requisição:** `app.php?page=nova-requisicao`
3. **Página Inexistente:** `app.php?page=nao-existe` → Deve mostrar 404
4. **Sem Login:** Logout e tente acessar → Deve redirecionar para login
5. **Admin Only:** Usuário comum tenta acessar página admin → Deve negar acesso

### **🔍 Testes de Parâmetros:**
1. **Com ID:** `app.php?page=ver-requisicao&id=1`
2. **Sem ID:** `app.php?page=ver-requisicao` → Deve mostrar 404
3. **ID Inválido:** `app.php?page=ver-requisicao&id=abc` → Deve mostrar 404

### **🔄 Testes de Redirecionamento:**
1. **URL Antiga:** `index.php` → Deve redirecionar para `app.php?page=dashboard`
2. **Com Parâmetros:** `view_request.php?id=1` → Deve redirecionar corretamente

---

## 📈 Benefícios do Sistema

### **👨‍💻 Para Desenvolvedores:**
- ✅ **Código centralizado** para roteamento
- ✅ **Fácil adição** de novas páginas
- ✅ **Controle de acesso** simplificado
- ✅ **Debugging** mais fácil

### **👥 Para Usuários:**
- ✅ **URLs consistentes** e previsíveis
- ✅ **Navegação intuitiva** com breadcrumbs
- ✅ **Páginas de erro** informativas
- ✅ **Performance melhorada**

### **🏢 Para o Sistema:**
- ✅ **Segurança aprimorada** com validações
- ✅ **Manutenibilidade** melhor
- ✅ **Escalabilidade** para novas funcionalidades
- ✅ **SEO otimizado** com redirecionamentos

---

## 🚀 Próximos Passos

### **📋 Implementações Futuras:**
1. **URLs Amigáveis:** `/dashboard` em vez de `?page=dashboard`
2. **Cache de Rotas:** Para melhor performance
3. **Middleware:** Para validações customizadas
4. **API Routes:** Para endpoints AJAX

### **🔧 Melhorias Planejadas:**
1. **Breadcrumbs Dinâmicos:** Baseados na rota atual
2. **Meta Tags Automáticas:** SEO otimizado por página
3. **Logs de Acesso:** Para análise de uso
4. **Rate Limiting:** Para proteção contra spam

---

## 📞 Suporte

### **🐛 Problemas Comuns:**
1. **404 em página válida:** Verificar se arquivo existe
2. **Redirecionamento infinito:** Verificar configuração do `.htaccess`
3. **Acesso negado:** Verificar permissões de usuário
4. **Parâmetros perdidos:** Verificar configuração da rota

### **🔍 Debug:**
1. **Verificar logs** do servidor web
2. **Testar sem `.htaccess`** temporariamente
3. **Verificar permissões** de arquivos
4. **Validar configuração** do `router.php`

---

**🎯 SISTEMA DE ROTEAMENTO IMPLEMENTADO COM SUCESSO!**
*Navegação centralizada, segura e escalável para todo o sistema.*
