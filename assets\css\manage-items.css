/**
 * CSS para Gerenciamento de Itens
 * Sistema de Requisição de Material de Cozinha
 * Seguindo padrões W3C para separação de tecnologias
 */

/* ==========================================================================
   UTILITÁRIOS GERAIS
   ========================================================================== */

.font-monospace {
    font-family: 'Courier New', monospace;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
}

/* ==========================================================================
   TABELAS
   ========================================================================== */

.table th {
    border-top: none;
}

/* ==========================================================================
   CARDS E HEADERS
   ========================================================================== */

.card-header h6 {
    margin-bottom: 0;
}

/* ==========================================================================
   CÓDIGO DE BARRAS
   ========================================================================== */

.barcode-display {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    letter-spacing: 2px;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
}

/* ==========================================================================
   VALIDAÇÃO DE NOME
   ========================================================================== */

.name-validation-container {
    position: relative;
}

#name-validation {
    animation: fadeIn 0.3s ease-in;
}

#name-suggestions {
    animation: slideDown 0.3s ease-in;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
}

/* ==========================================================================
   ESTADOS DE INPUT COM VALIDAÇÃO
   ========================================================================== */

.input-with-validation {
    transition: border-color 0.3s ease;
}

.input-with-validation.checking {
    border-color: #17a2b8 !important;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.input-with-validation.valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.input-with-validation.invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* ==========================================================================
   BOTÕES DE SUGESTÃO
   ========================================================================== */

.suggestion-btn {
    transition: all 0.2s ease;
}

.suggestion-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ==========================================================================
   ALERTAS ESPECIAIS
   ========================================================================== */

.duplicate-alert {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    border-left: 4px solid #ffc107;
    animation: pulse 2s infinite;
}

/* ==========================================================================
   ANIMAÇÕES
   ========================================================================== */

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* ==========================================================================
   RESPONSIVIDADE
   ========================================================================== */

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .card-header .row {
        flex-direction: column;
    }
    
    .card-header .text-right {
        text-align: left !important;
        margin-top: 1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .btn-lg {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* ==========================================================================
   ACESSIBILIDADE
   ========================================================================== */

/* Melhor contraste para leitores de tela */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Foco visível para navegação por teclado */
.form-control:focus,
.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Estados de erro mais visíveis */
.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* ==========================================================================
   IMPRESSÃO
   ========================================================================== */

@media print {
    .btn,
    .alert,
    .navbar,
    .modal {
        display: none !important;
    }
    
    .container {
        max-width: none;
        margin: 0;
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th,
    .table td {
        padding: 0.25rem;
    }
}
