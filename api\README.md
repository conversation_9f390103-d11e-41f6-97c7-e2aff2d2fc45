# 📱 API REST - Sistema de Requisições de Cozinha

## 🎯 Visão Geral

API REST para integração com aplicativo Android do Sistema de Requisições de Cozinha.

## 🔐 Autenticação

A API usa autenticação via Bearer Token. Após o login, inclua o token no header:

```
Authorization: Bearer {token}
```

## 📋 Endpoints

### **🔑 Autenticação**

#### **POST /api/auth**
Login do usuário

**Request:**
```json
{
    "username": "admin",
    "password": "senha123"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login realizado com sucesso",
    "data": {
        "token": "eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiZXhwIjoxNjk5OTk5OTk5fQ==",
        "user": {
            "id": 1,
            "username": "admin",
            "role": "admin"
        }
    }
}
```

#### **GET /api/auth**
Verificar autenticação

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "message": "Autenticado",
    "data": {
        "user": {
            "id": 1,
            "username": "admin",
            "role": "admin"
        }
    }
}
```

#### **DELETE /api/auth**
Logout

**Response:**
```json
{
    "success": true,
    "message": "Logout realizado com sucesso",
    "data": []
}
```

---

### **📋 Requisições**

#### **GET /api/requests**
Listar requisições

**Query Parameters:**
- `page` (opcional): Página (padrão: 1)
- `per_page` (opcional): Itens por página (padrão: 10)

**Response:**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "title": "Requisição de Ingredientes",
                "status": "pending",
                "request_date": "2024-01-15 10:30:00",
                "internal_code": "REQ000001",
                "item_count": 5,
                "username": "funcionario1"
            }
        ],
        "total": 25,
        "page": 1,
        "perPage": 10,
        "totalPages": 3
    }
}
```

#### **GET /api/requests/{id}**
Detalhes de uma requisição

**Response:**
```json
{
    "success": true,
    "data": {
        "request": {
            "id": 1,
            "title": "Requisição de Ingredientes",
            "status": "pending",
            "request_date": "2024-01-15 10:30:00",
            "internal_code": "REQ000001",
            "username": "funcionario1"
        },
        "items": [
            {
                "id": 1,
                "name": "Farinha de Trigo",
                "quantity": 5,
                "unit": "kg",
                "description": "Farinha especial para pães"
            }
        ]
    }
}
```

#### **POST /api/requests**
Criar nova requisição

**Request:**
```json
{
    "title": "Nova Requisição",
    "priority": "normal",
    "department": "Cozinha",
    "notes": "Observações adicionais",
    "items": {
        "1": 5,
        "2": 3,
        "3": 10
    }
}
```

**Response:**
```json
{
    "success": true,
    "message": "Requisição criada com sucesso",
    "data": {
        "id": 15,
        "internal_code": "REQ000015",
        "item_count": 3
    }
}
```

#### **PUT /api/requests/{id}**
Atualizar status (apenas admin)

**Request:**
```json
{
    "status": "approved",
    "notes": "Aprovado para entrega",
    "reason": "Todos os itens disponíveis"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Status atualizado com sucesso",
    "data": []
}
```

#### **DELETE /api/requests/{id}**
Excluir requisição

**Response:**
```json
{
    "success": true,
    "message": "Requisição excluída com sucesso",
    "data": []
}
```

---

### **📦 Itens**

#### **GET /api/items**
Listar itens disponíveis

**Query Parameters:**
- `page` (opcional): Página (padrão: 1)
- `per_page` (opcional): Itens por página (padrão: 20)
- `search` (opcional): Termo de busca

**Response:**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "name": "Farinha de Trigo",
                "description": "Farinha especial para pães",
                "unit": "kg",
                "quantity": 50
            }
        ],
        "total": 100,
        "page": 1,
        "perPage": 20,
        "totalPages": 5
    }
}
```

---

### **👥 Usuários** (Apenas Admin)

#### **GET /api/users**
Listar usuários

**Response:**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "username": "admin",
                "role": "admin"
            },
            {
                "id": 2,
                "username": "funcionario1",
                "role": "staff"
            }
        ]
    }
}
```

---

### **📊 Estatísticas**

#### **GET /api/stats**
Estatísticas das requisições

**Response:**
```json
{
    "success": true,
    "data": {
        "total": 50,
        "pending": 15,
        "approved": 20,
        "rejected": 5,
        "delivered": 10
    }
}
```

---

## 🚨 Códigos de Erro

- **400**: Dados inválidos
- **401**: Não autenticado
- **403**: Acesso negado
- **404**: Recurso não encontrado
- **405**: Método não permitido
- **500**: Erro interno do servidor

## 📱 Exemplo de Uso no Android

```javascript
// Login
const loginResponse = await fetch('/api/auth', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        username: 'admin',
        password: 'senha123'
    })
});

const loginData = await loginResponse.json();
const token = loginData.data.token;

// Buscar requisições
const requestsResponse = await fetch('/api/requests?page=1&per_page=10', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});

const requestsData = await requestsResponse.json();
```

## 🔧 Configuração

1. Certifique-se que o arquivo `api/index.php` está acessível
2. Configure CORS se necessário
3. Teste os endpoints usando Postman ou similar
4. Implemente tratamento de erros no app Android

## 🛡️ Segurança

- Tokens expiram em 24 horas
- Validação de permissões por endpoint
- Sanitização automática de dados
- Logs de atividade registrados

---

**🚀 API pronta para integração com aplicativo Android!**
