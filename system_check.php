<?php
require_once 'config/db_connect.php';

echo "<h2>Verificação do Sistema</h2>";

$errors = [];
$warnings = [];
$success = [];

// Verificar conexão com banco de dados
if (isset($pdo) && $pdo !== null) {
    try {
        $stmt = $pdo->query("SELECT 1");
        $success[] = "✓ Conexão com banco de dados funcionando";
    } catch (PDOException $e) {
        $errors[] = "✗ Erro na conexão com banco de dados: " . $e->getMessage();
    }
} else {
    $errors[] = "✗ Variável \$pdo não está definida ou é null";
}

// Verificar se as tabelas existem
if (isset($pdo) && $pdo !== null) {
    $tables = ['users', 'items', 'requests', 'request_items'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $success[] = "✓ Tabela '$table' existe";
            } else {
                $errors[] = "✗ Tabela '$table' não encontrada";
            }
        } catch (PDOException $e) {
            $errors[] = "✗ Erro ao verificar tabela '$table': " . $e->getMessage();
        }
    }
} else {
    $errors[] = "✗ Não é possível verificar tabelas - conexão com banco indisponível";
}

// Verificar se existem usuários
if (isset($pdo) && $pdo !== null) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        if ($userCount > 0) {
            $success[] = "✓ Existem $userCount usuário(s) cadastrado(s)";
        } else {
            $warnings[] = "⚠ Nenhum usuário cadastrado";
        }
    } catch (PDOException $e) {
        $errors[] = "✗ Erro ao verificar usuários: " . $e->getMessage();
    }

    // Verificar se existe usuário admin
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        $stmt->execute();
        $adminCount = $stmt->fetchColumn();
        if ($adminCount > 0) {
            $success[] = "✓ Existe(m) $adminCount administrador(es)";
        } else {
            $warnings[] = "⚠ Nenhum administrador cadastrado";
        }
    } catch (PDOException $e) {
        $errors[] = "✗ Erro ao verificar administradores: " . $e->getMessage();
    }

    // Verificar se existem itens
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM items");
        $itemCount = $stmt->fetchColumn();
        if ($itemCount > 0) {
            $success[] = "✓ Existem $itemCount item(ns) no estoque";
        } else {
            $warnings[] = "⚠ Nenhum item cadastrado no estoque";
        }
    } catch (PDOException $e) {
        $errors[] = "✗ Erro ao verificar itens: " . $e->getMessage();
    }
} else {
    $errors[] = "✗ Não é possível verificar dados - conexão com banco indisponível";
}

// Verificar arquivos essenciais
$essentialFiles = [
    'index.php',
    'login.php',
    'logout.php',
    'request_form.php',
    'my_requests.php',
    'manage_requests.php',
    'manage_items.php',
    'view_request.php',
    'config/db_connect.php',
    'includes/navbar.php'
];

foreach ($essentialFiles as $file) {
    if (file_exists($file)) {
        $success[] = "✓ Arquivo '$file' existe";
    } else {
        $errors[] = "✗ Arquivo '$file' não encontrado";
    }
}

// Verificar permissões de sessão
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (ini_get('session.auto_start') || session_status() === PHP_SESSION_ACTIVE) {
    $success[] = "✓ Sessões PHP funcionando";
} else {
    $errors[] = "✗ Problema com sessões PHP";
}

// Verificar extensões PHP necessárias
$requiredExtensions = ['pdo', 'pdo_mysql'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        $success[] = "✓ Extensão PHP '$ext' carregada";
    } else {
        $errors[] = "✗ Extensão PHP '$ext' não encontrada";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Verificação do Sistema</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-4">
        <h1>Verificação do Sistema de Requisições</h1>
        
        <?php if (!empty($errors)): ?>
        <div class="card mb-3">
            <div class="card-header bg-danger text-white">
                <h5>Erros Encontrados</h5>
            </div>
            <div class="card-body">
                <?php foreach ($errors as $error): ?>
                    <p class="text-danger"><?php echo htmlspecialchars($error); ?></p>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($warnings)): ?>
        <div class="card mb-3">
            <div class="card-header bg-warning text-dark">
                <h5>Avisos</h5>
            </div>
            <div class="card-body">
                <?php foreach ($warnings as $warning): ?>
                    <p class="text-warning"><?php echo htmlspecialchars($warning); ?></p>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
        <div class="card mb-3">
            <div class="card-header bg-success text-white">
                <h5>Verificações Bem-sucedidas</h5>
            </div>
            <div class="card-body">
                <?php foreach ($success as $item): ?>
                    <p class="text-success"><?php echo htmlspecialchars($item); ?></p>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <h4>Ações Recomendadas:</h4>
            <ul>
                <?php if (!empty($errors)): ?>
                    <li>Corrija os erros listados acima antes de usar o sistema</li>
                <?php endif; ?>
                
                <?php if (in_array("⚠ Nenhum usuário cadastrado", $warnings)): ?>
                    <li><a href="populate_database.php">Execute o script de população do banco</a> para criar usuários e itens de exemplo</li>
                <?php endif; ?>
                
                <?php if (in_array("⚠ Nenhum administrador cadastrado", $warnings)): ?>
                    <li><a href="create_admin.php">Crie um usuário administrador</a></li>
                <?php endif; ?>
                
                <?php if (in_array("⚠ Nenhum item cadastrado no estoque", $warnings)): ?>
                    <li>Acesse o sistema como administrador e cadastre itens no estoque</li>
                <?php endif; ?>
                
                <?php if (empty($errors)): ?>
                    <li class="text-success">✓ Sistema pronto para uso! <a href="login.php">Fazer login</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</body>
</html>
