<?php
echo "<h1>🏗️ Criação do Banco de Dados</h1>";

// Configurações
$host = 'localhost';
$dbname = 'kitchen_inventory';
$username = 'root';
$password = '123mudar';

echo "<h2>📋 Configurações:</h2>";
echo "<ul>";
echo "<li><strong>Host:</strong> $host</li>";
echo "<li><strong>Database:</strong> $dbname</li>";
echo "<li><strong>Username:</strong> $username</li>";
echo "</ul>";

$success = [];
$errors = [];

try {
    // Conectar ao MySQL sem especificar banco
    echo "<h2>🔄 Conectando ao MySQL...</h2>";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $success[] = "✅ Conexão com MySQL estabelecida";

    // Verificar se banco já existe
    echo "<h2>🔍 Verificando se banco existe...</h2>";
    $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
    if ($stmt->rowCount() > 0) {
        $success[] = "ℹ️ Banco '$dbname' já existe";
        echo "<p>✅ O banco '$dbname' já existe!</p>";
    } else {
        // Criar banco
        echo "<h2>🏗️ Criando banco de dados...</h2>";
        $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $success[] = "✅ Banco '$dbname' criado com sucesso";
        echo "<p>✅ Banco '$dbname' criado com sucesso!</p>";
    }

    // Conectar ao banco específico
    echo "<h2>🔗 Conectando ao banco específico...</h2>";
    $pdo_db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo_db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $success[] = "✅ Conexão com banco '$dbname' estabelecida";

    // Criar tabelas
    echo "<h2>📊 Criando tabelas...</h2>";

    // Tabela users
    $sql_users = "
    CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `role` enum('admin','staff') NOT NULL DEFAULT 'staff',
        `full_name` varchar(100) DEFAULT NULL,
        `email` varchar(100) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `department` varchar(50) DEFAULT NULL,
        `avatar` varchar(255) DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_users_email` (`email`),
        KEY `idx_users_department` (`department`),
        KEY `idx_users_full_name` (`full_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo_db->exec($sql_users);
    $success[] = "✅ Tabela 'users' criada";

    // Tabela items
    $sql_items = "
    CREATE TABLE IF NOT EXISTS `items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `description` text,
        `unit` varchar(20) NOT NULL DEFAULT 'unidade',
        `internal_code` varchar(20) UNIQUE,
        `barcode` varchar(50) DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_items_internal_code` (`internal_code`),
        KEY `idx_items_name` (`name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo_db->exec($sql_items);
    $success[] = "✅ Tabela 'items' criada";

    // Tabela requests
    $sql_requests = "
    CREATE TABLE IF NOT EXISTS `requests` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `title` varchar(200) DEFAULT NULL,
        `department` varchar(100) DEFAULT NULL,
        `priority` enum('baixa','media','alta','urgente') NOT NULL DEFAULT 'media',
        `observations` text,
        `status` enum('pending','approved','rejected','delivered') NOT NULL DEFAULT 'pending',
        `request_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `internal_code` varchar(20) UNIQUE,
        `barcode` varchar(50) DEFAULT NULL,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `idx_requests_internal_code` (`internal_code`),
        KEY `idx_requests_status` (`status`),
        CONSTRAINT `requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo_db->exec($sql_requests);
    $success[] = "✅ Tabela 'requests' criada";

    // Tabela request_items
    $sql_request_items = "
    CREATE TABLE IF NOT EXISTS `request_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `request_id` int(11) NOT NULL,
        `item_id` int(11) NOT NULL,
        `quantity` decimal(10,2) NOT NULL,
        `observations` text,
        PRIMARY KEY (`id`),
        KEY `request_id` (`request_id`),
        KEY `item_id` (`item_id`),
        CONSTRAINT `request_items_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `requests` (`id`) ON DELETE CASCADE,
        CONSTRAINT `request_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo_db->exec($sql_request_items);
    $success[] = "✅ Tabela 'request_items' criada";

    // Criar usuário admin padrão
    echo "<h2>👤 Criando usuário administrador...</h2>";
    
    $stmt = $pdo_db->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn();

    if ($adminExists == 0) {
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo_db->prepare("
            INSERT INTO users (username, password, role, full_name, email) 
            VALUES ('admin', ?, 'admin', 'Administrador do Sistema', '<EMAIL>')
        ");
        $stmt->execute([$hashedPassword]);
        $success[] = "✅ Usuário admin criado (username: admin, senha: admin123)";
    } else {
        $success[] = "ℹ️ Usuário admin já existe";
    }

    // Criar alguns itens de exemplo
    echo "<h2>📦 Criando itens de exemplo...</h2>";
    
    $stmt = $pdo_db->prepare("SELECT COUNT(*) FROM items");
    $stmt->execute();
    $itemCount = $stmt->fetchColumn();

    if ($itemCount == 0) {
        $items = [
            ['Arroz Branco', 'Arroz branco tipo 1', 'kg'],
            ['Feijão Preto', 'Feijão preto tipo 1', 'kg'],
            ['Óleo de Soja', 'Óleo de soja refinado', 'litro'],
            ['Sal Refinado', 'Sal refinado iodado', 'kg'],
            ['Açúcar Cristal', 'Açúcar cristal especial', 'kg'],
            ['Farinha de Trigo', 'Farinha de trigo especial', 'kg'],
            ['Macarrão Espaguete', 'Macarrão espaguete 500g', 'pacote'],
            ['Molho de Tomate', 'Molho de tomate tradicional', 'lata'],
            ['Cebola', 'Cebola amarela', 'kg'],
            ['Alho', 'Alho nacional', 'kg']
        ];

        foreach ($items as $index => $item) {
            $internalCode = 'ITEM' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $stmt = $pdo_db->prepare("
                INSERT INTO items (name, description, unit, internal_code) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$item[0], $item[1], $item[2], $internalCode]);
        }
        $success[] = "✅ " . count($items) . " itens de exemplo criados";
    } else {
        $success[] = "ℹ️ Itens já existem no banco";
    }

} catch (PDOException $e) {
    $errors[] = "❌ Erro: " . $e->getMessage();
}

// Exibir resultados
echo "<h2>📊 Resultados:</h2>";

if (!empty($success)) {
    echo "<div style='background: #d1edff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ Sucessos:</h3>";
    foreach ($success as $msg) {
        echo "<p>$msg</p>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Erros:</h3>";
    foreach ($errors as $msg) {
        echo "<p>$msg</p>";
    }
    echo "</div>";
}

if (empty($errors)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎉 Banco de Dados Criado com Sucesso!</h3>";
    echo "<p>O banco de dados '$dbname' foi criado e configurado corretamente.</p>";
    echo "<h4>📋 Próximos passos:</h4>";
    echo "<ul>";
    echo "<li>✅ <a href='test_db_connection.php'>Testar conexão novamente</a></li>";
    echo "<li>✅ <a href='login.php'>Fazer login no sistema</a> (admin / admin123)</li>";
    echo "<li>✅ <a href='index.php'>Acessar o dashboard</a></li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Script executado em: " . date('d/m/Y H:i:s') . "</small></p>";
?>
