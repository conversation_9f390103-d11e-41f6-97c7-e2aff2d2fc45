<?php
/**
 * Controller Base
 * Classe base para todos os controllers do sistema
 */
class BaseController {
    protected $db;
    protected $user;
    
    public function __construct() {
        $this->db = DatabaseManager::getInstance();
        $this->initSession();
        $this->loadUser();
    }
    
    /**
     * Inicializa sessão se necessário
     */
    protected function initSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Carrega dados do usuário logado
     */
    protected function loadUser() {
        if (isset($_SESSION['user_id'])) {
            $this->user = $this->db->fetchOne(
                "SELECT * FROM users WHERE id = ?",
                [$_SESSION['user_id']]
            );
        }
    }
    
    /**
     * Verifica se usuário está autenticado
     */
    protected function requireAuth() {
        if (!$this->isAuthenticated()) {
            $this->redirect('login.php');
        }
    }
    
    /**
     * Verifica se usuário é admin
     */
    protected function requireAdmin() {
        $this->requireAuth();
        if (!$this->isAdmin()) {
            $this->redirect('dashboard?error=access_denied');
        }
    }
    
    /**
     * Verifica se está autenticado
     */
    protected function isAuthenticated() {
        return isset($_SESSION['user_id']) && $this->user !== false;
    }
    
    /**
     * Verifica se é admin
     */
    protected function isAdmin() {
        return $this->isAuthenticated() && $this->user['role'] === 'admin';
    }
    
    /**
     * Redireciona para URL
     */
    protected function redirect($url) {
        if (!headers_sent()) {
            header("Location: $url");
            exit;
        }
    }
    
    /**
     * Valida dados de entrada
     */
    protected function validate($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            
            // Required
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field] = $rule['message'] ?? "Campo {$field} é obrigatório";
                continue;
            }
            
            // Skip validation if field is empty and not required
            if (empty($value)) continue;
            
            // Min length
            if (isset($rule['min']) && strlen($value) < $rule['min']) {
                $errors[$field] = $rule['message'] ?? "Campo {$field} deve ter pelo menos {$rule['min']} caracteres";
            }
            
            // Max length
            if (isset($rule['max']) && strlen($value) > $rule['max']) {
                $errors[$field] = $rule['message'] ?? "Campo {$field} deve ter no máximo {$rule['max']} caracteres";
            }
            
            // Email
            if (isset($rule['email']) && $rule['email'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = $rule['message'] ?? "Campo {$field} deve ser um email válido";
            }
            
            // Numeric
            if (isset($rule['numeric']) && $rule['numeric'] && !is_numeric($value)) {
                $errors[$field] = $rule['message'] ?? "Campo {$field} deve ser numérico";
            }
            
            // Custom validation
            if (isset($rule['custom']) && is_callable($rule['custom'])) {
                $result = $rule['custom']($value);
                if ($result !== true) {
                    $errors[$field] = $result;
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * Sanitiza dados de entrada
     */
    protected function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Gera token CSRF
     */
    protected function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Valida token CSRF
     */
    protected function validateCsrfToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Retorna resposta JSON
     */
    protected function jsonResponse($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Retorna erro JSON
     */
    protected function jsonError($message, $status = 400) {
        $this->jsonResponse(['error' => $message], $status);
    }
    
    /**
     * Retorna sucesso JSON
     */
    protected function jsonSuccess($data = [], $message = 'Sucesso') {
        $this->jsonResponse(['success' => true, 'message' => $message, 'data' => $data]);
    }
    
    /**
     * Gera código interno único
     */
    protected function generateInternalCode($prefix, $id, $length = 6) {
        return $prefix . str_pad($id, $length, '0', STR_PAD_LEFT);
    }
    
    /**
     * Formata data para exibição
     */
    protected function formatDate($date, $format = 'd/m/Y H:i') {
        if (empty($date)) return '';
        return date($format, strtotime($date));
    }
    
    /**
     * Gera hash de senha
     */
    protected function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verifica senha
     */
    protected function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Log de atividade
     */
    protected function logActivity($action, $details = []) {
        try {
            $this->db->insert('activity_logs', [
                'user_id' => $this->user['id'] ?? null,
                'action' => $action,
                'details' => json_encode($details),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Log silencioso - não quebrar aplicação se tabela não existir
            error_log("Erro ao registrar atividade: " . $e->getMessage());
        }
    }
    
    /**
     * Busca com paginação
     */
    protected function paginate($table, $page = 1, $perPage = 20, $where = '', $params = [], $orderBy = 'id DESC') {
        return $this->db->paginate($table, $page, $perPage, $where, $params, $orderBy);
    }
    
    /**
     * Processa upload de arquivo
     */
    protected function handleFileUpload($file, $allowedTypes = ['jpg', 'jpeg', 'png'], $maxSize = 2097152) {
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            throw new Exception('Nenhum arquivo foi enviado');
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Erro no upload do arquivo');
        }
        
        if ($file['size'] > $maxSize) {
            throw new Exception('Arquivo muito grande. Máximo: ' . ($maxSize / 1024 / 1024) . 'MB');
        }
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            throw new Exception('Tipo de arquivo não permitido. Permitidos: ' . implode(', ', $allowedTypes));
        }
        
        $filename = uniqid() . '.' . $extension;
        $uploadPath = 'assets/uploads/' . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
            throw new Exception('Erro ao salvar arquivo');
        }
        
        return $filename;
    }
}
?>
