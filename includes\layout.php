<?php
// Layout base do sistema
if (!isset($_SESSION)) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Definir página atual para navegação ativa
$current_page = basename($_SERVER['PHP_SELF']);

// Função para verificar se a página está ativa
function isActivePage($page) {
    global $current_page;
    return $current_page === $page ? 'active' : '';
}

// Função para verificar se o usuário é admin
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? 'Sistema de Requisição de Material'; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --sidebar-width: 250px;
            --header-height: 70px;
            --footer-height: 60px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f6fa;
            margin: 0;
            padding: 0;
        }

        /* Header */
        .main-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            height: var(--header-height);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: bold;
            text-decoration: none;
            color: white;
        }

        .logo i {
            margin-right: 10px;
            font-size: 2rem;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        /* Navigation */
        .main-nav {
            background: white;
            border-bottom: 1px solid #dee2e6;
            position: fixed;
            top: var(--header-height);
            left: 0;
            right: 0;
            z-index: 1020;
            height: 50px;
        }

        .nav-content {
            display: flex;
            align-items: center;
            height: 100%;
            padding: 0 20px;
        }

        .nav-links {
            display: flex;
            gap: 30px;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .nav-links a {
            color: var(--dark-color);
            text-decoration: none;
            padding: 15px 0;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        /* Sidebar */
        .main-sidebar {
            position: fixed;
            top: calc(var(--header-height) + 50px);
            left: 0;
            width: var(--sidebar-width);
            height: calc(100vh - var(--header-height) - 50px - var(--footer-height));
            background: white;
            border-right: 1px solid #dee2e6;
            overflow-y: auto;
            z-index: 1010;
            box-shadow: 2px 0 10px rgba(0,0,0,0.05);
        }

        .sidebar-content {
            padding: 20px 0;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .sidebar-title {
            font-size: 0.9rem;
            font-weight: bold;
            color: var(--secondary-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0 20px;
            margin-bottom: 10px;
        }

        .sidebar-menu {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--dark-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: var(--light-color);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }

        .sidebar-menu i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            margin-top: calc(var(--header-height) + 50px);
            margin-bottom: var(--footer-height);
            padding: 30px;
            min-height: calc(100vh - var(--header-height) - 50px - var(--footer-height) - 60px);
        }

        /* Footer */
        .main-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--footer-height);
            background: var(--dark-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .main-sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Utilities */
        .page-header {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .page-title {
            margin: 0;
            color: var(--dark-color);
            font-size: 1.8rem;
            font-weight: 600;
        }

        .page-subtitle {
            margin: 5px 0 0 0;
            color: var(--secondary-color);
            font-size: 1rem;
        }

        .content-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }

        .btn-custom {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="header-content">
            <a href="index.php" class="logo">
                <i class="fas fa-utensils"></i>
                <span>Sistema de Requisição</span>
            </a>
            
            <div class="user-menu">
                <button class="mobile-menu-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div style="font-weight: 600;"><?php echo htmlspecialchars($_SESSION['username']); ?></div>
                        <div style="font-size: 0.8rem; opacity: 0.8;">
                            <?php echo isAdmin() ? 'Administrador' : 'Usuário'; ?>
                        </div>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-link text-white dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Perfil</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Configurações</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="main-nav">
        <div class="nav-content">
            <ul class="nav-links">
                <li><a href="index.php" class="<?php echo isActivePage('index.php'); ?>">
                    <i class="fas fa-home me-2"></i>Dashboard
                </a></li>
                <li><a href="my_requests.php" class="<?php echo isActivePage('my_requests.php'); ?>">
                    <i class="fas fa-file-alt me-2"></i>Minhas Requisições
                </a></li>
                <?php if (isAdmin()): ?>
                <li><a href="manage_requests.php" class="<?php echo isActivePage('manage_requests.php'); ?>">
                    <i class="fas fa-tasks me-2"></i>Gerenciar Requisições
                </a></li>
                <li><a href="manage_items.php" class="<?php echo isActivePage('manage_items.php'); ?>">
                    <i class="fas fa-boxes me-2"></i>Gerenciar Itens
                </a></li>
                <?php endif; ?>
            </ul>
        </div>
    </nav>

    <!-- Sidebar -->
    <aside class="main-sidebar" id="sidebar">
        <div class="sidebar-content">
            <!-- Ações Rápidas -->
            <div class="sidebar-section">
                <div class="sidebar-title">Ações Rápidas</div>
                <ul class="sidebar-menu">
                    <li><a href="request_form.php" class="<?php echo isActivePage('request_form.php'); ?>">
                        <i class="fas fa-plus"></i>Nova Requisição
                    </a></li>
                    <?php if (isAdmin()): ?>
                    <li><a href="manage_items.php" class="<?php echo isActivePage('manage_items.php'); ?>">
                        <i class="fas fa-plus-circle"></i>Adicionar Item
                    </a></li>
                    <?php endif; ?>
                </ul>
            </div>

            <!-- Requisições -->
            <div class="sidebar-section">
                <div class="sidebar-title">Requisições</div>
                <ul class="sidebar-menu">
                    <li><a href="my_requests.php" class="<?php echo isActivePage('my_requests.php'); ?>">
                        <i class="fas fa-file-alt"></i>Minhas Requisições
                    </a></li>
                    <?php if (isAdmin()): ?>
                    <li><a href="manage_requests.php" class="<?php echo isActivePage('manage_requests.php'); ?>">
                        <i class="fas fa-tasks"></i>Todas as Requisições
                    </a></li>
                    <li><a href="reports.php" class="<?php echo isActivePage('reports.php'); ?>">
                        <i class="fas fa-chart-bar"></i>Relatórios
                    </a></li>
                    <?php endif; ?>
                </ul>
            </div>

            <!-- Itens -->
            <div class="sidebar-section">
                <div class="sidebar-title">Itens</div>
                <ul class="sidebar-menu">
                    <li><a href="items_list.php" class="<?php echo isActivePage('items_list.php'); ?>">
                        <i class="fas fa-list"></i>Lista de Itens
                    </a></li>
                    <?php if (isAdmin()): ?>
                    <li><a href="manage_items.php" class="<?php echo isActivePage('manage_items.php'); ?>">
                        <i class="fas fa-boxes"></i>Gerenciar Itens
                    </a></li>
                    <li><a href="categories.php" class="<?php echo isActivePage('categories.php'); ?>">
                        <i class="fas fa-tags"></i>Categorias
                    </a></li>
                    <?php endif; ?>
                </ul>
            </div>

            <?php if (isAdmin()): ?>
            <!-- Administração -->
            <div class="sidebar-section">
                <div class="sidebar-title">Administração</div>
                <ul class="sidebar-menu">
                    <li><a href="users.php" class="<?php echo isActivePage('users.php'); ?>">
                        <i class="fas fa-users"></i>Usuários
                    </a></li>
                    <li><a href="settings.php" class="<?php echo isActivePage('settings.php'); ?>">
                        <i class="fas fa-cog"></i>Configurações
                    </a></li>
                    <li><a href="backup.php" class="<?php echo isActivePage('backup.php'); ?>">
                        <i class="fas fa-database"></i>Backup
                    </a></li>
                </ul>
            </div>
            <?php endif; ?>

            <!-- Ferramentas -->
            <div class="sidebar-section">
                <div class="sidebar-title">Ferramentas</div>
                <ul class="sidebar-menu">
                    <li><a href="search.php" class="<?php echo isActivePage('search.php'); ?>">
                        <i class="fas fa-search"></i>Buscar
                    </a></li>
                    <li><a href="help.php" class="<?php echo isActivePage('help.php'); ?>">
                        <i class="fas fa-question-circle"></i>Ajuda
                    </a></li>
                </ul>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <?php if (isset($page_header) && $page_header): ?>
        <div class="page-header">
            <h1 class="page-title"><?php echo $page_title ?? 'Página'; ?></h1>
            <?php if (isset($page_subtitle)): ?>
            <p class="page-subtitle"><?php echo $page_subtitle; ?></p>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <!-- Conteúdo da página será inserido aqui -->
