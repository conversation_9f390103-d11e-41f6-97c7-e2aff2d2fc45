<?php
// Exportação para DOCX
if (!isset($exportData)) {
    exit('Dados de exportação não encontrados');
}

$request = $exportData['request'];
$items = $exportData['items'];
$statusLabel = $exportData['status_label'];

// Nome do arquivo mais descritivo
$requestName = $request['title'] ?? 'Requisicao_' . $request['id'];
$safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $requestName);
$filename = $safeName . '_' . date('Y-m-d_H-i-s') . '.docx';

// Verificar se a biblioteca PhpWord está disponível
if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
    // Fallback: gerar RTF (Rich Text Format) que pode ser aberto pelo Word
    generateRTF($request, $items, $statusLabel, $filename);
    exit;
}

try {
    // Usar PhpWord se disponível
    $phpWord = new \PhpOffice\PhpWord\PhpWord();

    // Configurações do documento
    $phpWord->getSettings()->setThemeFontLang(new \PhpOffice\PhpWord\Style\Language('pt-BR'));

    // Definir estilos
    $phpWord->addTitleStyle(1, ['name' => 'Arial', 'size' => 20, 'bold' => true, 'color' => '0066CC']);
    $phpWord->addTitleStyle(2, ['name' => 'Arial', 'size' => 16, 'bold' => true, 'color' => '0066CC']);
    $phpWord->addTitleStyle(3, ['name' => 'Arial', 'size' => 14, 'bold' => true, 'color' => '333333']);

    // Adicionar seção com margens
    $section = $phpWord->addSection([
        'marginTop' => 1440,    // 1 inch
        'marginBottom' => 1440,
        'marginLeft' => 1440,
        'marginRight' => 1440
    ]);

    // Cabeçalho principal
    $headerTable = $section->addTable(['borderSize' => 0, 'width' => 100, 'unit' => 'pct']);
    $headerTable->addRow();
    $headerCell = $headerTable->addCell(9000, ['bgColor' => 'E3F2FD']);
    $headerCell->addText('SISTEMA DE REQUISIÇÃO DE MATERIAL DE COZINHA',
        ['name' => 'Arial', 'size' => 18, 'bold' => true, 'color' => '0066CC'],
        ['alignment' => 'center']);
    $headerCell->addText($request['title'] ?? 'Requisição #' . $request['id'],
        ['name' => 'Arial', 'size' => 16, 'bold' => true, 'color' => '333333'],
        ['alignment' => 'center']);

    $section->addTextBreak(2);

    // Seção de informações gerais
    $section->addTitle('📋 INFORMAÇÕES GERAIS', 2);
    $section->addTextBreak(1);

    $infoTable = $section->addTable(['borderSize' => 6, 'borderColor' => 'CCCCCC']);

    // Linha 1
    $infoTable->addRow();
    $infoTable->addCell(2000)->addText('ID da Requisição:', ['bold' => true]);
    $infoTable->addCell(2000)->addText('#' . $request['id'], ['color' => '0066CC', 'bold' => true]);
    $infoTable->addCell(2000)->addText('Solicitante:', ['bold' => true]);
    $infoTable->addCell(3000)->addText($request['username'], ['color' => '0066CC', 'bold' => true]);

    // Linha 2
    $infoTable->addRow();
    $infoTable->addCell(2000)->addText('Data da Requisição:', ['bold' => true]);
    $infoTable->addCell(2000)->addText(date('d/m/Y H:i', strtotime($request['request_date'])));
    $infoTable->addCell(2000)->addText('Status:', ['bold' => true]);
    $infoTable->addCell(3000)->addText(strtoupper($statusLabel), ['bold' => true, 'color' => '0066CC']);

    $section->addTextBreak(2);

    // Seção de estatísticas
    $totalItems = count($items);
    $totalQuantity = array_sum(array_column($items, 'quantity'));

    $section->addTitle('📊 ESTATÍSTICAS', 2);
    $section->addTextBreak(1);

    $statsTable = $section->addTable(['borderSize' => 6, 'borderColor' => 'CCCCCC']);
    $statsTable->addRow();
    $statsTable->addCell(2250, ['bgColor' => 'E3F2FD'])->addText('Total de Tipos de Itens', ['bold' => true], ['alignment' => 'center']);
    $statsTable->addCell(2250, ['bgColor' => 'E8F5E8'])->addText('Quantidade Total', ['bold' => true], ['alignment' => 'center']);
    $statsTable->addCell(2250, ['bgColor' => 'FFF3E0'])->addText('Média por Item', ['bold' => true], ['alignment' => 'center']);
    $statsTable->addCell(2250, ['bgColor' => 'F3E5F5'])->addText('Data de Geração', ['bold' => true], ['alignment' => 'center']);

    $statsTable->addRow();
    $statsTable->addCell(2250)->addText($totalItems, ['size' => 16, 'bold' => true, 'color' => '0066CC'], ['alignment' => 'center']);
    $statsTable->addCell(2250)->addText($totalQuantity, ['size' => 16, 'bold' => true, 'color' => '28A745'], ['alignment' => 'center']);
    $statsTable->addCell(2250)->addText(number_format($totalQuantity / max($totalItems, 1), 1), ['size' => 16, 'bold' => true, 'color' => 'FF9800'], ['alignment' => 'center']);
    $statsTable->addCell(2250)->addText(date('d/m/Y'), ['size' => 12, 'bold' => true, 'color' => '9C27B0'], ['alignment' => 'center']);

    $section->addTextBreak(2);

    // Seção de itens detalhada
    $section->addTitle('📦 ITENS SOLICITADOS - DETALHAMENTO COMPLETO', 2);
    $section->addTextBreak(1);

    // Tabela de itens com design melhorado
    $itemsTable = $section->addTable([
        'borderSize' => 6,
        'borderColor' => '0066CC',
        'width' => 100,
        'unit' => 'pct'
    ]);

    // Cabeçalho da tabela
    $itemsTable->addRow(800);
    $itemsTable->addCell(800, ['bgColor' => '0066CC'])->addText('Nº', ['bold' => true, 'color' => 'FFFFFF'], ['alignment' => 'center']);
    $itemsTable->addCell(3000, ['bgColor' => '0066CC'])->addText('Nome do Item', ['bold' => true, 'color' => 'FFFFFF'], ['alignment' => 'center']);
    $itemsTable->addCell(3500, ['bgColor' => '0066CC'])->addText('Descrição Completa', ['bold' => true, 'color' => 'FFFFFF'], ['alignment' => 'center']);
    $itemsTable->addCell(1000, ['bgColor' => '0066CC'])->addText('Quantidade', ['bold' => true, 'color' => 'FFFFFF'], ['alignment' => 'center']);
    $itemsTable->addCell(1000, ['bgColor' => '0066CC'])->addText('Unidade', ['bold' => true, 'color' => 'FFFFFF'], ['alignment' => 'center']);
    $itemsTable->addCell(700, ['bgColor' => '0066CC'])->addText('Status', ['bold' => true, 'color' => 'FFFFFF'], ['alignment' => 'center']);

    // Dados dos itens
    $itemNumber = 1;
    foreach ($items as $item) {
        $rowColor = ($itemNumber % 2 == 0) ? 'F8F9FA' : 'FFFFFF';

        $itemsTable->addRow();
        $itemsTable->addCell(800, ['bgColor' => $rowColor])->addText($itemNumber, ['bold' => true, 'color' => '0066CC'], ['alignment' => 'center']);
        $itemsTable->addCell(3000, ['bgColor' => $rowColor])->addText($item['name'], ['bold' => true]);
        $itemsTable->addCell(3500, ['bgColor' => $rowColor])->addText($item['description']);

        // Cor da quantidade baseada no valor
        $quantityColor = '6C757D'; // Baixa
        if ($item['quantity'] >= 10) {
            $quantityColor = 'DC3545'; // Alta - vermelho
        } elseif ($item['quantity'] >= 5) {
            $quantityColor = 'FFC107'; // Média - amarelo
        }

        $itemsTable->addCell(1000, ['bgColor' => $rowColor])->addText($item['quantity'], ['bold' => true, 'color' => $quantityColor], ['alignment' => 'center']);
        $itemsTable->addCell(1000, ['bgColor' => $rowColor])->addText($item['unit'], [], ['alignment' => 'center']);
        $itemsTable->addCell(700, ['bgColor' => $rowColor])->addText('⏳', [], ['alignment' => 'center']);

        $itemNumber++;
    }

    $section->addTextBreak(2);

    // Seção de assinaturas
    $section->addTitle('✍️ CONTROLE E ASSINATURAS', 2);
    $section->addTextBreak(1);

    $signatureTable = $section->addTable(['borderSize' => 6, 'borderColor' => '0066CC']);

    // Cabeçalhos
    $signatureTable->addRow();
    $signatureTable->addCell(3000, ['bgColor' => 'E3F2FD'])->addText('SOLICITADO POR', ['bold' => true], ['alignment' => 'center']);
    $signatureTable->addCell(3000, ['bgColor' => 'E8F5E8'])->addText('APROVADO POR', ['bold' => true], ['alignment' => 'center']);
    $signatureTable->addCell(3000, ['bgColor' => 'FFF3E0'])->addText('ENTREGUE POR', ['bold' => true], ['alignment' => 'center']);

    // Dados
    $signatureTable->addRow(1200);
    $cell1 = $signatureTable->addCell(3000);
    $cell1->addText($request['username'], ['bold' => true], ['alignment' => 'center']);
    $cell1->addTextBreak(2);
    $cell1->addText('_________________________', [], ['alignment' => 'center']);
    $cell1->addText(date('d/m/Y H:i', strtotime($request['request_date'])), [], ['alignment' => 'center']);

    $cell2 = $signatureTable->addCell(3000);
    $cell2->addTextBreak(2);
    $cell2->addText('_________________________', [], ['alignment' => 'center']);
    $cell2->addText('Nome: ___________________', [], ['alignment' => 'center']);
    $cell2->addText('Data: ___/___/______', [], ['alignment' => 'center']);

    $cell3 = $signatureTable->addCell(3000);
    $cell3->addTextBreak(2);
    $cell3->addText('_________________________', [], ['alignment' => 'center']);
    $cell3->addText('Nome: ___________________', [], ['alignment' => 'center']);
    $cell3->addText('Data: ___/___/______', [], ['alignment' => 'center']);

    $section->addTextBreak(2);

    // Seção de observações
    $section->addTitle('📝 OBSERVAÇÕES GERAIS', 3);
    $obsTable = $section->addTable(['borderSize' => 6, 'borderColor' => '0066CC']);
    $obsTable->addRow(1200);
    $obsCell = $obsTable->addCell(9000, ['bgColor' => 'F8F9FA']);
    $obsCell->addTextBreak(4);

    $section->addTextBreak(2);

    // Rodapé profissional
    $footerTable = $section->addTable(['borderSize' => 6, 'borderColor' => 'CCCCCC']);
    $footerTable->addRow();
    $footerTable->addCell(3000, ['bgColor' => 'F8F9FA'])->addText('Sistema de Requisição\nMaterial de Cozinha v2.0', ['size' => 9, 'bold' => true], ['alignment' => 'center']);
    $footerTable->addCell(3000, ['bgColor' => 'F8F9FA'])->addText('Relatório Gerado\n' . date('d/m/Y H:i:s'), ['size' => 9, 'bold' => true], ['alignment' => 'center']);
    $footerTable->addCell(3000, ['bgColor' => 'F8F9FA'])->addText('Gerado por\n' . ($_SESSION['username'] ?? 'Sistema'), ['size' => 9, 'bold' => true], ['alignment' => 'center']);
    
    // Salvar arquivo
    $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    
    // Headers para download
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $objWriter->save('php://output');
    exit;
    
} catch (Exception $e) {
    // Fallback para RTF
    generateRTF($request, $items, $statusLabel, $filename);
    exit;
}

function generateRTF($request, $items, $statusLabel, $filename) {
    // Gerar RTF (Rich Text Format) como fallback melhorado
    $rtfFilename = str_replace('.docx', '.rtf', $filename);

    header('Content-Type: application/rtf');
    header('Content-Disposition: attachment; filename="' . $rtfFilename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    // Calcular estatísticas
    $totalItems = count($items);
    $totalQuantity = array_sum(array_column($items, 'quantity'));

    echo '{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Arial;}{\\f1 Times New Roman;}}';
    echo '{\\colortbl;\\red0\\green102\\blue204;\\red40\\green167\\blue69;\\red255\\green193\\blue7;\\red220\\green53\\blue69;}';
    echo '\\f0\\fs24';

    // Cabeçalho principal
    echo '{\\b\\fs36\\qc\\cf1 SISTEMA DE REQUISIÇÃO DE MATERIAL DE COZINHA}\\par';
    echo '{\\b\\fs28\\qc ' . ($request['title'] ?? 'Requisição #' . $request['id']) . '}\\par\\par';

    // Seção de informações gerais
    echo '{\\b\\fs24\\cf1 INFORMAÇÕES GERAIS}\\par';
    echo '\\par';
    echo '{\\b ID da Requisição:} #' . $request['id'] . '\\tab\\tab {\\b Solicitante:} ' . $request['username'] . '\\par';
    echo '{\\b Data da Requisição:} ' . date('d/m/Y H:i', strtotime($request['request_date'])) . '\\tab\\tab {\\b Status:} {\\b\\cf1 ' . strtoupper($statusLabel) . '}\\par';
    echo '\\par';

    // Seção de estatísticas
    echo '{\\b\\fs24\\cf1 ESTATÍSTICAS}\\par';
    echo '\\par';
    echo '{\\b Total de Tipos de Itens:} {\\b\\cf2 ' . $totalItems . '}\\tab\\tab {\\b Quantidade Total:} {\\b\\cf2 ' . $totalQuantity . '}\\par';
    echo '{\\b Média por Item:} {\\b\\cf3 ' . number_format($totalQuantity / max($totalItems, 1), 1) . '}\\tab\\tab {\\b Data de Geração:} ' . date('d/m/Y') . '\\par';
    echo '\\par';

    // Seção de itens detalhada
    echo '{\\b\\fs24\\cf1 ITENS SOLICITADOS - DETALHAMENTO COMPLETO}\\par';
    echo '\\par';

    // Cabeçalho da tabela melhorado
    echo '{\\b Nº\\tab Nome do Item\\tab\\tab Descrição Completa\\tab\\tab Quantidade\\tab Unidade\\tab Status}\\par';
    echo '\\par';

    // Itens com numeração e cores
    $itemNumber = 1;
    foreach ($items as $item) {
        // Cor da quantidade baseada no valor
        $quantityColor = '\\cf0'; // Padrão
        if ($item['quantity'] >= 10) {
            $quantityColor = '\\cf4'; // Vermelho para alta
        } elseif ($item['quantity'] >= 5) {
            $quantityColor = '\\cf3'; // Amarelo para média
        }

        echo '{\\b\\cf1 ' . $itemNumber . '}\\tab ';
        echo '{\\b ' . $item['name'] . '}\\tab\\tab ';
        echo $item['description'] . '\\tab\\tab ';
        echo '{\\b' . $quantityColor . ' ' . $item['quantity'] . '}\\tab ';
        echo $item['unit'] . '\\tab ';
        echo 'Pendente\\par';
        $itemNumber++;
    }

    echo '\\par';

    // Seção de assinaturas
    echo '{\\b\\fs24\\cf1 CONTROLE E ASSINATURAS}\\par';
    echo '\\par';
    echo '{\\b SOLICITADO POR}\\tab\\tab\\tab {\\b APROVADO POR}\\tab\\tab\\tab {\\b ENTREGUE POR}\\par';
    echo '\\par';
    echo $request['username'] . '\\tab\\tab\\tab _________________\\tab\\tab\\tab _________________\\par';
    echo date('d/m/Y H:i', strtotime($request['request_date'])) . '\\tab\\tab\\tab Data: ___/___/___\\tab\\tab\\tab Data: ___/___/___\\par';
    echo '\\par';
    echo '_________________\\tab\\tab\\tab _________________\\tab\\tab\\tab _________________\\par';
    echo 'Assinatura\\tab\\tab\\tab\\tab Assinatura\\tab\\tab\\tab\\tab Assinatura\\par';
    echo '\\par';

    // Seção de observações
    echo '{\\b\\fs24\\cf1 OBSERVAÇÕES GERAIS}\\par';
    echo '\\par';
    echo '_________________________________________________________________\\par';
    echo '_________________________________________________________________\\par';
    echo '_________________________________________________________________\\par';
    echo '_________________________________________________________________\\par';
    echo '\\par';

    // Rodapé profissional
    echo '{\\b\\fs20\\qc INFORMAÇÕES DO RELATÓRIO}\\par';
    echo '\\par';
    echo '{\\i\\qc Sistema de Requisição de Material de Cozinha v2.0}\\par';
    echo '{\\i\\qc Relatório gerado em: ' . date('d/m/Y H:i:s') . '}\\par';
    echo '{\\i\\qc Gerado por: ' . ($_SESSION['username'] ?? 'Sistema') . '}\\par';
    echo '{\\i\\qc Este documento possui validade legal para controle interno}\\par';

    echo '}';
    exit;
}
?>
