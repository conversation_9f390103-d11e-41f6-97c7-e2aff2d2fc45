import axios from 'axios';

// Configuração base da API
const API_BASE_URL = 'http://localhost/projetos/os_cozinha/api';

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Interceptor para adicionar token automaticamente
    this.api.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Interceptor para tratar respostas
    this.api.interceptors.response.use(
      (response) => {
        return response.data;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token expirado ou inválido
          this.clearAuthToken();
        }
        return Promise.reject(error);
      }
    );

    this.authToken = null;
  }

  // Configurar token de autenticação
  setAuthToken(token) {
    this.authToken = token;
  }

  // Limpar token de autenticação
  clearAuthToken() {
    this.authToken = null;
  }

  // ===== AUTENTICAÇÃO =====

  async login(username, password) {
    try {
      const response = await this.api.post('/auth', {
        username,
        password,
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  async logout() {
    try {
      await this.api.delete('/auth');
      this.clearAuthToken();
    } catch (error) {
      // Ignorar erros de logout
      this.clearAuthToken();
    }
  }

  async validateToken(token) {
    try {
      const tempToken = this.authToken;
      this.setAuthToken(token);
      
      const response = await this.api.get('/auth');
      
      if (!tempToken) {
        this.clearAuthToken();
      } else {
        this.setAuthToken(tempToken);
      }
      
      return response.success;
    } catch (error) {
      return false;
    }
  }

  // ===== REQUISIÇÕES =====

  async getRequests(page = 1, perPage = 10) {
    try {
      const response = await this.api.get('/requests', {
        params: { page, per_page: perPage }
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  async getRequestDetails(id) {
    try {
      const response = await this.api.get(`/requests/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  }

  async createRequest(requestData) {
    try {
      const response = await this.api.post('/requests', requestData);
      return response;
    } catch (error) {
      throw error;
    }
  }

  async updateRequestStatus(id, status, notes = '', reason = '') {
    try {
      const response = await this.api.put(`/requests/${id}`, {
        status,
        notes,
        reason,
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  async deleteRequest(id) {
    try {
      const response = await this.api.delete(`/requests/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  }

  // ===== ITENS =====

  async getItems(page = 1, perPage = 20, search = '') {
    try {
      const response = await this.api.get('/items', {
        params: { page, per_page: perPage, search }
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  // ===== USUÁRIOS =====

  async getUsers() {
    try {
      const response = await this.api.get('/users');
      return response;
    } catch (error) {
      throw error;
    }
  }

  // ===== ESTATÍSTICAS =====

  async getStats() {
    try {
      const response = await this.api.get('/stats');
      return response;
    } catch (error) {
      throw error;
    }
  }

  // ===== UTILITÁRIOS =====

  // Verificar conectividade
  async checkConnection() {
    try {
      const response = await this.api.get('/auth');
      return true;
    } catch (error) {
      return false;
    }
  }

  // Upload de arquivo (para futuras implementações)
  async uploadFile(file, endpoint) {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await this.api.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Busca genérica
  async search(query, type = 'items') {
    try {
      const response = await this.api.get(`/${type}`, {
        params: { search: query, per_page: 50 }
      });
      return response;
    } catch (error) {
      throw error;
    }
  }
}

// Exportar instância única
export const apiService = new ApiService();

// Exportar classe para testes
export { ApiService };
