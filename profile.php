<?php
// Iniciar sessão se não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Incluir configuração de página
require_once 'includes/page_config.php';

// Aplicar configuração automática da página
initPage();

// Incluir layout
require_once 'includes/layout.php';

// Incluir dependências
require_once 'config/db_connect.php';
require_once 'includes/AvatarUploader.php';

// Inicializar uploader de avatar
$avatarUploader = new AvatarUploader();

// Processar formulários
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_profile':
                $result = updateProfile();
                $message = $result['message'];
                $messageType = $result['success'] ? 'success' : 'danger';
                break;
                
            case 'upload_avatar':
                if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] !== UPLOAD_ERR_NO_FILE) {
                    $result = $avatarUploader->uploadAvatar($_FILES['avatar'], $_SESSION['user_id']);
                    if ($result['success']) {
                        // Atualizar campo avatar na tabela users
                        $stmt = $pdo->prepare("UPDATE users SET avatar = ? WHERE id = ?");
                        $stmt->execute([$result['filename'], $_SESSION['user_id']]);
                        
                        $message = $result['message'];
                        $messageType = 'success';
                    } else {
                        $message = implode('<br>', $result['errors']);
                        $messageType = 'danger';
                    }
                } else {
                    $message = 'Nenhum arquivo selecionado.';
                    $messageType = 'warning';
                }
                break;
                
            case 'remove_avatar':
                $result = $avatarUploader->removeAvatar($_SESSION['user_id']);
                if ($result['success']) {
                    // Limpar campo avatar na tabela users
                    $stmt = $pdo->prepare("UPDATE users SET avatar = NULL WHERE id = ?");
                    $stmt->execute([$_SESSION['user_id']]);
                    
                    $message = $result['message'];
                    $messageType = 'success';
                } else {
                    $message = implode('<br>', $result['errors']);
                    $messageType = 'danger';
                }
                break;
                
            case 'change_password':
                $result = changePassword();
                $message = $result['message'];
                $messageType = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// Buscar dados do usuário
$stmt = $pdo->prepare("
    SELECT u.*, 
           (SELECT COUNT(*) FROM requests WHERE user_id = u.id) as total_requests,
           (SELECT COUNT(*) FROM requests WHERE user_id = u.id AND status = 'approved') as approved_requests
    FROM users u 
    WHERE u.id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Função para atualizar perfil
function updateProfile() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            UPDATE users SET 
                full_name = ?,
                email = ?,
                phone = ?,
                department = ?,
                position = ?,
                bio = ?,
                birth_date = ?,
                profile_updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $birthDate = !empty($_POST['birth_date']) ? $_POST['birth_date'] : null;
        
        $stmt->execute([
            $_POST['full_name'],
            $_POST['email'],
            $_POST['phone'],
            $_POST['department'],
            $_POST['position'],
            $_POST['bio'],
            $birthDate,
            $_SESSION['user_id']
        ]);
        
        // Atualizar nome na sessão se foi alterado
        if (!empty($_POST['full_name'])) {
            $_SESSION['full_name'] = $_POST['full_name'];
        }
        
        return ['success' => true, 'message' => 'Perfil atualizado com sucesso!'];
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Erro ao atualizar perfil: ' . $e->getMessage()];
    }
}

// Função para alterar senha
function changePassword() {
    global $pdo;
    
    if (empty($_POST['current_password']) || empty($_POST['new_password']) || empty($_POST['confirm_password'])) {
        return ['success' => false, 'message' => 'Todos os campos de senha são obrigatórios.'];
    }
    
    if ($_POST['new_password'] !== $_POST['confirm_password']) {
        return ['success' => false, 'message' => 'Nova senha e confirmação não coincidem.'];
    }
    
    if (strlen($_POST['new_password']) < 6) {
        return ['success' => false, 'message' => 'Nova senha deve ter pelo menos 6 caracteres.'];
    }
    
    try {
        // Verificar senha atual
        $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $currentHash = $stmt->fetchColumn();
        
        if (!password_verify($_POST['current_password'], $currentHash)) {
            return ['success' => false, 'message' => 'Senha atual incorreta.'];
        }
        
        // Atualizar senha
        $newHash = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([$newHash, $_SESSION['user_id']]);
        
        return ['success' => true, 'message' => 'Senha alterada com sucesso!'];
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Erro ao alterar senha: ' . $e->getMessage()];
    }
}

// Obter URL do avatar
$avatarUrl = $avatarUploader->getAvatarUrl($_SESSION['user_id'], 'large');

// Garantir que $avatarUrl não seja null
if ($avatarUrl === null || empty($avatarUrl)) {
    $avatarUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDE1MCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNTAiIGhlaWdodD0iMTUwIiBmaWxsPSIjNmM3NTdkIi8+CjxjaXJjbGUgY3g9Ijc1IiBjeT0iNjAiIHI9IjIwIiBmaWxsPSJ3aGl0ZSIgZmlsbC1vcGFjaXR5PSIwLjgiLz4KPHBhdGggZD0iTTc1IDEwMEM2MC41IDEwMCA0OC41IDEwNy41IDQyIDExOEg0MlYxMzBIMTA4VjExOEM5OS41IDEwNy41IDg5LjUgMTAwIDc1IDEwMFoiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuOCIvPgo8L3N2Zz4K';
}
?>

<!-- Mensagens de feedback -->
<?php if (!empty($message)): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Perfil do Usuário -->
<div class="row">
    <!-- Coluna da Esquerda - Avatar e Informações Básicas -->
    <div class="col-md-4">
        <!-- Card do Avatar -->
        <div class="content-card text-center">
            <div class="position-relative d-inline-block mb-3">
                <img src="<?php echo htmlspecialchars($avatarUrl); ?>"
                     alt="Avatar"
                     class="rounded-circle border border-3 border-primary"
                     style="width: 150px; height: 150px; object-fit: cover;"
                     id="avatar-preview">
                
                <!-- Badge de status online -->
                <span class="position-absolute bottom-0 end-0 translate-middle p-2 bg-success border border-light rounded-circle">
                    <span class="visually-hidden">Online</span>
                </span>
            </div>
            
            <h4 class="mb-1"><?php echo htmlspecialchars($user['full_name'] ?: $user['username']); ?></h4>
            <p class="text-muted mb-3"><?php echo htmlspecialchars($user['position'] ?: 'Funcionário'); ?></p>
            
            <!-- Upload de Avatar -->
            <form method="POST" enctype="multipart/form-data" class="mb-3">
                <input type="hidden" name="action" value="upload_avatar">
                <div class="mb-2">
                    <input type="file" 
                           class="form-control form-control-sm" 
                           name="avatar" 
                           accept="image/*"
                           onchange="previewAvatar(this)">
                    <div class="form-text">JPG, PNG, GIF ou WebP. Máximo 5MB.</div>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-upload me-1"></i>Enviar Avatar
                    </button>
                    <?php if ($user['avatar']): ?>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAvatar()">
                        <i class="fas fa-trash me-1"></i>Remover Avatar
                    </button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
        
        <!-- Card de Estatísticas -->
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-bar text-primary me-2"></i>
                Estatísticas
            </h5>
            <div class="row text-center">
                <div class="col-6">
                    <div class="border-end">
                        <h4 class="text-primary mb-0"><?php echo $user['total_requests']; ?></h4>
                        <small class="text-muted">Requisições</small>
                    </div>
                </div>
                <div class="col-6">
                    <h4 class="text-success mb-0"><?php echo $user['approved_requests']; ?></h4>
                    <small class="text-muted">Aprovadas</small>
                </div>
            </div>
            
            <hr>
            
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">Membro desde:</small>
                <small class="fw-bold">
                    <?php echo date('d/m/Y', strtotime($user['created_at'] ?? 'now')); ?>
                </small>
            </div>
            
            <?php if ($user['last_login']): ?>
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">Último acesso:</small>
                <small class="fw-bold">
                    <?php echo date('d/m/Y H:i', strtotime($user['last_login'])); ?>
                </small>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Coluna da Direita - Formulários -->
    <div class="col-md-8">
        <!-- Tabs de Navegação -->
        <ul class="nav nav-tabs mb-4" id="profileTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab">
                    <i class="fas fa-user me-1"></i>Informações Pessoais
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                    <i class="fas fa-lock me-1"></i>Segurança
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="preferences-tab" data-bs-toggle="tab" data-bs-target="#preferences" type="button" role="tab">
                    <i class="fas fa-cog me-1"></i>Preferências
                </button>
            </li>
        </ul>
        
        <!-- Conteúdo das Tabs -->
        <div class="tab-content" id="profileTabsContent">
            <!-- Tab Informações Pessoais -->
            <div class="tab-pane fade show active" id="info" role="tabpanel">
                <div class="content-card">
                    <h5 class="mb-4">
                        <i class="fas fa-user text-primary me-2"></i>
                        Informações Pessoais
                    </h5>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="update_profile">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">Nome Completo *</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="full_name" 
                                       name="full_name" 
                                       value="<?php echo htmlspecialchars($user['full_name'] ?: ''); ?>"
                                       required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       value="<?php echo htmlspecialchars($user['email'] ?: ''); ?>"
                                       required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Telefone</label>
                                <input type="tel" 
                                       class="form-control" 
                                       id="phone" 
                                       name="phone" 
                                       value="<?php echo htmlspecialchars($user['phone'] ?: ''); ?>"
                                       placeholder="(11) 99999-9999">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="birth_date" class="form-label">Data de Nascimento</label>
                                <input type="date" 
                                       class="form-control" 
                                       id="birth_date" 
                                       name="birth_date" 
                                       value="<?php echo $user['birth_date'] ?: ''; ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">Departamento</label>
                                <select class="form-select" id="department" name="department">
                                    <option value="">Selecione...</option>
                                    <option value="Cozinha" <?php echo ($user['department'] === 'Cozinha') ? 'selected' : ''; ?>>Cozinha</option>
                                    <option value="Administração" <?php echo ($user['department'] === 'Administração') ? 'selected' : ''; ?>>Administração</option>
                                    <option value="Compras" <?php echo ($user['department'] === 'Compras') ? 'selected' : ''; ?>>Compras</option>
                                    <option value="Estoque" <?php echo ($user['department'] === 'Estoque') ? 'selected' : ''; ?>>Estoque</option>
                                    <option value="Recursos Humanos" <?php echo ($user['department'] === 'Recursos Humanos') ? 'selected' : ''; ?>>Recursos Humanos</option>
                                    <option value="Outro" <?php echo ($user['department'] === 'Outro') ? 'selected' : ''; ?>>Outro</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Cargo</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="position" 
                                       name="position" 
                                       value="<?php echo htmlspecialchars($user['position'] ?: ''); ?>"
                                       placeholder="Ex: Cozinheiro, Gerente, etc.">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="bio" class="form-label">Biografia</label>
                            <textarea class="form-control" 
                                      id="bio" 
                                      name="bio" 
                                      rows="3" 
                                      placeholder="Conte um pouco sobre você..."><?php echo htmlspecialchars($user['bio'] ?: ''); ?></textarea>
                            <div class="form-text">Máximo 500 caracteres.</div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-custom">
                                <i class="fas fa-save me-2"></i>Salvar Alterações
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tab Segurança -->
            <div class="tab-pane fade" id="security" role="tabpanel">
                <div class="content-card">
                    <h5 class="mb-4">
                        <i class="fas fa-lock text-primary me-2"></i>
                        Alterar Senha
                    </h5>

                    <form method="POST" id="passwordForm">
                        <input type="hidden" name="action" value="change_password">

                        <div class="mb-3">
                            <label for="current_password" class="form-label">Senha Atual *</label>
                            <div class="input-group">
                                <input type="password"
                                       class="form-control"
                                       id="current_password"
                                       name="current_password"
                                       required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="new_password" class="form-label">Nova Senha *</label>
                            <div class="input-group">
                                <input type="password"
                                       class="form-control"
                                       id="new_password"
                                       name="new_password"
                                       minlength="6"
                                       required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Mínimo 6 caracteres.</div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirmar Nova Senha *</label>
                            <div class="input-group">
                                <input type="password"
                                       class="form-control"
                                       id="confirm_password"
                                       name="confirm_password"
                                       minlength="6"
                                       required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Indicador de força da senha -->
                        <div class="mb-3">
                            <div class="password-strength">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" id="password-strength-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small id="password-strength-text" class="text-muted">Digite uma senha</small>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning btn-custom">
                                <i class="fas fa-key me-2"></i>Alterar Senha
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Informações de Segurança -->
                <div class="content-card mt-4">
                    <h5 class="mb-3">
                        <i class="fas fa-shield-alt text-success me-2"></i>
                        Informações de Segurança
                    </h5>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-user-check text-success me-3"></i>
                                <div>
                                    <strong>Usuário:</strong><br>
                                    <small class="text-muted"><?php echo htmlspecialchars($user['username']); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-user-tag text-primary me-3"></i>
                                <div>
                                    <strong>Perfil:</strong><br>
                                    <small class="text-muted">
                                        <?php echo $user['role'] === 'admin' ? 'Administrador' : 'Usuário'; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if ($user['last_login']): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Último acesso:</strong>
                        <?php echo date('d/m/Y \à\s H:i', strtotime($user['last_login'])); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Tab Preferências -->
            <div class="tab-pane fade" id="preferences" role="tabpanel">
                <div class="content-card">
                    <h5 class="mb-4">
                        <i class="fas fa-cog text-primary me-2"></i>
                        Preferências do Sistema
                    </h5>

                    <form method="POST" id="preferencesForm">
                        <input type="hidden" name="action" value="update_preferences">

                        <!-- Notificações -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-bell text-warning me-2"></i>
                                Notificações
                            </h6>

                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                                <label class="form-check-label" for="email_notifications">
                                    Receber notificações por email
                                </label>
                            </div>

                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="request_updates" checked>
                                <label class="form-check-label" for="request_updates">
                                    Atualizações de requisições
                                </label>
                            </div>

                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="system_news">
                                <label class="form-check-label" for="system_news">
                                    Novidades do sistema
                                </label>
                            </div>
                        </div>

                        <!-- Interface -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-palette text-info me-2"></i>
                                Interface
                            </h6>

                            <div class="mb-3">
                                <label for="theme" class="form-label">Tema</label>
                                <select class="form-select" id="theme">
                                    <option value="light">Claro</option>
                                    <option value="dark">Escuro</option>
                                    <option value="auto">Automático</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="language" class="form-label">Idioma</label>
                                <select class="form-select" id="language">
                                    <option value="pt-BR" selected>Português (Brasil)</option>
                                    <option value="en-US">English (US)</option>
                                    <option value="es-ES">Español</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="items_per_page" class="form-label">Itens por página</label>
                                <select class="form-select" id="items_per_page">
                                    <option value="10">10</option>
                                    <option value="25" selected>25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>

                        <!-- Privacidade -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-user-secret text-secondary me-2"></i>
                                Privacidade
                            </h6>

                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="profile_public">
                                <label class="form-check-label" for="profile_public">
                                    Perfil visível para outros usuários
                                </label>
                            </div>

                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="show_online_status" checked>
                                <label class="form-check-label" for="show_online_status">
                                    Mostrar status online
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-custom">
                                <i class="fas fa-save me-2"></i>Salvar Preferências
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Zona de Perigo -->
                <div class="content-card mt-4 border-danger">
                    <h5 class="mb-3 text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Zona de Perigo
                    </h5>

                    <p class="text-muted mb-3">
                        Ações irreversíveis que afetam permanentemente sua conta.
                    </p>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-warning" onclick="exportUserData()">
                            <i class="fas fa-download me-2"></i>Exportar Meus Dados
                        </button>

                        <button type="button" class="btn btn-outline-danger" onclick="confirmDeleteAccount()">
                            <i class="fas fa-user-times me-2"></i>Excluir Conta
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Remover Avatar -->
<div class="modal fade" id="removeAvatarModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash text-danger me-2"></i>
                    Remover Avatar
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja remover seu avatar?</p>
                <p class="text-muted">Esta ação não pode ser desfeita. Seu avatar padrão será exibido.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="remove_avatar">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Remover
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Scripts específicos da página
$page_scripts = '
<script>
// Preview do avatar antes do upload
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById("avatar-preview").src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Remover avatar com confirmação
function removeAvatar() {
    const modal = new bootstrap.Modal(document.getElementById("removeAvatarModal"));
    modal.show();
}

// Toggle visibilidade da senha
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector("i");

    if (field.type === "password") {
        field.type = "text";
        button.className = "fas fa-eye-slash";
    } else {
        field.type = "password";
        button.className = "fas fa-eye";
    }
}

// Verificar força da senha
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = "";

    if (password.length >= 6) strength += 1;
    if (password.length >= 8) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    const strengthBar = document.getElementById("password-strength-bar");
    const strengthText = document.getElementById("password-strength-text");

    switch (strength) {
        case 0:
        case 1:
            strengthBar.style.width = "20%";
            strengthBar.className = "progress-bar bg-danger";
            feedback = "Muito fraca";
            break;
        case 2:
        case 3:
            strengthBar.style.width = "40%";
            strengthBar.className = "progress-bar bg-warning";
            feedback = "Fraca";
            break;
        case 4:
            strengthBar.style.width = "60%";
            strengthBar.className = "progress-bar bg-info";
            feedback = "Média";
            break;
        case 5:
            strengthBar.style.width = "80%";
            strengthBar.className = "progress-bar bg-success";
            feedback = "Forte";
            break;
        case 6:
            strengthBar.style.width = "100%";
            strengthBar.className = "progress-bar bg-success";
            feedback = "Muito forte";
            break;
    }

    strengthText.textContent = feedback;
}

// Validar confirmação de senha
function validatePasswordConfirmation() {
    const newPassword = document.getElementById("new_password").value;
    const confirmPassword = document.getElementById("confirm_password").value;
    const confirmField = document.getElementById("confirm_password");

    if (newPassword !== confirmPassword) {
        confirmField.setCustomValidity("As senhas não coincidem");
        confirmField.classList.add("is-invalid");
    } else {
        confirmField.setCustomValidity("");
        confirmField.classList.remove("is-invalid");
        if (confirmPassword.length > 0) {
            confirmField.classList.add("is-valid");
        }
    }
}

// Máscara para telefone
function formatPhone(input) {
    let value = input.value.replace(/\D/g, "");

    if (value.length <= 10) {
        value = value.replace(/(\d{2})(\d{4})(\d{4})/, "($1) $2-$3");
    } else {
        value = value.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
    }

    input.value = value;
}

// Event listeners
document.addEventListener("DOMContentLoaded", function() {
    // Verificar força da senha
    const newPasswordField = document.getElementById("new_password");
    if (newPasswordField) {
        newPasswordField.addEventListener("input", function() {
            checkPasswordStrength(this.value);
        });
    }

    // Validar confirmação de senha
    const confirmPasswordField = document.getElementById("confirm_password");
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener("input", validatePasswordConfirmation);
        newPasswordField.addEventListener("input", validatePasswordConfirmation);
    }

    // Máscara para telefone
    const phoneField = document.getElementById("phone");
    if (phoneField) {
        phoneField.addEventListener("input", function() {
            formatPhone(this);
        });
    }

    // Contador de caracteres para biografia
    const bioField = document.getElementById("bio");
    if (bioField) {
        bioField.addEventListener("input", function() {
            const maxLength = 500;
            const currentLength = this.value.length;
            const remaining = maxLength - currentLength;

            let countElement = document.getElementById("bio-count");
            if (!countElement) {
                countElement = document.createElement("small");
                countElement.id = "bio-count";
                countElement.className = "form-text";
                this.parentNode.appendChild(countElement);
            }

            countElement.textContent = `${currentLength}/${maxLength} caracteres`;

            if (remaining < 50) {
                countElement.className = "form-text text-warning";
            } else if (remaining < 0) {
                countElement.className = "form-text text-danger";
            } else {
                countElement.className = "form-text text-muted";
            }
        });
    }
});

// Exportar dados do usuário
function exportUserData() {
    if (confirm("Deseja exportar todos os seus dados do sistema?")) {
        window.location.href = "export_user_data.php";
    }
}

// Confirmar exclusão de conta
function confirmDeleteAccount() {
    const confirmation = prompt("Para confirmar a exclusão da sua conta, digite: EXCLUIR");
    if (confirmation === "EXCLUIR") {
        if (confirm("ATENÇÃO: Esta ação é irreversível! Tem certeza absoluta?")) {
            alert("Funcionalidade em desenvolvimento. Entre em contato com o administrador.");
        }
    } else if (confirmation !== null) {
        alert("Texto de confirmação incorreto. Exclusão cancelada.");
    }
}
</script>
';

// Incluir footer do layout
require_once 'includes/layout_footer.php';
?>
