# 👤 Sistema de Perfil do Usuário - Documentação Completa

## 🎯 Visão Geral

Sistema completo de gerenciamento de perfil de usuário com upload de avatar, edição de informações pessoais, configurações de segurança e preferências do sistema.

### **✨ Funcionalidades Principais:**
- ✅ **Upload de Avatar** com múltiplos tamanhos
- ✅ **Edição de Informações** pessoais e profissionais
- ✅ **Alteração de Senha** com validação de força
- ✅ **Preferências do Sistema** personalizáveis
- ✅ **Configurações de Privacidade**
- ✅ **Estatísticas do Usuário**
- ✅ **Interface Responsiva** e moderna

## 🏗️ Arquitetura do Sistema

### **📁 Arquivos Criados:**

#### **1. database/update_profile_table.sql**
- **Função:** Script SQL para atualizar estrutura do banco
- **Conteúdo:**
  - ✅ Novas colunas na tabela `users`
  - ✅ Tabela `avatar_uploads` para log de uploads
  - ✅ Índices para performance
  - ✅ Comentários de documentação

#### **2. includes/AvatarUploader.php**
- **Função:** Classe para gerenciar upload de avatars
- **Recursos:**
  - ✅ Upload com validação completa
  - ✅ Redimensionamento automático
  - ✅ Múltiplos tamanhos (thumb, small, medium, large, original)
  - ✅ Suporte a JPG, PNG, GIF, WebP
  - ✅ Log de uploads no banco

#### **3. profile.php**
- **Função:** Página principal do perfil
- **Seções:**
  - ✅ Informações Pessoais
  - ✅ Segurança (alteração de senha)
  - ✅ Preferências do sistema
  - ✅ Estatísticas do usuário

#### **4. generate_default_avatars.html**
- **Função:** Gerador de avatars padrão
- **Tamanhos:** 32x32, 50x50, 100x100, 200x200

#### **5. includes/favicon.php**
- **Atualização:** Avatar no header do layout

## 🗄️ Estrutura do Banco de Dados

### **📊 Tabela `users` (Atualizada):**

```sql
-- Colunas existentes
id INT PRIMARY KEY
username VARCHAR(50)
password VARCHAR(255)
role ENUM('user', 'admin')
created_at TIMESTAMP

-- Novas colunas adicionadas
avatar VARCHAR(255)              -- Caminho do avatar
full_name VARCHAR(255)           -- Nome completo
email VARCHAR(255)               -- Email do usuário
phone VARCHAR(20)                -- Telefone
department VARCHAR(100)          -- Departamento
position VARCHAR(100)            -- Cargo
bio TEXT                         -- Biografia
birth_date DATE                  -- Data de nascimento
hire_date DATE                   -- Data de contratação
last_login TIMESTAMP             -- Último login
profile_updated_at TIMESTAMP     -- Última atualização do perfil
preferences JSON                 -- Preferências em JSON
```

### **📊 Tabela `avatar_uploads` (Nova):**

```sql
id INT PRIMARY KEY AUTO_INCREMENT
user_id INT                      -- FK para users
original_filename VARCHAR(255)   -- Nome original do arquivo
stored_filename VARCHAR(255)     -- Nome armazenado
file_size INT                    -- Tamanho do arquivo
mime_type VARCHAR(100)           -- Tipo MIME
upload_date TIMESTAMP            -- Data do upload
is_active BOOLEAN                -- Avatar ativo
```

## 🎨 Interface do Usuário

### **📱 Layout Responsivo:**

```
┌─────────────────────────────────────────────────────────┐
│                    HEADER                               │
│  🍽️ Sistema    👤 [Avatar] João Silva ⚙️ Menu        │
├─────────────────────────────────────────────────────────┤
│                 NAVEGAÇÃO                               │
│  🏠 Dashboard  📄 Requisições  👤 Perfil               │
├─────────────┬───────────────────────────────────────────┤
│   SIDEBAR   │              PERFIL                       │
│             │                                           │
│ 🚀 Ações    │  ┌─────────────────────────────────────┐  │
│ Rápidas     │  │         AVATAR & INFO               │  │
│             │  │  👤 [150x150]                      │  │
│ 📋 Requis.  │  │  João Silva                        │  │
│ • Minhas    │  │  Cozinheiro                        │  │
│ • Todas     │  │  📊 Estatísticas                   │  │
│             │  └─────────────────────────────────────┘  │
│ 📦 Itens    │                                           │
│ • Lista     │  ┌─────────────────────────────────────┐  │
│ • Gerenciar │  │         TABS DE CONFIGURAÇÃO        │  │
│             │  │  👤 Info  🔒 Segurança  ⚙️ Pref.   │  │
│ ⚙️ Admin    │  │                                     │  │
│ • Usuários  │  │  [Formulários de Edição]            │  │
├─────────────┴───┴─────────────────────────────────────┘  │
│                    FOOTER                               │
└─────────────────────────────────────────────────────────┘
```

### **🎨 Seções da Página:**

#### **1. 👤 Avatar e Informações Básicas:**
- **Avatar:** 150x150px com preview
- **Upload:** Drag & drop ou seleção de arquivo
- **Validação:** Tipos permitidos, tamanho máximo
- **Estatísticas:** Requisições totais e aprovadas
- **Status:** Online/offline indicator

#### **2. 📝 Informações Pessoais:**
```html
<!-- Formulário de Informações -->
Nome Completo *     [________________]
Email *             [________________]
Telefone            [________________]
Data de Nascimento  [________________]
Departamento        [Dropdown_______]
Cargo               [________________]
Biografia           [________________]
                    [________________]
                    [________________]
                    [Salvar Alterações]
```

#### **3. 🔒 Segurança:**
```html
<!-- Formulário de Senha -->
Senha Atual *       [________________] 👁️
Nova Senha *        [________________] 👁️
Confirmar Senha *   [________________] 👁️

Força da Senha:     [████████████████] Muito forte

Informações de Segurança:
✅ Usuário: joao.silva
✅ Perfil: Administrador
ℹ️ Último acesso: 15/01/2024 às 14:30
```

#### **4. ⚙️ Preferências:**
```html
<!-- Notificações -->
🔔 Notificações
☑️ Receber notificações por email
☑️ Atualizações de requisições
☐ Novidades do sistema

<!-- Interface -->
🎨 Interface
Tema:           [Claro ▼]
Idioma:         [Português (Brasil) ▼]
Itens/página:   [25 ▼]

<!-- Privacidade -->
🔒 Privacidade
☐ Perfil visível para outros usuários
☑️ Mostrar status online
```

## 🔧 Sistema de Upload de Avatar

### **📸 Funcionalidades:**

#### **✅ Validações Implementadas:**
- **Tipos permitidos:** JPG, PNG, GIF, WebP
- **Tamanho máximo:** 5MB
- **Dimensões mínimas:** 50x50px
- **Dimensões máximas:** 2000x2000px
- **Verificação MIME:** Validação real do tipo de arquivo
- **Verificação de imagem:** getimagesize() para confirmar

#### **🎯 Tamanhos Gerados:**
```php
$avatarSizes = [
    'original' => null,    // Máximo 800x800
    'large' => 200,        // Para perfil
    'medium' => 100,       // Para header
    'small' => 50,         // Para comentários/listas
    'thumb' => 32          // Para mini avatars
];
```

#### **📁 Estrutura de Arquivos:**
```
assets/uploads/avatars/
├── original/
│   └── avatar_123_1640995200.jpg
├── large/
│   └── avatar_123_1640995200.jpg
├── medium/
│   └── avatar_123_1640995200.jpg
├── small/
│   └── avatar_123_1640995200.jpg
└── thumb/
    └── avatar_123_1640995200.jpg
```

### **🔄 Processo de Upload:**

#### **1. Validação:**
```php
// Verificar erro de upload
if ($file['error'] !== UPLOAD_ERR_OK) return false;

// Verificar tamanho
if ($file['size'] > $maxFileSize) return false;

// Verificar tipo MIME
$mimeType = finfo_file(finfo_open(FILEINFO_MIME_TYPE), $file['tmp_name']);
if (!in_array($mimeType, $allowedTypes)) return false;

// Verificar se é imagem válida
$imageInfo = getimagesize($file['tmp_name']);
if ($imageInfo === false) return false;
```

#### **2. Processamento:**
```php
// Gerar nome único
$filename = 'avatar_' . $userId . '_' . time() . '.' . $extension;

// Criar imagem source
$sourceImage = $this->createImageFromFile($file['tmp_name']);

// Redimensionar para cada tamanho
foreach ($this->avatarSizes as $sizeName => $dimension) {
    $resizedImage = $this->resizeImage($sourceImage, ...);
    $this->saveImage($resizedImage, $filePath, $extension);
}
```

#### **3. Registro no Banco:**
```php
// Desativar avatars anteriores
UPDATE avatar_uploads SET is_active = FALSE WHERE user_id = ?

// Registrar novo upload
INSERT INTO avatar_uploads (user_id, original_filename, stored_filename, ...)
VALUES (?, ?, ?, ...)

// Atualizar campo avatar na tabela users
UPDATE users SET avatar = ? WHERE id = ?
```

## 🔒 Sistema de Segurança

### **🛡️ Validação de Senha:**

#### **✅ Critérios de Força:**
```javascript
function checkPasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 6) strength += 1;  // Mínimo 6 caracteres
    if (password.length >= 8) strength += 1;  // Recomendado 8+
    if (/[a-z]/.test(password)) strength += 1; // Minúsculas
    if (/[A-Z]/.test(password)) strength += 1; // Maiúsculas
    if (/[0-9]/.test(password)) strength += 1; // Números
    if (/[^A-Za-z0-9]/.test(password)) strength += 1; // Símbolos
    
    // Classificação: Muito fraca (0-1), Fraca (2-3), Média (4), Forte (5), Muito forte (6)
}
```

#### **🔐 Processo de Alteração:**
```php
// 1. Verificar senha atual
$stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
if (!password_verify($currentPassword, $storedHash)) {
    return ['success' => false, 'message' => 'Senha atual incorreta'];
}

// 2. Validar nova senha
if (strlen($newPassword) < 6) {
    return ['success' => false, 'message' => 'Senha muito curta'];
}

// 3. Atualizar senha
$newHash = password_hash($newPassword, PASSWORD_DEFAULT);
$stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
```

### **👁️ Visibilidade de Senha:**
```javascript
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector("i");
    
    if (field.type === "password") {
        field.type = "text";
        button.className = "fas fa-eye-slash";
    } else {
        field.type = "password";
        button.className = "fas fa-eye";
    }
}
```

## 🎨 UX/UI Avançado

### **✨ Funcionalidades JavaScript:**

#### **📸 Preview de Avatar:**
```javascript
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById("avatar-preview").src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}
```

#### **📱 Máscara de Telefone:**
```javascript
function formatPhone(input) {
    let value = input.value.replace(/\D/g, "");
    
    if (value.length <= 10) {
        value = value.replace(/(\d{2})(\d{4})(\d{4})/, "($1) $2-$3");
    } else {
        value = value.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
    }
    
    input.value = value;
}
```

#### **📝 Contador de Caracteres:**
```javascript
// Para biografia (máximo 500 caracteres)
bioField.addEventListener("input", function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    // Atualizar contador visual
    countElement.textContent = `${currentLength}/${maxLength} caracteres`;
    
    // Alterar cor baseado no limite
    if (remaining < 50) {
        countElement.className = "form-text text-warning";
    } else if (remaining < 0) {
        countElement.className = "form-text text-danger";
    }
});
```

### **🎯 Validação em Tempo Real:**
```javascript
// Validação de confirmação de senha
function validatePasswordConfirmation() {
    const newPassword = document.getElementById("new_password").value;
    const confirmPassword = document.getElementById("confirm_password").value;
    
    if (newPassword !== confirmPassword) {
        confirmField.setCustomValidity("As senhas não coincidem");
        confirmField.classList.add("is-invalid");
    } else {
        confirmField.setCustomValidity("");
        confirmField.classList.remove("is-invalid");
        confirmField.classList.add("is-valid");
    }
}
```

## 📊 Estatísticas do Usuário

### **📈 Métricas Exibidas:**
```php
// Buscar estatísticas do usuário
$stmt = $pdo->prepare("
    SELECT u.*, 
           (SELECT COUNT(*) FROM requests WHERE user_id = u.id) as total_requests,
           (SELECT COUNT(*) FROM requests WHERE user_id = u.id AND status = 'approved') as approved_requests
    FROM users u 
    WHERE u.id = ?
");
```

### **📊 Exibição Visual:**
```html
<!-- Card de Estatísticas -->
<div class="row text-center">
    <div class="col-6">
        <h4 class="text-primary">25</h4>
        <small class="text-muted">Requisições</small>
    </div>
    <div class="col-6">
        <h4 class="text-success">18</h4>
        <small class="text-muted">Aprovadas</small>
    </div>
</div>

<!-- Informações Adicionais -->
Membro desde: 15/01/2023
Último acesso: 15/01/2024 14:30
```

## 🔧 Configuração e Instalação

### **📋 Passo a Passo:**

#### **1. Atualizar Banco de Dados:**
```sql
-- Executar script SQL
mysql -u username -p database_name < database/update_profile_table.sql
```

#### **2. Criar Diretórios:**
```bash
# Criar estrutura de upload
mkdir -p assets/uploads/avatars/{original,large,medium,small,thumb}
chmod 755 assets/uploads/avatars/
chmod 755 assets/uploads/avatars/*
```

#### **3. Gerar Avatars Padrão:**
```bash
# Abrir gerador no navegador
open generate_default_avatars.html

# Baixar e salvar em assets/images/
```

#### **4. Configurar Permissões:**
```bash
# Permissões para upload
chown www-data:www-data assets/uploads/avatars/
chmod 755 assets/uploads/avatars/
```

### **⚙️ Configurações PHP:**
```ini
# php.ini - Configurações recomendadas
upload_max_filesize = 5M
post_max_size = 6M
max_execution_time = 30
memory_limit = 128M

# Extensões necessárias
extension=gd
extension=fileinfo
```

## 🚀 Funcionalidades Futuras

### **🔄 Melhorias Planejadas:**
- 🔄 **Auto-save** de preferências
- 🔄 **Crop de imagem** no frontend
- 🔄 **Histórico de avatars**
- 🔄 **Integração com redes sociais**
- 🔄 **Tema escuro/claro** automático
- 🔄 **Notificações push**
- 🔄 **Exportação de dados** (GDPR)
- 🔄 **Two-factor authentication**

### **📱 PWA Integration:**
- 🔄 **Offline profile** editing
- 🔄 **Background sync** para uploads
- 🔄 **Native camera** integration
- 🔄 **Push notifications**

---

**👤 SISTEMA DE PERFIL COMPLETO IMPLEMENTADO!**
*Funcionalidade moderna e profissional para gerenciamento completo do perfil do usuário com upload de avatar, segurança avançada e interface responsiva.*
