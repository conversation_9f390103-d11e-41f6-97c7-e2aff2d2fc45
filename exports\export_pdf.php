<?php
// Exportação para PDF usando HTML e conversão
if (!isset($exportData)) {
    exit('Dados de exportação não encontrados');
}

$request = $exportData['request'];
$items = $exportData['items'];
$statusLabel = $exportData['status_label'];

// Nome do arquivo mais descritivo
$requestName = $request['title'] ?? 'Requisicao_' . $request['id'];
$safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $requestName);
$filename = $safeName . '_' . date('Y-m-d_H-i-s') . '.pdf';

// Verificar se a biblioteca mPDF está disponível
if (!class_exists('Mpdf\Mpdf')) {
    // Fallback: gerar HTML para impressão como PDF
    generateHTMLForPDF($request, $items, $statusLabel, $filename);
    exit;
}

try {
    // Usar mPDF se disponível
    $mpdf = new \Mpdf\Mpdf([
        'mode' => 'utf-8',
        'format' => 'A4',
        'margin_left' => 15,
        'margin_right' => 15,
        'margin_top' => 16,
        'margin_bottom' => 16,
        'margin_header' => 9,
        'margin_footer' => 9
    ]);

    $html = generatePDFContent($request, $items, $statusLabel);
    
    $mpdf->WriteHTML($html);
    $mpdf->Output($filename, 'D');
    exit;
    
} catch (Exception $e) {
    // Fallback para HTML
    generateHTMLForPDF($request, $items, $statusLabel, $filename);
    exit;
}

function generatePDFContent($request, $items, $statusLabel) {
    // Calcular estatísticas
    $totalItems = count($items);
    $totalQuantity = array_sum(array_column($items, 'quantity'));

    // Categorizar itens
    $categories = [];
    foreach ($items as $item) {
        $category = explode(' ', $item['name'])[0];
        if (!isset($categories[$category])) {
            $categories[$category] = ['count' => 0, 'quantity' => 0];
        }
        $categories[$category]['count']++;
        $categories[$category]['quantity'] += $item['quantity'];
    }

    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            @page { margin: 2cm; }
            body {
                font-family: "Segoe UI", Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
                color: #333;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 3px solid #007bff;
                padding-bottom: 20px;
            }
            .company-name {
                font-size: 24px;
                font-weight: bold;
                color: #007bff;
                margin-bottom: 5px;
            }
            .document-title {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }
            .request-name {
                font-size: 16px;
                color: #007bff;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .section-title {
                font-size: 14px;
                font-weight: bold;
                color: #007bff;
                margin: 20px 0 10px 0;
                border-bottom: 2px solid #007bff;
                padding-bottom: 5px;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
            }
            .info-box {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                background-color: #f8f9fa;
            }
            .info-label {
                font-weight: bold;
                color: #495057;
                margin-bottom: 5px;
            }
            .info-value {
                color: #007bff;
                font-weight: bold;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
                margin: 20px 0;
            }
            .stat-box {
                text-align: center;
                padding: 15px;
                border-radius: 5px;
                color: white;
            }
            .stat-primary { background-color: #007bff; }
            .stat-success { background-color: #28a745; }
            .stat-info { background-color: #17a2b8; }
            .stat-warning { background-color: #ffc107; color: #333; }
            .stat-number {
                font-size: 24px;
                font-weight: bold;
                display: block;
            }
            .stat-label {
                font-size: 10px;
                opacity: 0.9;
            }
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .items-table th {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                padding: 12px 8px;
                text-align: left;
                font-weight: bold;
                font-size: 10px;
            }
            .items-table td {
                border: 1px solid #ddd;
                padding: 10px 8px;
                vertical-align: top;
            }
            .items-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .items-table tr:hover {
                background-color: #e3f2fd;
            }
            .item-number {
                background-color: #007bff;
                color: white;
                border-radius: 50%;
                width: 25px;
                height: 25px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 10px;
            }
            .quantity-badge {
                background-color: #28a745;
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-weight: bold;
                font-size: 10px;
            }
            .quantity-high { background-color: #dc3545; }
            .quantity-medium { background-color: #ffc107; color: #333; }
            .quantity-low { background-color: #6c757d; }
            .status-badge {
                padding: 6px 12px;
                border-radius: 15px;
                color: white;
                font-weight: bold;
                font-size: 12px;
                display: inline-block;
            }
            .status-pending { background-color: #ffc107; color: #333; }
            .status-approved { background-color: #28a745; }
            .status-rejected { background-color: #dc3545; }
            .status-delivered { background-color: #17a2b8; }
            .signature-section {
                margin-top: 40px;
                border-top: 2px solid #007bff;
                padding-top: 20px;
            }
            .signature-grid {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 30px;
                margin-top: 20px;
            }
            .signature-box {
                text-align: center;
                border: 1px solid #ddd;
                padding: 20px;
                border-radius: 5px;
                background-color: #f8f9fa;
            }
            .signature-line {
                border-bottom: 2px solid #333;
                margin: 20px 0 10px 0;
                height: 40px;
            }
            .footer {
                margin-top: 40px;
                text-align: center;
                font-size: 9px;
                color: #666;
                border-top: 1px solid #ddd;
                padding-top: 15px;
            }
            .watermark {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 72px;
                color: rgba(0, 123, 255, 0.1);
                font-weight: bold;
                z-index: -1;
                pointer-events: none;
            }
        </style>
    </head>
    <body>
        <div class="watermark">REQUISIÇÃO</div>

        <div class="header">
            <div class="company-name">SISTEMA DE REQUISIÇÃO</div>
            <div class="document-title">MATERIAL DE COZINHA</div>
            <div class="request-name">' . htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']) . '</div>
        </div>

        <div class="section-title">📋 INFORMAÇÕES GERAIS</div>
        <div class="info-grid">
            <div class="info-box">
                <div class="info-label">ID da Requisição</div>
                <div class="info-value">#' . $request['id'] . '</div>
            </div>
            <div class="info-box">
                <div class="info-label">Solicitante</div>
                <div class="info-value">' . htmlspecialchars($request['username']) . '</div>
            </div>
            <div class="info-box">
                <div class="info-label">Data da Requisição</div>
                <div class="info-value">' . date('d/m/Y H:i', strtotime($request['request_date'])) . '</div>
            </div>
            <div class="info-box">
                <div class="info-label">Status Atual</div>
                <div><span class="status-badge status-' . $request['status'] . '">' . strtoupper($statusLabel) . '</span></div>
            </div>
        </div>

        <div class="section-title">📊 ESTATÍSTICAS</div>
        <div class="stats-grid">
            <div class="stat-box stat-primary">
                <span class="stat-number">' . $totalItems . '</span>
                <span class="stat-label">TIPOS DE ITENS</span>
            </div>
            <div class="stat-box stat-success">
                <span class="stat-number">' . $totalQuantity . '</span>
                <span class="stat-label">QUANTIDADE TOTAL</span>
            </div>
            <div class="stat-box stat-info">
                <span class="stat-number">' . count($categories) . '</span>
                <span class="stat-label">CATEGORIAS</span>
            </div>
            <div class="stat-box stat-warning">
                <span class="stat-number">' . number_format($totalQuantity / max($totalItems, 1), 1) . '</span>
                <span class="stat-label">MÉDIA POR ITEM</span>
            </div>
        </div>';

    // Adicionar resumo por categoria se houver múltiplas
    if (count($categories) > 1) {
        $html .= '
        <div class="section-title">🏷️ RESUMO POR CATEGORIA</div>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Categoria</th>
                    <th>Tipos de Itens</th>
                    <th>Quantidade Total</th>
                    <th>Percentual</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($categories as $cat => $data) {
            $percentage = round(($data['quantity'] / $totalQuantity) * 100, 1);
            $html .= '
                <tr>
                    <td><strong>' . htmlspecialchars($cat) . '</strong></td>
                    <td>' . $data['count'] . '</td>
                    <td>' . $data['quantity'] . '</td>
                    <td>' . $percentage . '%</td>
                </tr>';
        }

        $html .= '
            </tbody>
        </table>';
    }

    $html .= '

        <div class="section-title">📦 ITENS SOLICITADOS - DETALHAMENTO COMPLETO</div>
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">Nº</th>
                    <th style="width: 30%;">Nome do Item</th>
                    <th style="width: 40%;">Descrição Completa</th>
                    <th style="width: 10%;">Quantidade</th>
                    <th style="width: 10%;">Unidade</th>
                    <th style="width: 5%;">Status</th>
                </tr>
            </thead>
            <tbody>';

    $itemNumber = 1;
    foreach ($items as $item) {
        // Determinar classe da quantidade
        $quantityClass = 'quantity-low';
        if ($item['quantity'] >= 10) {
            $quantityClass = 'quantity-high';
        } elseif ($item['quantity'] >= 5) {
            $quantityClass = 'quantity-medium';
        }

        $html .= '
                <tr>
                    <td><div class="item-number">' . $itemNumber . '</div></td>
                    <td><strong>' . htmlspecialchars($item['name']) . '</strong></td>
                    <td>' . htmlspecialchars($item['description']) . '</td>
                    <td><span class="quantity-badge ' . $quantityClass . '">' . $item['quantity'] . '</span></td>
                    <td>' . htmlspecialchars($item['unit']) . '</td>
                    <td>⏳</td>
                </tr>';
        $itemNumber++;
    }

    $html .= '
            </tbody>
        </table>

        <div class="signature-section">
            <div class="section-title">✍️ CONTROLE E ASSINATURAS</div>
            <div class="signature-grid">
                <div class="signature-box">
                    <strong>SOLICITADO POR</strong>
                    <div class="signature-line"></div>
                    <div>' . htmlspecialchars($request['username']) . '</div>
                    <div>' . date('d/m/Y H:i', strtotime($request['request_date'])) . '</div>
                </div>
                <div class="signature-box">
                    <strong>APROVADO POR</strong>
                    <div class="signature-line"></div>
                    <div>Nome: _________________</div>
                    <div>Data: ___/___/______</div>
                </div>
                <div class="signature-box">
                    <strong>ENTREGUE POR</strong>
                    <div class="signature-line"></div>
                    <div>Nome: _________________</div>
                    <div>Data: ___/___/______</div>
                </div>
            </div>

            <div style="margin-top: 30px; padding: 15px; border: 2px solid #007bff; border-radius: 5px; background-color: #f8f9fa;">
                <strong>📝 OBSERVAÇÕES GERAIS:</strong>
                <div style="margin-top: 10px; min-height: 60px; border-bottom: 1px solid #ccc;"></div>
                <div style="margin-top: 10px; min-height: 60px; border-bottom: 1px solid #ccc;"></div>
            </div>
        </div>

        <div class="footer">
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; text-align: center;">
                <div>
                    <strong>Sistema de Requisição</strong><br>
                    Material de Cozinha v2.0
                </div>
                <div>
                    <strong>Relatório Gerado</strong><br>
                    ' . date('d/m/Y H:i:s') . '
                </div>
                <div>
                    <strong>Gerado por</strong><br>
                    ' . ($_SESSION['username'] ?? 'Sistema') . '
                </div>
            </div>
            <div style="margin-top: 15px; text-align: center; font-size: 8px; color: #999;">
                Este documento foi gerado automaticamente pelo sistema e possui validade legal para controle interno.
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

function generateHTMLForPDF($request, $items, $statusLabel, $filename) {
    // Headers para exibir HTML que pode ser salvo como PDF pelo navegador
    header('Content-Type: text/html; charset=UTF-8');
    header('Content-Disposition: inline; filename="' . $filename . '"');
    
    echo generatePDFContent($request, $items, $statusLabel);
    
    // JavaScript para abrir diálogo de impressão automaticamente
    echo '
    <script>
        window.onload = function() {
            window.print();
        }
    </script>';
}
?>
