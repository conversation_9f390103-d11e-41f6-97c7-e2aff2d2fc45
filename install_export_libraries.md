# 📄 Instalação de Bibliotecas para Exportação

## 🎯 Visão Geral

Para habilitar a exportação completa para PDF e DOCX, você pode instalar as seguintes bibliotecas PHP opcionais. O sistema funciona sem elas, mas com funcionalidades limitadas.

## 📊 Status das Exportações

### ✅ **Excel (CSV)** - Funciona Imediatamente
- **Formato**: CSV com separador ponto e vírgula
- **Compatibilidade**: Excel, LibreOffice, Google Sheets
- **Dependências**: Nenhuma (PHP nativo)
- **Encoding**: UTF-8 com BOM

### 📄 **PDF** - Funcional com Fallback
- **Sem biblioteca**: HTML para impressão como PDF
- **Com mPDF**: PDF nativo profissional
- **Fallback**: Página HTML otimizada para impressão

### 📝 **DOCX** - Funcional com Fallback  
- **Sem biblioteca**: RTF (Rich Text Format)
- **Com PhpWord**: DOCX nativo do Microsoft Word
- **Fallback**: RTF compatível com Word

## 🔧 Instalação via Composer (Recomendado)

### 1. **Instalar Composer** (se não tiver)
```bash
# Windows
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php composer-setup.php
php -r "unlink('composer-setup.php');"

# Linux/Mac
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 2. **Criar composer.json**
Crie o arquivo `composer.json` na raiz do projeto:
```json
{
    "require": {
        "mpdf/mpdf": "^8.1",
        "phpoffice/phpword": "^1.0"
    }
}
```

### 3. **Instalar Bibliotecas**
```bash
# Na pasta do projeto
composer install
```

### 4. **Incluir Autoloader**
Adicione no início dos arquivos de exportação:
```php
require_once 'vendor/autoload.php';
```

## 📦 Instalação Manual (Alternativa)

### **Para mPDF (PDF)**
1. Baixe de: https://github.com/mpdf/mpdf/releases
2. Extraia para `libs/mpdf/`
3. Inclua: `require_once 'libs/mpdf/vendor/autoload.php';`

### **Para PhpWord (DOCX)**
1. Baixe de: https://github.com/PHPOffice/PHPWord/releases
2. Extraia para `libs/phpword/`
3. Inclua: `require_once 'libs/phpword/src/PhpWord/Autoloader.php';`

## 🚀 Configuração Automática

### **Script de Verificação**
Crie `check_export_libraries.php`:
```php
<?php
echo "<h2>Status das Bibliotecas de Exportação</h2>";

// Verificar mPDF
if (class_exists('Mpdf\Mpdf')) {
    echo "<p>✅ mPDF: Instalado - PDF nativo disponível</p>";
} else {
    echo "<p>⚠️ mPDF: Não instalado - Usando fallback HTML</p>";
}

// Verificar PhpWord
if (class_exists('PhpOffice\PhpWord\PhpWord')) {
    echo "<p>✅ PhpWord: Instalado - DOCX nativo disponível</p>";
} else {
    echo "<p>⚠️ PhpWord: Não instalado - Usando fallback RTF</p>";
}

echo "<p><strong>Nota:</strong> Todas as exportações funcionam mesmo sem as bibliotecas!</p>";
?>
```

## 📋 Funcionalidades por Formato

### **📊 Excel (CSV)**
```
✅ Cabeçalho com informações da requisição
✅ Tabela de itens formatada
✅ Totalizadores automáticos
✅ Encoding UTF-8 com BOM
✅ Separador ponto e vírgula (Excel BR)
✅ Compatível com LibreOffice/Google Sheets
```

### **📄 PDF**
```
✅ Layout profissional A4
✅ Cabeçalho e rodapé
✅ Tabela de itens formatada
✅ Cores e estilos
✅ Informações completas da requisição
✅ Fallback para HTML imprimível
```

### **📝 DOCX/RTF**
```
✅ Formato Microsoft Word
✅ Tabelas formatadas
✅ Estilos de texto (negrito, itálico)
✅ Cabeçalhos e parágrafos
✅ Compatível com Word/LibreOffice
✅ Fallback RTF universal
```

## 🔍 Teste das Exportações

### **1. Testar Excel**
- Acesse uma requisição
- Clique em "📊 Excel"
- Arquivo CSV deve baixar automaticamente
- Abra no Excel/LibreOffice

### **2. Testar PDF**
- Clique em "📄 PDF"
- Com mPDF: Download direto do PDF
- Sem mPDF: Página HTML para impressão

### **3. Testar DOCX**
- Clique em "📝 Word"
- Com PhpWord: Download direto do DOCX
- Sem PhpWord: Download do RTF

## ⚠️ Solução de Problemas

### **Erro de Memória**
```php
// Adicionar no início dos arquivos de exportação
ini_set('memory_limit', '256M');
```

### **Erro de Timeout**
```php
// Adicionar no início dos arquivos de exportação
set_time_limit(60);
```

### **Erro de Permissões**
```bash
# Linux/Mac
chmod 755 exports/
chmod 644 exports/*.php
```

### **Erro de Encoding**
- Verificar se arquivos estão em UTF-8
- Verificar configuração do servidor web
- Testar com diferentes navegadores

## 📈 Benefícios das Bibliotecas

### **Com Bibliotecas Instaladas**
- ✅ PDFs nativos de alta qualidade
- ✅ DOCX totalmente compatível
- ✅ Controle total sobre formatação
- ✅ Melhor performance
- ✅ Recursos avançados

### **Sem Bibliotecas (Fallback)**
- ✅ Funciona imediatamente
- ✅ Não requer instalação
- ✅ Compatível com qualquer servidor
- ✅ RTF/HTML universalmente suportado
- ⚠️ Formatação mais simples

## 🎯 Recomendação

**Para uso profissional**: Instale as bibliotecas via Composer
**Para teste/desenvolvimento**: Use os fallbacks (já funcionam!)

---

**💡 Dica**: O sistema foi projetado para funcionar perfeitamente mesmo sem as bibliotecas externas!
