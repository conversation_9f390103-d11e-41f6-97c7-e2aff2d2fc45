# 👑 Guia de Permissões de Administrador

## 🎯 Visão Geral

Sistema **completamente configurado** para que todos os administradores tenham **acesso total** a todas as requisições de todos os usuários, com funcionalidades completas de visualização, edição, aprovação e exportação.

## ✅ Permissões de Administrador Implementadas

### **📋 1. Visualização de Requisições**

#### **`manage_requests.php` - Painel Administrativo**
```php
// Linha 37-44: Busca TODAS as requisições de TODOS os usuários
$stmt = $pdo->prepare("
    SELECT r.*, u.username 
    FROM requests r 
    JOIN users u ON r.user_id = u.id 
    ORDER BY r.request_date DESC 
    LIMIT $itemsPerPage OFFSET $offset
");
```

**✅ Funcionalidades:**
- **Ver todas as requisições** do sistema
- **Filtrar por status** (pendente, aprovada, rejeitada, entregue)
- **Pesquisar** por nome ou solicitante
- **Paginação** para grandes volumes
- **Ações em massa** (aprovar, rejeitar, entregar)

### **👁️ 2. Visualização Detalhada**

#### **`view_request.php` - Detalhes Completos**
```php
// Linha 27: Permite admin ver qualquer requisição
if ($_SESSION['role'] != 'admin' && $request['user_id'] != $_SESSION['user_id']) {
    header('Location: my_requests.php');
    exit;
}
```

**✅ Funcionalidades:**
- **Ver detalhes** de qualquer requisição
- **Informações completas** (solicitante, data, status, itens)
- **Ações administrativas** diretas (aprovar/rejeitar/entregar)
- **Exportação** em todos os formatos
- **Navegação inteligente** (volta para manage_requests.php)

### **✏️ 3. Edição de Requisições**

#### **`edit_request.php` - Edição Completa**
```php
// Linha 35: Admin pode editar qualquer requisição
if ($_SESSION['role'] != 'admin' && $request['user_id'] != $_SESSION['user_id']) {
    header('Location: my_requests.php');
    exit;
}
```

**✅ Funcionalidades:**
- **Editar qualquer requisição** pendente
- **Alterar nome** da requisição
- **Modificar itens** e quantidades
- **Pesquisa avançada** de produtos
- **Navegação inteligente** (volta para manage_requests.php)

### **📄 4. Exportação Universal**

#### **`export_request.php` - Exportação Completa**
```php
// Linha 34: Admin pode exportar qualquer requisição
if ($_SESSION['role'] != 'admin' && $request['user_id'] != $_SESSION['user_id']) {
    header('Location: my_requests.php');
    exit;
}
```

**✅ Funcionalidades:**
- **Exportar qualquer requisição** em Excel, PDF, DOCX
- **Relatórios profissionais** com dados completos
- **Informações do solicitante** incluídas
- **Dados estatísticos** automáticos

## 🏠 Dashboard Administrativo

### **`index.php` - Painel Principal**

#### **Estatísticas Pessoais:**
```php
// Linhas 14-24: Estatísticas do próprio admin
SELECT 
    COUNT(*) as total_requests,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
    SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_requests
FROM requests 
WHERE user_id = ?
```

#### **Estatísticas do Sistema:**
```php
// Linhas 29-36: Estatísticas globais (apenas para admin)
SELECT 
    (SELECT COUNT(*) FROM requests) as total_system_requests,
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(*) FROM items) as total_items,
    (SELECT COUNT(*) FROM requests WHERE status = 'pending') as pending_system_requests
```

**✅ Cards Informativos:**
- **📊 Total de Requisições** do sistema
- **👥 Usuários Cadastrados**
- **📦 Itens Cadastrados**
- **⚠️ Requisições Pendentes** (aguardando aprovação)

## 🧭 Navegação Inteligente

### **Botões "Voltar" Contextuais:**

#### **Para Administradores:**
```php
// edit_request.php e view_request.php
<a href="<?php echo $_SESSION['role'] == 'admin' ? 'manage_requests.php' : 'my_requests.php'; ?>" 
   class="btn btn-secondary">⬅ Voltar</a>
```

#### **Para Usuários Normais:**
- **Volta para** `my_requests.php` (suas próprias requisições)

#### **Para Administradores:**
- **Volta para** `manage_requests.php` (painel administrativo)

## 🔐 Controle de Acesso

### **Verificações de Segurança:**

#### **1. Verificação de Role:**
```php
// Em todas as páginas administrativas
if ($_SESSION['role'] != 'admin') {
    header('Location: index.php');
    exit;
}
```

#### **2. Verificação de Propriedade OU Admin:**
```php
// Para visualização/edição de requisições específicas
if ($_SESSION['role'] != 'admin' && $request['user_id'] != $_SESSION['user_id']) {
    header('Location: my_requests.php');
    exit;
}
```

#### **3. Verificação de Status:**
```php
// Para edição (apenas requisições pendentes)
if ($request['status'] != 'pending') {
    $error = 'Apenas requisições pendentes podem ser editadas';
}
```

## 📋 Menu de Navegação

### **Navbar Administrativa:**
```php
<?php if (isset($_SESSION['role']) && $_SESSION['role'] == 'admin'): ?>
<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle" href="#" id="adminDropdown">
        ⚙️ Administração
    </a>
    <div class="dropdown-menu">
        <a class="dropdown-item" href="setup_products.php">🛠️ Configurar Produtos</a>
        <a class="dropdown-item" href="manage_items.php">📦 Gerenciar Itens</a>
        <a class="dropdown-item" href="manage_requests.php">📋 Gerenciar Requisições</a>
        <a class="dropdown-item" href="manage_users.php">👥 Gerenciar Usuários</a>
        <a class="dropdown-item" href="system_check.php">🔍 Verificar Sistema</a>
    </div>
</li>
<?php endif; ?>
```

## 🎯 Funcionalidades Exclusivas de Admin

### **1. Ações de Status:**
- **✅ Aprovar** requisições pendentes
- **❌ Rejeitar** requisições pendentes
- **🚚 Marcar como entregue** requisições aprovadas

### **2. Gestão de Usuários:**
- **👥 Criar** novos usuários
- **✏️ Editar** informações de usuários
- **👁️ Visualizar** perfis e estatísticas
- **🔒 Gerenciar** permissões

### **3. Gestão de Itens:**
- **📦 Adicionar** novos produtos
- **✏️ Editar** itens existentes
- **🗑️ Remover** itens não utilizados
- **📊 Ver** estatísticas de uso

### **4. Relatórios e Exportações:**
- **📊 Exportar** qualquer requisição
- **📈 Relatórios** estatísticos
- **📋 Listas** de usuários e itens
- **🔍 Auditoria** completa do sistema

## 📊 Estatísticas Disponíveis

### **Dashboard Pessoal:**
- **Total de requisições** criadas pelo admin
- **Requisições pendentes** do admin
- **Requisições aprovadas** do admin
- **Requisições entregues** do admin

### **Dashboard do Sistema:**
- **Total de requisições** de todos os usuários
- **Total de usuários** cadastrados
- **Total de itens** disponíveis
- **Requisições pendentes** aguardando aprovação

## 🔄 Fluxo de Trabalho Administrativo

### **1. Monitoramento:**
1. **Acessar** `manage_requests.php`
2. **Visualizar** todas as requisições
3. **Filtrar** por status "Pendente"
4. **Identificar** requisições para aprovação

### **2. Análise:**
1. **Clicar** em "Ver Detalhes"
2. **Analisar** itens solicitados
3. **Verificar** quantidades
4. **Avaliar** necessidade

### **3. Ação:**
1. **Aprovar** se adequado
2. **Rejeitar** se inadequado
3. **Editar** se necessário ajuste
4. **Marcar como entregue** após entrega

### **4. Acompanhamento:**
1. **Exportar** relatórios
2. **Acompanhar** estatísticas
3. **Monitorar** usuários
4. **Manter** sistema atualizado

## ✅ Resumo de Permissões

### **Administradores PODEM:**
- ✅ **Ver TODAS** as requisições de TODOS os usuários
- ✅ **Editar QUALQUER** requisição pendente
- ✅ **Aprovar/Rejeitar** qualquer requisição
- ✅ **Marcar como entregue** requisições aprovadas
- ✅ **Exportar QUALQUER** requisição
- ✅ **Gerenciar** usuários e itens
- ✅ **Acessar** estatísticas do sistema
- ✅ **Criar** requisições próprias

### **Usuários Normais PODEM:**
- ✅ **Ver apenas** suas próprias requisições
- ✅ **Editar apenas** suas requisições pendentes
- ✅ **Exportar apenas** suas próprias requisições
- ✅ **Criar** novas requisições
- ✅ **Ver** estatísticas pessoais

---

**🎉 SISTEMA COMPLETAMENTE CONFIGURADO!**
*Todos os administradores têm acesso total a todas as requisições com funcionalidades completas.*
