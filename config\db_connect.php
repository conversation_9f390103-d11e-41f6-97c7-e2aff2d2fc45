<?php
// Configuração de conexão com banco de dados
$host = 'localhost';
$dbname = 'kitchen_inventory';
$username = 'root';
$password = '123mudar';

// Verificar se a variável $pdo já foi definida para evitar reconexões
if (!isset($pdo)) {
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

        // Definir variável global para indicar que a conexão está disponível
        $GLOBALS['db_connected'] = true;

    } catch (PDOException $e) {
        // Log do erro em vez de parar a execução
        error_log("Erro de conexão com banco: " . $e->getMessage());

        // Definir variável para indicar que não há conexão
        $GLOBALS['db_connected'] = false;
        $pdo = null;

        // Em ambiente de desenvolvimento, mostrar aviso sem parar execução
        if (!headers_sent()) {
            $db_error_message = "Aviso: Banco de dados não disponível. Algumas funcionalidades podem estar limitadas.";
        }
    }
}
?>