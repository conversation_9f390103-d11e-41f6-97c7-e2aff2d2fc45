<?php
// Arquivo de debug para identificar problemas

echo "<h1>🔍 Debug do Sistema</h1>";

// 1. Verificar PHP
echo "<h2>📋 Informações PHP</h2>";
echo "<p><strong>Versão PHP:</strong> " . phpversion() . "</p>";
echo "<p><strong>Servidor:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";

// 2. Verificar arquivos
echo "<h2>📁 Verificação de Arquivos</h2>";

$files_to_check = [
    'config/db_connect.php',
    'includes/seo_meta.php',
    'includes/navbar.php',
    '.htaccess'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p>✅ <strong>$file</strong> - Existe</p>";
    } else {
        echo "<p>❌ <strong>$file</strong> - Não encontrado</p>";
    }
}

// 3. Verificar conexão com banco
echo "<h2>🗄️ Teste de Conexão com Banco</h2>";
try {
    require_once 'config/db_connect.php';
    echo "<p>✅ <strong>Conexão com banco:</strong> OK</p>";
} catch (Exception $e) {
    echo "<p>❌ <strong>Erro no banco:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 4. Verificar SEO
echo "<h2>🚀 Teste SEO</h2>";
try {
    ob_start();
    include 'includes/seo_meta.php';
    $seo_output = ob_get_clean();
    echo "<p>✅ <strong>SEO Meta:</strong> Carregado com sucesso</p>";
    echo "<details><summary>Ver output SEO</summary><pre>" . htmlspecialchars($seo_output) . "</pre></details>";
} catch (Exception $e) {
    echo "<p>❌ <strong>Erro no SEO:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// 5. Verificar .htaccess
echo "<h2>⚙️ Verificação .htaccess</h2>";
if (file_exists('.htaccess')) {
    $htaccess_content = file_get_contents('.htaccess');
    echo "<p>✅ <strong>.htaccess:</strong> Existe (" . strlen($htaccess_content) . " bytes)</p>";
    echo "<details><summary>Ver conteúdo .htaccess</summary><pre>" . htmlspecialchars($htaccess_content) . "</pre></details>";
} else {
    echo "<p>❌ <strong>.htaccess:</strong> Não encontrado</p>";
}

// 6. Verificar permissões
echo "<h2>🔐 Verificação de Permissões</h2>";
$dirs_to_check = ['.', 'config', 'includes', 'exports'];
foreach ($dirs_to_check as $dir) {
    if (is_readable($dir)) {
        echo "<p>✅ <strong>$dir:</strong> Legível</p>";
    } else {
        echo "<p>❌ <strong>$dir:</strong> Não legível</p>";
    }
}

// 7. Verificar variáveis de servidor
echo "<h2>🌐 Variáveis de Servidor</h2>";
$server_vars = ['HTTP_HOST', 'REQUEST_URI', 'PHP_SELF', 'DOCUMENT_ROOT'];
foreach ($server_vars as $var) {
    echo "<p><strong>$var:</strong> " . ($_SERVER[$var] ?? 'Não definido') . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>🏠 Voltar ao Sistema</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #007bff; }
h2 { color: #28a745; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
details { margin: 10px 0; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
