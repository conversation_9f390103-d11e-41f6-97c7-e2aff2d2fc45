# 📊 Sistema de Códigos de Barras e Códigos Internos

## 🎯 Visão Geral

Sistema **completo** de códigos de barras e códigos internos implementado para requisições e produtos, com geração automática, visualização profissional e controle de estoque avançado.

## ✨ Principais Funcionalidades Implementadas

### **📋 1. Códigos para Requisições**

#### **Campos Adicionados à Tabela `requests`:**
```sql
ALTER TABLE requests ADD COLUMN internal_code VARCHAR(20) UNIQUE AFTER id;
ALTER TABLE requests ADD COLUMN barcode VARCHAR(50) UNIQUE AFTER internal_code;
ALTER TABLE requests ADD COLUMN priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium';
ALTER TABLE requests ADD COLUMN department VARCHAR(100);
ALTER TABLE requests ADD COLUMN notes TEXT;
ALTER TABLE requests ADD COLUMN approved_by INT NULL;
ALTER TABLE requests ADD COLUMN approved_date DATETIME NULL;
ALTER TABLE requests ADD COLUMN delivered_by INT NULL;
ALTER TABLE requests ADD COLUMN delivered_date DATETIME NULL;
```

#### **Geração Automática:**
- **Código Interno:** `REQ000001`, `REQ000002`, etc.
- **Código de Barras:** EAN-13 com prefixo `789` + ID + dígito verificador
- **Exemplo:** `7890000012345` para requisição ID 123

### **📦 2. Códigos para Produtos/Itens**

#### **Campos Adicionados à Tabela `items`:**
```sql
ALTER TABLE items ADD COLUMN internal_code VARCHAR(20) UNIQUE AFTER id;
ALTER TABLE items ADD COLUMN barcode VARCHAR(50) UNIQUE AFTER internal_code;
ALTER TABLE items ADD COLUMN category VARCHAR(100);
ALTER TABLE items ADD COLUMN supplier VARCHAR(200);
ALTER TABLE items ADD COLUMN min_stock INT DEFAULT 0;
ALTER TABLE items ADD COLUMN current_stock INT DEFAULT 0;
ALTER TABLE items ADD COLUMN cost_price DECIMAL(10,2) DEFAULT 0.00;
```

#### **Geração Automática:**
- **Código Interno:** `ITEM00001`, `ITEM00002`, etc.
- **Código de Barras:** EAN-13 com prefixo `456` + ID + dígito verificador
- **Exemplo:** `4560000012345` para item ID 123

## 🔧 Biblioteca de Códigos de Barras

### **Arquivo: `includes/barcode_generator.php`**

#### **Classe BarcodeGenerator:**
```php
// Gerar código interno para requisições
BarcodeGenerator::generateRequestCode($id);  // REQ000001

// Gerar código interno para itens
BarcodeGenerator::generateItemCode($id);     // ITEM00001

// Gerar código de barras EAN-13
BarcodeGenerator::generateBarcode('REQ', $id);  // 7890000012345

// Gerar código de barras SVG
BarcodeGenerator::generateBarcodeSVG($code, $width, $height);

// Validar código EAN-13
BarcodeGenerator::validateEAN13($code);      // true/false

// Gerar QR Code (URL)
BarcodeGenerator::generateQRCodeURL($data, $size);
```

#### **Funções Auxiliares Globais:**
```php
generateRequestInternalCode($pdo);  // Próximo código disponível
generateItemInternalCode($pdo);     // Próximo código disponível
generateRequestBarcode($id);        // Código de barras para requisição
generateItemBarcode($id);           // Código de barras para item
displayBarcode($code, 'svg');       // Exibir código visual
generateQRCode($data, $size);       // Gerar QR Code
```

## 📝 Formulários Aprimorados

### **🆕 Formulário de Requisição (`request_form.php`)**

#### **Novos Campos:**
```html
<!-- Nome da Requisição -->
<input type="text" name="title" placeholder="Ex: Festa Junina 2024">

<!-- Prioridade -->
<select name="priority">
    <option value="low">🟢 Baixa</option>
    <option value="medium">🟡 Média</option>
    <option value="high">🟠 Alta</option>
    <option value="urgent">🔴 Urgente</option>
</select>

<!-- Departamento -->
<input type="text" name="department" placeholder="Ex: Cozinha Principal">

<!-- Observações -->
<textarea name="notes" placeholder="Observações adicionais..."></textarea>
```

#### **Geração Automática:**
- **Código interno** e **código de barras** gerados automaticamente
- **Fallback** para versões antigas do banco de dados
- **Mensagem informativa** sobre códigos gerados

### **📦 Formulário de Produtos (`manage_items.php`)**

#### **Seções Organizadas:**

##### **1. Informações Básicas:**
```html
<input type="text" name="name" placeholder="Nome do produto">
<input type="text" name="category" placeholder="Categoria">
<select name="unit">
    <option value="kg">kg (quilograma)</option>
    <option value="l">l (litro)</option>
    <option value="unid">unid (unidade)</option>
    <!-- ... mais opções -->
</select>
```

##### **2. Códigos e Identificação:**
```html
<!-- Código Interno (readonly) -->
<input type="text" value="Será gerado automaticamente" readonly>

<!-- Código de Barras (opcional) -->
<input type="text" name="barcode" placeholder="Deixe vazio para gerar">
```

##### **3. Controle de Estoque:**
```html
<input type="number" name="current_stock" placeholder="Estoque atual">
<input type="number" name="min_stock" placeholder="Estoque mínimo">
<input type="number" name="cost_price" placeholder="Preço de custo">
<input type="text" name="supplier" placeholder="Fornecedor">
```

## 📊 Visualização Aprimorada

### **🗂️ Tabela de Produtos Melhorada**

#### **Colunas Exibidas:**
- **Código:** ID + Código interno
- **Nome:** Nome + descrição resumida
- **Categoria:** Badge colorido
- **Estoque:** Atual + mínimo com cores de alerta
- **Unidade:** Badge informativo
- **Fornecedor:** Nome do fornecedor
- **Código de Barras:** Código + botão para visualizar

#### **Alertas de Estoque:**
```php
// Cores baseadas no estoque
if ($currentStock <= $minStock) {
    $class = 'text-danger';  // Vermelho: estoque crítico
} elseif ($currentStock <= $minStock * 1.5) {
    $class = 'text-warning'; // Amarelo: estoque baixo
} else {
    $class = 'text-success'; // Verde: estoque ok
}
```

### **📋 Modal de Código de Barras**

#### **Funcionalidades:**
- **Visualização** do código numérico
- **QR Code** gerado automaticamente
- **Botão de impressão** com layout otimizado
- **Design responsivo** e profissional

## 🖨️ Página de Visualização de Códigos

### **Arquivo: `view_barcode.php`**

#### **Funcionalidades:**
- **Visualização completa** do código de barras
- **QR Code** para leitura móvel
- **Informações detalhadas** (solicitante, data, etc.)
- **Impressão otimizada** com CSS específico
- **Download** da imagem do código
- **Compartilhamento** via Web Share API

#### **Design Profissional:**
```css
.barcode-container {
    background: white;
    border: 2px solid #007bff;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.barcode-display {
    font-family: 'Courier New', monospace;
    font-size: 24px;
    letter-spacing: 4px;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
}
```

## 🔄 Script de Atualização

### **Arquivo: `update_database_codes.php`**

#### **Operações Realizadas:**
1. **Adicionar colunas** nas tabelas `requests` e `items`
2. **Gerar códigos** para registros existentes
3. **Criar índices** para performance
4. **Verificar** se colunas já existem (seguro para re-execução)

#### **Campos Adicionados:**

##### **Tabela `requests`:**
- `internal_code` - Código interno único
- `barcode` - Código de barras EAN-13
- `priority` - Prioridade da requisição
- `department` - Departamento solicitante
- `notes` - Observações
- `approved_by` - ID do usuário que aprovou
- `approved_date` - Data de aprovação
- `delivered_by` - ID do usuário que entregou
- `delivered_date` - Data de entrega

##### **Tabela `items`:**
- `internal_code` - Código interno único
- `barcode` - Código de barras EAN-13
- `category` - Categoria do produto
- `supplier` - Fornecedor principal
- `min_stock` - Estoque mínimo
- `current_stock` - Estoque atual
- `cost_price` - Preço de custo

## 📈 Benefícios do Sistema

### **🔍 Rastreabilidade:**
- **Códigos únicos** para cada requisição e produto
- **Histórico completo** de aprovações e entregas
- **Auditoria** facilitada com códigos de barras

### **📊 Controle de Estoque:**
- **Alertas automáticos** para estoque baixo
- **Controle de fornecedores** integrado
- **Preços de custo** para análise financeira

### **⚡ Eficiência:**
- **Leitura rápida** com códigos de barras
- **QR Codes** para dispositivos móveis
- **Impressão otimizada** para etiquetas

### **📱 Modernização:**
- **Interface responsiva** e moderna
- **Compatibilidade** com leitores de código
- **Integração** com sistemas externos

## 🔧 Configurações Técnicas

### **Padrões de Códigos:**

#### **Códigos Internos:**
- **Requisições:** `REQ` + 6 dígitos (REQ000001)
- **Itens:** `ITEM` + 5 dígitos (ITEM00001)

#### **Códigos de Barras EAN-13:**
- **Requisições:** Prefixo `789` + ID (8 dígitos) + verificador
- **Itens:** Prefixo `456` + ID (8 dígitos) + verificador

#### **Validação:**
- **Dígito verificador** EAN-13 calculado automaticamente
- **Unicidade** garantida por constraints no banco
- **Fallback** para versões antigas do sistema

### **Performance:**
- **Índices** criados para códigos internos
- **Consultas otimizadas** para busca por código
- **Cache** de códigos gerados

## 📋 Como Usar

### **1. Atualizar Banco de Dados:**
```
Acesse: update_database_codes.php
Execute como administrador
```

### **2. Criar Requisição:**
- Preencha os **novos campos** (prioridade, departamento, observações)
- **Códigos** são gerados automaticamente
- **Visualize** o código em `view_barcode.php?type=request&id=123`

### **3. Gerenciar Produtos:**
- **Adicione** categoria, fornecedor, estoque
- **Códigos** são gerados automaticamente
- **Visualize** alertas de estoque na listagem

### **4. Imprimir Códigos:**
- **Clique** no ícone de código de barras
- **Imprima** diretamente ou baixe a imagem
- **Compartilhe** via QR Code

---

**🎉 SISTEMA DE CÓDIGOS COMPLETO IMPLEMENTADO!**
*Controle total com códigos de barras, QR codes e gestão avançada de estoque.*
