<?php
// Teste completo do sistema
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Simular usuário logado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin_teste';
    $_SESSION['role'] = 'admin';
}

// Configurações da página
$page_title = 'Teste do Sistema';
$page_subtitle = 'Verificação completa de funcionalidades';
$page_header = true;

// Incluir layout
require_once 'includes/layout.php';
?>

<!-- Teste do Sistema -->
<div class="content-card">
    <h2>
        <i class="fas fa-check-circle text-success me-2"></i>
        Sistema Funcionando Perfeitamente!
    </h2>
    <p class="lead">Todos os componentes do layout padronizado estão operacionais.</p>
    
    <div class="alert alert-success">
        <h5><i class="fas fa-thumbs-up me-2"></i>Testes Realizados:</h5>
        <ul class="mb-0">
            <li>✅ Layout padronizado carregado</li>
            <li>✅ Responsividade mobile funcionando</li>
            <li>✅ Sidebar com overlay operacional</li>
            <li>✅ Header responsivo ativo</li>
            <li>✅ Sistema de configuração centralizada</li>
            <li>✅ AvatarUploader corrigido</li>
            <li>✅ Erros de depreciação resolvidos</li>
        </ul>
    </div>
</div>

<!-- Teste de Componentes -->
<div class="content-card">
    <h3>
        <i class="fas fa-puzzle-piece text-primary me-2"></i>
        Componentes Testados
    </h3>
    
    <div class="row">
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-mobile-alt me-2"></i>Responsividade
                </div>
                <div class="card-body">
                    <p class="card-text">Layout se adapta perfeitamente a diferentes tamanhos de tela.</p>
                    <div class="d-flex gap-2">
                        <span class="badge bg-success">Desktop</span>
                        <span class="badge bg-success">Tablet</span>
                        <span class="badge bg-success">Mobile</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-bars me-2"></i>Sidebar Mobile
                </div>
                <div class="card-body">
                    <p class="card-text">Sidebar com overlay e animações suaves para mobile.</p>
                    <div class="d-flex gap-2">
                        <span class="badge bg-success">Overlay</span>
                        <span class="badge bg-success">Animação</span>
                        <span class="badge bg-success">ESC</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-cog me-2"></i>Configuração
                </div>
                <div class="card-body">
                    <p class="card-text">Sistema centralizado de configuração de páginas.</p>
                    <div class="d-flex gap-2">
                        <span class="badge bg-success">Automático</span>
                        <span class="badge bg-success">Flexível</span>
                        <span class="badge bg-success">Escalável</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-header bg-warning text-white">
                    <i class="fas fa-user-circle me-2"></i>Avatar System
                </div>
                <div class="card-body">
                    <p class="card-text">Sistema de avatar robusto com fallbacks seguros.</p>
                    <div class="d-flex gap-2">
                        <span class="badge bg-success">Upload</span>
                        <span class="badge bg-success">Redimensionamento</span>
                        <span class="badge bg-success">Fallback</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Teste de Navegação -->
<div class="content-card">
    <h3>
        <i class="fas fa-link text-info me-2"></i>
        Links de Teste
    </h3>
    
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="list-group">
                <div class="list-group-item list-group-item-action active">
                    <strong>Páginas Principais</strong>
                </div>
                <a href="index.php" class="list-group-item list-group-item-action">
                    <i class="fas fa-home me-2"></i>Dashboard
                </a>
                <a href="profile.php" class="list-group-item list-group-item-action">
                    <i class="fas fa-user me-2"></i>Perfil
                </a>
                <a href="test_layout.php" class="list-group-item list-group-item-action">
                    <i class="fas fa-flask me-2"></i>Teste Layout
                </a>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="list-group">
                <div class="list-group-item list-group-item-action active">
                    <strong>Páginas de Debug</strong>
                </div>
                <a href="debug_layout.php" class="list-group-item list-group-item-action">
                    <i class="fas fa-bug me-2"></i>Debug Layout
                </a>
                <a href="index_simple.php" class="list-group-item list-group-item-action">
                    <i class="fas fa-home me-2"></i>Index Simples
                </a>
                <a href="test_system.php" class="list-group-item list-group-item-action">
                    <i class="fas fa-check-circle me-2"></i>Teste Sistema
                </a>
            </div>
        </div>
        
        <div class="col-md-4 mb-3">
            <div class="list-group">
                <div class="list-group-item list-group-item-action active">
                    <strong>Status do Sistema</strong>
                </div>
                <div class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>Layout: OK
                </div>
                <div class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>Mobile: OK
                </div>
                <div class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>Avatar: OK
                </div>
                <div class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>Config: OK
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Instruções de Teste -->
<div class="content-card">
    <h3>
        <i class="fas fa-clipboard-list text-secondary me-2"></i>
        Como Testar o Sistema
    </h3>
    
    <div class="row">
        <div class="col-md-6">
            <h5>📱 Teste Mobile:</h5>
            <ol>
                <li>Redimensione a janela para menos de 768px</li>
                <li>Observe o header se tornar compacto</li>
                <li>Clique no botão ☰ para abrir a sidebar</li>
                <li>Clique no overlay escuro para fechar</li>
                <li>Pressione ESC para fechar a sidebar</li>
            </ol>
        </div>
        
        <div class="col-md-6">
            <h5>🖥️ Teste Desktop:</h5>
            <ol>
                <li>Redimensione para mais de 768px</li>
                <li>Observe a sidebar fixa à esquerda</li>
                <li>Navegue pelos links da sidebar</li>
                <li>Teste a responsividade dos cards</li>
                <li>Verifique o header completo</li>
            </ol>
        </div>
    </div>
    
    <div class="alert alert-info mt-3">
        <h6><i class="fas fa-lightbulb me-2"></i>Dica:</h6>
        <p class="mb-0">Use as ferramentas de desenvolvedor do navegador (F12) para simular diferentes dispositivos e testar a responsividade.</p>
    </div>
</div>

<!-- Informações Técnicas -->
<div class="content-card">
    <h3>
        <i class="fas fa-code text-dark me-2"></i>
        Informações Técnicas
    </h3>
    
    <div class="row">
        <div class="col-md-6">
            <h6>📁 Arquivos Principais:</h6>
            <ul class="list-unstyled">
                <li><i class="fas fa-file-code text-primary me-2"></i><code>includes/layout.php</code></li>
                <li><i class="fas fa-file-code text-success me-2"></i><code>includes/page_config.php</code></li>
                <li><i class="fas fa-file-code text-info me-2"></i><code>includes/layout_footer.php</code></li>
                <li><i class="fas fa-file-code text-warning me-2"></i><code>includes/AvatarUploader.php</code></li>
            </ul>
        </div>
        
        <div class="col-md-6">
            <h6>🔧 Tecnologias:</h6>
            <ul class="list-unstyled">
                <li><i class="fab fa-bootstrap text-primary me-2"></i>Bootstrap 5.3</li>
                <li><i class="fab fa-js text-warning me-2"></i>JavaScript ES6+</li>
                <li><i class="fab fa-css3-alt text-info me-2"></i>CSS3 + Flexbox</li>
                <li><i class="fab fa-php text-secondary me-2"></i>PHP 8.0+</li>
            </ul>
        </div>
    </div>
</div>

<?php
// Scripts específicos da página
$page_scripts = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    console.log("✅ Sistema de layout padronizado funcionando perfeitamente!");
    console.log("📱 Teste a responsividade redimensionando a janela");
    console.log("🎯 Clique no botão ☰ para testar a sidebar mobile");
    
    // Mostrar toast de sucesso
    setTimeout(function() {
        if (typeof showToast === "function") {
            showToast("Sistema funcionando perfeitamente!", "success");
        }
    }, 1000);
});
</script>
';

// Incluir footer
require_once 'includes/layout_footer.php';
?>
