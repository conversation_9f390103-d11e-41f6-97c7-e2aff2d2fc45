<?php
session_start();
require_once 'config/db_connect.php';

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'Preencha todos os campos';
    } else {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            header('Location: index.php');
            exit;
        } else {
            $error = 'Credenciais inválidas';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <?php include 'includes/seo_meta.php'; ?>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .login-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .btn-login {
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        .system-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <!-- Informações do Sistema -->
                <div class="system-info text-center">
                    <h1 class="h4 text-primary mb-3">
                        <i class="fas fa-utensils" aria-hidden="true"></i>
                        Sistema de Requisição
                    </h1>
                    <p class="text-muted mb-0">Material de Cozinha</p>
                    <small class="text-muted">Controle profissional de estoque e requisições</small>
                </div>

                <!-- Card de Login -->
                <main class="login-card card" role="main">
                    <header class="login-header">
                        <h2 class="mb-3">
                            <i class="fas fa-sign-in-alt" aria-hidden="true"></i>
                            Acesso ao Sistema
                        </h2>
                        <p class="mb-0">Faça login para acessar suas requisições</p>
                    </header>

                    <div class="login-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert" aria-live="polite">
                                <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                                <?php echo htmlspecialchars($error); ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Fechar alerta">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        <?php endif; ?>

                        <form method="post" novalidate aria-label="Formulário de login">
                            <div class="form-group">
                                <label for="username" class="font-weight-bold">
                                    <i class="fas fa-user text-primary" aria-hidden="true"></i>
                                    Nome de Usuário
                                </label>
                                <input type="text"
                                       id="username"
                                       name="username"
                                       class="form-control"
                                       placeholder="Digite seu nome de usuário"
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                       required
                                       autocomplete="username"
                                       aria-describedby="username-help">
                                <small id="username-help" class="form-text text-muted">
                                    Use suas credenciais fornecidas pelo administrador
                                </small>
                            </div>

                            <div class="form-group">
                                <label for="password" class="font-weight-bold">
                                    <i class="fas fa-lock text-primary" aria-hidden="true"></i>
                                    Senha
                                </label>
                                <input type="password"
                                       id="password"
                                       name="password"
                                       class="form-control"
                                       placeholder="Digite sua senha"
                                       required
                                       autocomplete="current-password"
                                       aria-describedby="password-help">
                                <small id="password-help" class="form-text text-muted">
                                    Sua senha é confidencial e criptografada
                                </small>
                            </div>

                            <button type="submit" class="btn btn-primary btn-login btn-block">
                                <i class="fas fa-sign-in-alt" aria-hidden="true"></i>
                                Entrar no Sistema
                            </button>
                        </form>

                        <!-- Informações de Ajuda -->
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                Problemas para acessar? Entre em contato com o administrador
                            </small>
                        </div>
                    </div>
                </main>

                <!-- Informações de Segurança -->
                <div class="text-center mt-3">
                    <small class="text-white-50">
                        <i class="fas fa-shield-alt" aria-hidden="true"></i>
                        Conexão segura e dados protegidos
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Focar no campo de usuário ao carregar a página
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            if (usernameField && !usernameField.value) {
                usernameField.focus();
            }
        });

        // Validação básica do formulário
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                e.preventDefault();
                alert('Por favor, preencha todos os campos obrigatórios.');
                return false;
            }

            if (username.length < 3) {
                e.preventDefault();
                alert('O nome de usuário deve ter pelo menos 3 caracteres.');
                return false;
            }
        });
    </script>

    <!-- Schema.org para página de login -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Login - Sistema de Requisição",
        "description": "Página de acesso ao sistema de requisição de material de cozinha",
        "url": "<?php echo 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>",
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Sistema de Requisição de Material de Cozinha",
            "applicationCategory": "BusinessApplication"
        }
    }
    </script>
</body>
</html>