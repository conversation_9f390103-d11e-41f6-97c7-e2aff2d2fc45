<?php
/**
 * API REST para Aplicativo Android
 * Sistema de Requisições de Cozinha
 */

// Headers CORS para permitir acesso do app
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// Responder OPTIONS para preflight CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Incluir dependências
require_once '../includes/DatabaseManager.php';
require_once '../includes/BaseController.php';
require_once '../includes/RequestController.php';

/**
 * Classe principal da API
 */
class ApiController extends BaseController {
    private $method;
    private $endpoint;
    private $params;
    
    public function __construct() {
        parent::__construct();
        
        // Analisar requisição
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->parseRequest();
        
        // Processar requisição
        $this->processRequest();
    }
    
    /**
     * Analisa a requisição
     */
    private function parseRequest() {
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = str_replace('/api/', '', $path);
        $parts = explode('/', trim($path, '/'));
        
        $this->endpoint = $parts[0] ?? '';
        $this->params = array_slice($parts, 1);
    }
    
    /**
     * Processa a requisição
     */
    private function processRequest() {
        try {
            switch ($this->endpoint) {
                case 'auth':
                    $this->handleAuth();
                    break;
                case 'requests':
                    $this->handleRequests();
                    break;
                case 'items':
                    $this->handleItems();
                    break;
                case 'users':
                    $this->handleUsers();
                    break;
                case 'stats':
                    $this->handleStats();
                    break;
                default:
                    $this->jsonError('Endpoint não encontrado', 404);
            }
        } catch (Exception $e) {
            $this->jsonError($e->getMessage(), 500);
        }
    }
    
    /**
     * Autenticação
     */
    private function handleAuth() {
        switch ($this->method) {
            case 'POST':
                $this->login();
                break;
            case 'DELETE':
                $this->logout();
                break;
            case 'GET':
                $this->checkAuth();
                break;
            default:
                $this->jsonError('Método não permitido', 405);
        }
    }
    
    /**
     * Login
     */
    private function login() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (empty($input['username']) || empty($input['password'])) {
            $this->jsonError('Username e senha são obrigatórios');
        }
        
        $user = $this->db->fetchOne(
            "SELECT * FROM users WHERE username = ?",
            [$input['username']]
        );
        
        if (!$user || !$this->verifyPassword($input['password'], $user['password'])) {
            $this->jsonError('Credenciais inválidas', 401);
        }
        
        // Gerar token JWT simples (em produção usar biblioteca JWT)
        $token = base64_encode(json_encode([
            'user_id' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role'],
            'exp' => time() + (24 * 60 * 60) // 24 horas
        ]));
        
        $this->jsonSuccess([
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'role' => $user['role']
            ]
        ], 'Login realizado com sucesso');
    }
    
    /**
     * Verificar autenticação
     */
    private function checkAuth() {
        $token = $this->getAuthToken();
        if (!$token) {
            $this->jsonError('Token não fornecido', 401);
        }
        
        $userData = $this->validateToken($token);
        if (!$userData) {
            $this->jsonError('Token inválido', 401);
        }
        
        $this->jsonSuccess(['user' => $userData], 'Autenticado');
    }
    
    /**
     * Logout
     */
    private function logout() {
        // Em uma implementação real, invalidaríamos o token
        $this->jsonSuccess([], 'Logout realizado com sucesso');
    }
    
    /**
     * Gerenciar requisições
     */
    private function handleRequests() {
        $this->requireApiAuth();
        
        $requestController = new RequestController();
        
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    // Buscar requisição específica
                    $details = $requestController->getRequestDetails($this->params[0]);
                    $this->jsonSuccess($details);
                } else {
                    // Listar requisições
                    $page = $_GET['page'] ?? 1;
                    $perPage = $_GET['per_page'] ?? 10;
                    
                    if ($this->isAdmin()) {
                        $requests = $requestController->getAllRequests($page, $perPage);
                    } else {
                        $requests = $requestController->getUserRequests($this->user['id'], $page, $perPage);
                    }
                    
                    $this->jsonSuccess($requests);
                }
                break;
                
            case 'POST':
                // Criar nova requisição
                $input = json_decode(file_get_contents('php://input'), true);
                $result = $requestController->create($input);
                $this->jsonSuccess($result, 'Requisição criada com sucesso');
                break;
                
            case 'PUT':
                // Atualizar status (apenas admin)
                if (!isset($this->params[0])) {
                    $this->jsonError('ID da requisição é obrigatório');
                }
                
                $input = json_decode(file_get_contents('php://input'), true);
                $requestController->updateStatus(
                    $this->params[0],
                    $input['status'],
                    $input['notes'] ?? '',
                    $input['reason'] ?? ''
                );
                $this->jsonSuccess([], 'Status atualizado com sucesso');
                break;
                
            case 'DELETE':
                // Excluir requisição
                if (!isset($this->params[0])) {
                    $this->jsonError('ID da requisição é obrigatório');
                }
                
                $requestController->delete($this->params[0]);
                $this->jsonSuccess([], 'Requisição excluída com sucesso');
                break;
                
            default:
                $this->jsonError('Método não permitido', 405);
        }
    }
    
    /**
     * Gerenciar itens
     */
    private function handleItems() {
        $this->requireApiAuth();
        
        switch ($this->method) {
            case 'GET':
                $page = $_GET['page'] ?? 1;
                $perPage = $_GET['per_page'] ?? 20;
                $search = $_GET['search'] ?? '';
                
                $where = '';
                $params = [];
                
                if ($search) {
                    $where = 'name LIKE ? OR description LIKE ?';
                    $params = ["%$search%", "%$search%"];
                }
                
                $items = $this->paginate('items', $page, $perPage, $where, $params, 'name ASC');
                $this->jsonSuccess($items);
                break;
                
            default:
                $this->jsonError('Método não permitido', 405);
        }
    }
    
    /**
     * Gerenciar usuários (apenas admin)
     */
    private function handleUsers() {
        $this->requireApiAuth();
        $this->requireAdmin();
        
        switch ($this->method) {
            case 'GET':
                $users = $this->db->fetchAll("SELECT id, username, role FROM users ORDER BY username");
                $this->jsonSuccess(['data' => $users]);
                break;
                
            default:
                $this->jsonError('Método não permitido', 405);
        }
    }
    
    /**
     * Estatísticas
     */
    private function handleStats() {
        $this->requireApiAuth();
        
        $requestController = new RequestController();
        $userId = $this->isAdmin() ? null : $this->user['id'];
        $stats = $requestController->getStats($userId);
        
        $this->jsonSuccess($stats);
    }
    
    /**
     * Obter token de autenticação
     */
    private function getAuthToken() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (strpos($authHeader, 'Bearer ') === 0) {
            return substr($authHeader, 7);
        }
        
        return null;
    }
    
    /**
     * Validar token
     */
    private function validateToken($token) {
        try {
            $data = json_decode(base64_decode($token), true);
            
            if (!$data || $data['exp'] < time()) {
                return false;
            }
            
            // Verificar se usuário ainda existe
            $user = $this->db->fetchOne("SELECT * FROM users WHERE id = ?", [$data['user_id']]);
            if (!$user) {
                return false;
            }
            
            return [
                'id' => $user['id'],
                'username' => $user['username'],
                'role' => $user['role']
            ];
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Requer autenticação via API
     */
    private function requireApiAuth() {
        $token = $this->getAuthToken();
        if (!$token) {
            $this->jsonError('Token de autenticação necessário', 401);
        }
        
        $userData = $this->validateToken($token);
        if (!$userData) {
            $this->jsonError('Token inválido ou expirado', 401);
        }
        
        // Simular usuário logado
        $this->user = $userData;
        $_SESSION['user_id'] = $userData['id'];
        $_SESSION['role'] = $userData['role'];
    }
}

// Inicializar API
try {
    new ApiController();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro interno do servidor']);
}
?>
