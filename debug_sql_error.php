<?php
// Debug para identificar origem do erro SQL
session_start();

// Configurar para capturar todos os erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configurar handler de erro personalizado
set_error_handler(function($severity, $message, $file, $line) {
    if (strpos($message, 'OFFSET') !== false || strpos($message, 'SQLSTATE') !== false) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px 0;'>";
        echo "<h3>🚨 ERRO SQL DETECTADO:</h3>";
        echo "<p><strong>Mensagem:</strong> " . htmlspecialchars($message) . "</p>";
        echo "<p><strong>Arquivo:</strong> " . htmlspecialchars($file) . "</p>";
        echo "<p><strong>Linha:</strong> " . $line . "</p>";
        echo "<p><strong>Stack Trace:</strong></p>";
        echo "<pre>" . htmlspecialchars(print_r(debug_backtrace(), true)) . "</pre>";
        echo "</div>";
    }
    return false; // Continuar com o handler padrão
});

echo "<h1>🔍 Debug de Erro SQL</h1>";
echo "<p>Monitorando erros SQL relacionados a OFFSET...</p>";

// Incluir arquivos um por um para identificar o problema
$files_to_test = [
    'config/db_connect.php',
    'includes/page_config.php',
    'includes/layout.php'
];

foreach ($files_to_test as $file) {
    echo "<h3>📁 Testando: $file</h3>";
    if (file_exists($file)) {
        try {
            include_once $file;
            echo "<p>✅ $file incluído sem erros</p>";
        } catch (Exception $e) {
            echo "<p>❌ Erro em $file: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p>⚠️ $file não encontrado</p>";
    }
}

// Testar conexão com banco
echo "<h3>🔗 Testando Conexão com Banco</h3>";
try {
    require_once 'config/db_connect.php';
    if (isset($pdo) && $pdo !== null) {
        echo "<p>✅ Conexão com banco estabelecida</p>";
        
        // Testar consulta simples
        $stmt = $pdo->query("SELECT COUNT(*) FROM items");
        $count = $stmt->fetchColumn();
        echo "<p>✅ Consulta simples funcionando: $count itens na tabela</p>";
        
    } else {
        echo "<p>❌ Variável \$pdo não está disponível</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Erro na conexão: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Simular parâmetros da nova requisição
echo "<h3>🧪 Simulando Parâmetros da Nova Requisição</h3>";

$_GET['page'] = '0'; // Simular página 0 que causa o erro
$search = '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$itemsPerPage = 20;
$offset = max(0, ($page - 1) * $itemsPerPage);

echo "<p><strong>Parâmetros simulados:</strong></p>";
echo "<ul>";
echo "<li>GET['page']: " . ($_GET['page'] ?? 'não definido') . "</li>";
echo "<li>page (processado): $page</li>";
echo "<li>itemsPerPage: $itemsPerPage</li>";
echo "<li>offset: $offset</li>";
echo "</ul>";

// Testar consulta com os parâmetros
if (isset($pdo) && $pdo !== null) {
    echo "<h3>🔍 Testando Consulta com Paginação</h3>";
    
    try {
        $whereClause = '';
        $params = [];
        
        if (!empty($search)) {
            $whereClause = "WHERE name LIKE ? OR description LIKE ?";
            $params = ["%$search%", "%$search%"];
        }
        
        // Testar consulta de contagem
        $countSql = "SELECT COUNT(*) FROM items $whereClause";
        echo "<p><strong>SQL de contagem:</strong> <code>$countSql</code></p>";
        
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $totalItems = $countStmt->fetchColumn();
        echo "<p>✅ Contagem executada: $totalItems itens</p>";
        
        // Testar consulta com paginação
        if ($offset > 0) {
            $sql = "SELECT * FROM items $whereClause ORDER BY name LIMIT ?, ?";
            $paginationParams = array_merge($params, [$offset, $itemsPerPage]);
        } else {
            $sql = "SELECT * FROM items $whereClause ORDER BY name LIMIT ?";
            $paginationParams = array_merge($params, [$itemsPerPage]);
        }
        
        echo "<p><strong>SQL de paginação:</strong> <code>$sql</code></p>";
        echo "<p><strong>Parâmetros:</strong> " . json_encode($paginationParams) . "</p>";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($paginationParams);
        $items = $stmt->fetchAll();
        
        echo "<p>✅ Consulta de paginação executada: " . count($items) . " itens retornados</p>";
        
    } catch (PDOException $e) {
        echo "<p>❌ Erro na consulta: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>Código do erro:</strong> " . $e->getCode() . "</p>";
    }
}

// Verificar se há requisições AJAX ativas
echo "<h3>🌐 Verificando Requisições AJAX</h3>";
echo "<p>Verificando se há JavaScript fazendo requisições em background...</p>";

// Listar arquivos JavaScript que podem fazer requisições
$js_files = glob('*.js');
$js_files = array_merge($js_files, glob('assets/js/*.js'));
$js_files = array_merge($js_files, glob('js/*.js'));

if (!empty($js_files)) {
    echo "<p><strong>Arquivos JavaScript encontrados:</strong></p>";
    echo "<ul>";
    foreach ($js_files as $js_file) {
        echo "<li>$js_file</li>";
    }
    echo "</ul>";
} else {
    echo "<p>Nenhum arquivo JavaScript encontrado</p>";
}

echo "<hr>";
echo "<p><strong>Debug concluído em:</strong> " . date('d/m/Y H:i:s') . "</p>";
echo "<p><a href='nova-requisicao'>🔙 Voltar para Nova Requisição</a></p>";
?>
