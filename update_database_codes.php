<?php
// Script para atualizar banco de dados com códigos de barras e códigos internos
session_start();
require_once 'config/db_connect.php';

// Verificar se é admin
if (!isset($_SESSION['role']) || $_SESSION['role'] != 'admin') {
    die('Acesso negado. Apenas administradores podem executar este script.');
}

$success = [];
$errors = [];

try {
    // 1. Adicionar campos para tabela requests (requisições)
    echo "<h2>🔄 Atualizando tabela 'requests'...</h2>";
    
    // Verificar se colunas já existem
    $stmt = $pdo->query("SHOW COLUMNS FROM requests LIKE 'internal_code'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE requests ADD COLUMN internal_code VARCHAR(20) UNIQUE AFTER id");
        $success[] = "✅ Campo 'internal_code' adicionado à tabela requests";
    } else {
        $success[] = "ℹ️ Campo 'internal_code' já existe na tabela requests";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM requests LIKE 'barcode'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE requests ADD COLUMN barcode VARCHAR(50) UNIQUE AFTER internal_code");
        $success[] = "✅ Campo 'barcode' adicionado à tabela requests";
    } else {
        $success[] = "ℹ️ Campo 'barcode' já existe na tabela requests";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM requests LIKE 'priority'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE requests ADD COLUMN priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' AFTER status");
        $success[] = "✅ Campo 'priority' adicionado à tabela requests";
    } else {
        $success[] = "ℹ️ Campo 'priority' já existe na tabela requests";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM requests LIKE 'department'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE requests ADD COLUMN department VARCHAR(100) AFTER priority");
        $success[] = "✅ Campo 'department' adicionado à tabela requests";
    } else {
        $success[] = "ℹ️ Campo 'department' já existe na tabela requests";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM requests LIKE 'notes'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE requests ADD COLUMN notes TEXT AFTER department");
        $success[] = "✅ Campo 'notes' adicionado à tabela requests";
    } else {
        $success[] = "ℹ️ Campo 'notes' já existe na tabela requests";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM requests LIKE 'approved_by'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE requests ADD COLUMN approved_by INT NULL AFTER notes");
        $pdo->exec("ALTER TABLE requests ADD COLUMN approved_date DATETIME NULL AFTER approved_by");
        $pdo->exec("ALTER TABLE requests ADD COLUMN delivered_by INT NULL AFTER approved_date");
        $pdo->exec("ALTER TABLE requests ADD COLUMN delivered_date DATETIME NULL AFTER delivered_by");
        $success[] = "✅ Campos de aprovação e entrega adicionados à tabela requests";
    } else {
        $success[] = "ℹ️ Campos de aprovação já existem na tabela requests";
    }

    // 2. Adicionar campos para tabela items (produtos)
    echo "<h2>🔄 Atualizando tabela 'items'...</h2>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM items LIKE 'internal_code'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE items ADD COLUMN internal_code VARCHAR(20) UNIQUE AFTER id");
        $success[] = "✅ Campo 'internal_code' adicionado à tabela items";
    } else {
        $success[] = "ℹ️ Campo 'internal_code' já existe na tabela items";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM items LIKE 'barcode'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE items ADD COLUMN barcode VARCHAR(50) UNIQUE AFTER internal_code");
        $success[] = "✅ Campo 'barcode' adicionado à tabela items";
    } else {
        $success[] = "ℹ️ Campo 'barcode' já existe na tabela items";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM items LIKE 'category'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE items ADD COLUMN category VARCHAR(100) AFTER barcode");
        $success[] = "✅ Campo 'category' adicionado à tabela items";
    } else {
        $success[] = "ℹ️ Campo 'category' já existe na tabela items";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM items LIKE 'supplier'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE items ADD COLUMN supplier VARCHAR(200) AFTER category");
        $success[] = "✅ Campo 'supplier' adicionado à tabela items";
    } else {
        $success[] = "ℹ️ Campo 'supplier' já existe na tabela items";
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM items LIKE 'min_stock'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE items ADD COLUMN min_stock INT DEFAULT 0 AFTER supplier");
        $pdo->exec("ALTER TABLE items ADD COLUMN current_stock INT DEFAULT 0 AFTER min_stock");
        $pdo->exec("ALTER TABLE items ADD COLUMN cost_price DECIMAL(10,2) DEFAULT 0.00 AFTER current_stock");
        $success[] = "✅ Campos de estoque e preço adicionados à tabela items";
    } else {
        $success[] = "ℹ️ Campos de estoque já existem na tabela items";
    }

    // 3. Gerar códigos internos para requisições existentes
    echo "<h2>🔄 Gerando códigos para requisições existentes...</h2>";
    
    $stmt = $pdo->query("SELECT id FROM requests WHERE internal_code IS NULL OR internal_code = ''");
    $requests = $stmt->fetchAll();
    
    foreach ($requests as $request) {
        $internalCode = 'REQ' . str_pad($request['id'], 6, '0', STR_PAD_LEFT);
        $barcode = generateBarcode('REQ', $request['id']);
        
        $updateStmt = $pdo->prepare("UPDATE requests SET internal_code = ?, barcode = ? WHERE id = ?");
        $updateStmt->execute([$internalCode, $barcode, $request['id']]);
    }
    
    $success[] = "✅ Códigos gerados para " . count($requests) . " requisições";

    // 4. Gerar códigos internos para itens existentes
    echo "<h2>🔄 Gerando códigos para itens existentes...</h2>";
    
    $stmt = $pdo->query("SELECT id FROM items WHERE internal_code IS NULL OR internal_code = ''");
    $items = $stmt->fetchAll();
    
    foreach ($items as $item) {
        $internalCode = 'ITEM' . str_pad($item['id'], 5, '0', STR_PAD_LEFT);
        $barcode = generateBarcode('ITEM', $item['id']);
        
        $updateStmt = $pdo->prepare("UPDATE items SET internal_code = ?, barcode = ? WHERE id = ?");
        $updateStmt->execute([$internalCode, $barcode, $item['id']]);
    }
    
    $success[] = "✅ Códigos gerados para " . count($items) . " itens";

    // 5. Criar índices para performance
    echo "<h2>🔄 Criando índices...</h2>";
    
    try {
        $pdo->exec("CREATE INDEX idx_requests_internal_code ON requests(internal_code)");
        $success[] = "✅ Índice criado para requests.internal_code";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') === false) {
            $errors[] = "❌ Erro ao criar índice requests.internal_code: " . $e->getMessage();
        } else {
            $success[] = "ℹ️ Índice requests.internal_code já existe";
        }
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_items_internal_code ON items(internal_code)");
        $success[] = "✅ Índice criado para items.internal_code";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') === false) {
            $errors[] = "❌ Erro ao criar índice items.internal_code: " . $e->getMessage();
        } else {
            $success[] = "ℹ️ Índice items.internal_code já existe";
        }
    }

} catch (PDOException $e) {
    $errors[] = "❌ Erro geral: " . $e->getMessage();
}

// Função para gerar código de barras
function generateBarcode($prefix, $id) {
    // Gerar código de barras no formato EAN-13 simplificado
    $code = $prefix . str_pad($id, 8, '0', STR_PAD_LEFT);
    
    // Adicionar dígito verificador simples
    $sum = 0;
    for ($i = 0; $i < strlen($code); $i++) {
        if (is_numeric($code[$i])) {
            $sum += (int)$code[$i] * (($i % 2 == 0) ? 1 : 3);
        }
    }
    $checkDigit = (10 - ($sum % 10)) % 10;
    
    return $code . $checkDigit;
}

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Atualização do Banco de Dados - Códigos</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-database"></i> Atualização do Banco de Dados - Códigos de Barras e Internos</h3>
            </div>
            <div class="card-body">
                
                <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> Operações Realizadas com Sucesso:</h5>
                    <ul class="mb-0">
                        <?php foreach ($success as $msg): ?>
                            <li><?php echo $msg; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> Erros Encontrados:</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> Campos Adicionados:</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📋 Tabela Requests:</h6>
                            <ul>
                                <li><strong>internal_code</strong> - Código interno (REQ000001)</li>
                                <li><strong>barcode</strong> - Código de barras</li>
                                <li><strong>priority</strong> - Prioridade (baixa, média, alta, urgente)</li>
                                <li><strong>department</strong> - Departamento solicitante</li>
                                <li><strong>notes</strong> - Observações</li>
                                <li><strong>approved_by</strong> - Aprovado por (user_id)</li>
                                <li><strong>approved_date</strong> - Data de aprovação</li>
                                <li><strong>delivered_by</strong> - Entregue por (user_id)</li>
                                <li><strong>delivered_date</strong> - Data de entrega</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>📦 Tabela Items:</h6>
                            <ul>
                                <li><strong>internal_code</strong> - Código interno (ITEM00001)</li>
                                <li><strong>barcode</strong> - Código de barras</li>
                                <li><strong>category</strong> - Categoria do produto</li>
                                <li><strong>supplier</strong> - Fornecedor</li>
                                <li><strong>min_stock</strong> - Estoque mínimo</li>
                                <li><strong>current_stock</strong> - Estoque atual</li>
                                <li><strong>cost_price</strong> - Preço de custo</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home"></i> Voltar ao Sistema
                    </a>
                    <a href="manage_items.php" class="btn btn-success">
                        <i class="fas fa-boxes"></i> Gerenciar Itens
                    </a>
                    <a href="manage_requests.php" class="btn btn-info">
                        <i class="fas fa-clipboard-list"></i> Gerenciar Requisições
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
