# Sistema de Requisição - Configurações Básicas

# Charset padrão
AddDefaultCharset UTF-8

# Página inicial
DirectoryIndex index.php

# Desabilitar listagem de diretórios
Options -Indexes

# Headers de Segurança (básicos)
<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always append X-Frame-Options SAMEORIGIN

    # Prevenir MIME type sniffing
    Header set X-Content-Type-Options nosniff
</IfModule>

# Configurações de Cache
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Cache para imagens
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Cache para CSS e JS
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Cache para fontes
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # Não cachear arquivos PHP
    ExpiresByType text/html "access plus 0 seconds"
    ExpiresByType application/x-httpd-php "access plus 0 seconds"
</IfModule>

# Compressão GZIP
<IfModule mod_deflate.c>
    # Comprimir HTML, CSS, JavaScript, Text, XML e fontes
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# URLs Amigáveis para SEO
# Redirecionar para HTTPS (se disponível)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remover extensão .php das URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirecionar URLs com .php para versão sem extensão
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# URLs específicas amigáveis
RewriteRule ^home/?$ index.php [NC,L]
RewriteRule ^login/?$ login.php [NC,L]
RewriteRule ^logout/?$ logout.php [NC,L]
RewriteRule ^nova-requisicao/?$ request_form.php [NC,L]
RewriteRule ^minhas-requisicoes/?$ my_requests.php [NC,L]
RewriteRule ^requisicao/([0-9]+)/?$ view_request.php?id=$1 [NC,L]
RewriteRule ^editar-requisicao/([0-9]+)/?$ edit_request.php?id=$1 [NC,L]
RewriteRule ^admin/?$ manage_requests.php [NC,L]
RewriteRule ^admin/requisicoes/?$ manage_requests.php [NC,L]
RewriteRule ^admin/itens/?$ manage_items.php [NC,L]
RewriteRule ^admin/usuarios/?$ manage_users.php [NC,L]
RewriteRule ^usuario/([0-9]+)/?$ view_user.php?id=$1 [NC,L]

# Página de erro 404 personalizada
ErrorDocument 404 /404.php

# Configurações de MIME Types
<IfModule mod_mime.c>
    # Fontes
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType application/vnd.ms-fontobject .eot
    AddType application/x-font-ttf .ttf
    AddType font/opentype .otf
    
    # SVG
    AddType image/svg+xml .svg .svgz
    AddEncoding gzip .svgz
    
    # Outros
    AddType application/javascript .js
    AddType text/css .css
</IfModule>

# Configurações de Charset
AddDefaultCharset UTF-8

# Configurações de Índice
DirectoryIndex index.php index.html

# Desabilitar listagem de diretórios
Options -Indexes

# Configurações de Performance
<IfModule mod_env.c>
    SetEnv TZ America/Sao_Paulo
</IfModule>

# Configurações específicas para arquivos PHP
<FilesMatch "\.php$">
    # Configurações de cache para PHP
    <IfModule mod_headers.c>
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </IfModule>
</FilesMatch>

# Configurações para arquivos estáticos
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "public, max-age=31536000"
        Header unset ETag
    </IfModule>
    FileETag None
</FilesMatch>

# Configurações de Log (se necessário)
# LogLevel warn
# ErrorLog logs/error.log
# CustomLog logs/access.log combined
