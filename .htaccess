# Sistema de Roteamento com URLs Amigáveis
# Configurações para URLs limpa e amigáveis

# Habilitar reescrita de URL
RewriteEngine On

# URLs Amigáveis - Páginas Principais
RewriteRule ^dashboard/?$ app.php?page=dashboard [L,QSA]
RewriteRule ^nova-requisicao/?$ app.php?page=nova-requisicao [L,QSA]
RewriteRule ^minhas-requisicoes/?$ app.php?page=minhas-requisicoes [L,QSA]
RewriteRule ^perfil/?$ app.php?page=perfil [L,QSA]

# URLs Amigáveis - Páginas Administrativas
RewriteRule ^gerenciar-requisicoes/?$ app.php?page=gerenciar-requisicoes [L,QSA]
RewriteRule ^gerenciar-itens/?$ app.php?page=gerenciar-itens [L,QSA]
RewriteRule ^gerenciar-usuarios/?$ app.php?page=gerenciar-usuarios [L,QSA]
RewriteRule ^configurar-produtos/?$ app.php?page=configurar-produtos [L,QSA]

# URLs Amigáveis - Páginas com Parâmetros
RewriteRule ^requisicao/([0-9]+)/?$ app.php?page=ver-requisicao&id=$1 [L,QSA]
RewriteRule ^requisicao/([0-9]+)/editar/?$ app.php?page=editar-requisicao&id=$1 [L,QSA]
RewriteRule ^usuario/([0-9]+)/?$ app.php?page=ver-usuario&id=$1 [L,QSA]

# URLs Amigáveis - Exportação
RewriteRule ^requisicao/([0-9]+)/exportar/([a-z]+)/?$ app.php?page=exportar-requisicao&id=$1&format=$2 [L,QSA]

# URLs Amigáveis - Códigos de Barras
RewriteRule ^codigo-barras/([a-z]+)/([0-9]+)/?$ app.php?page=codigo-barras&type=$1&id=$2 [L,QSA]

# Redirecionar raiz para dashboard
RewriteRule ^/?$ dashboard [R=301,L]

# Redirecionar index.php para dashboard
RewriteRule ^index\.php$ dashboard [R=301,L]

# Proteger arquivos sensíveis
<Files ".env">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "*.bak">
    Require all denied
</Files>

<Files "*.backup">
    Require all denied
</Files>
