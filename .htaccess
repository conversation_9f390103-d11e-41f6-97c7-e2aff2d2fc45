# Sistema de Roteamento com Querystring
# Configurações para o Sistema de Requisição de Material de Cozinha

# Habilitar reescrita de URL
RewriteEngine On

# Definir página padrão
DirectoryIndex app.php

# Redirecionar páginas antigas para o novo sistema de roteamento
# Páginas principais
RewriteRule ^index\.php$ app.php?page=dashboard [R=301,L]
RewriteRule ^request_form\.php$ app.php?page=nova-requisicao [R=301,L]
RewriteRule ^my_requests\.php$ app.php?page=minhas-requisicoes [R=301,L]
RewriteRule ^profile\.php$ app.php?page=perfil [R=301,L]

# Páginas administrativas
RewriteRule ^manage_requests\.php$ app.php?page=gerenciar-requisicoes [R=301,L]
RewriteRule ^manage_items\.php$ app.php?page=gerenciar-itens [R=301,L]
RewriteRule ^manage_users\.php$ app.php?page=gerenciar-usuarios [R=301,L]

# Páginas com parâmetros
RewriteRule ^view_request\.php\?id=([0-9]+)$ app.php?page=ver-requisicao&id=$1 [R=301,L]
RewriteRule ^edit_request\.php\?id=([0-9]+)$ app.php?page=editar-requisicao&id=$1 [R=301,L]
RewriteRule ^view_user\.php\?id=([0-9]+)$ app.php?page=ver-usuario&id=$1 [R=301,L]

# Páginas de configuração
RewriteRule ^setup_products\.php$ app.php?page=configurar-produtos [R=301,L]
RewriteRule ^add_kitchen_products\.php$ app.php?page=adicionar-produtos-cozinha [R=301,L]

# Páginas de teste e debug
RewriteRule ^test_layout\.php$ app.php?page=teste-layout [R=301,L]
RewriteRule ^debug_layout\.php$ app.php?page=debug-sistema [R=301,L]
RewriteRule ^test_system\.php$ app.php?page=verificar-sistema [R=301,L]

# URLs amigáveis (opcional - para futuro)
# RewriteRule ^dashboard/?$ app.php?page=dashboard [L]
# RewriteRule ^nova-requisicao/?$ app.php?page=nova-requisicao [L]
# RewriteRule ^minhas-requisicoes/?$ app.php?page=minhas-requisicoes [L]
# RewriteRule ^perfil/?$ app.php?page=perfil [L]
# RewriteRule ^requisicao/([0-9]+)/?$ app.php?page=ver-requisicao&id=$1 [L]

# Configurações de segurança
# Proteger arquivos de configuração
<Files "config/*">
    Order Allow,Deny
    Deny from all
</Files>

# Proteger arquivos de backup
<Files "*.bak">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.backup">
    Order Allow,Deny
    Deny from all
</Files>

# Proteger arquivos de log
<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# Proteger arquivos sensíveis
<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.md">
    Order Allow,Deny
    Deny from all
</Files>

# Headers de segurança
<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevenir MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Habilitar proteção XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # Política de referrer
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Configurações de cache
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Cache para imagens
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # Cache para CSS e JS
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType text/javascript "access plus 1 week"
    
    # Cache para fontes
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
</IfModule>

# Compressão GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE text/javascript
</IfModule>

# Configurações de erro personalizadas
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Configurações PHP (se permitido)
<IfModule mod_php.c>
    # Ocultar versão do PHP
    php_flag expose_php off
    
    # Configurações de sessão
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
    
    # Configurações de upload
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # Configurações de memória
    php_value memory_limit 256M
    
    # Configurações de tempo
    php_value max_execution_time 60
</IfModule>
