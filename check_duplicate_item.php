<?php
/**
 * Endpoint AJAX para verificar nomes duplicados de itens
 * Retorna JSON com informações sobre duplicatas
 */

// Definir cabeçalho JSON
header('Content-Type: application/json; charset=utf-8');

session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    http_response_code(403);
    echo json_encode(['error' => 'Acesso negado']);
    exit;
}

require_once 'config/db_connect.php';

// Verificar se é uma requisição AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    http_response_code(400);
    echo json_encode(['error' => 'Requisição inválida']);
    exit;
}

// Verificar se é POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método não permitido']);
    exit;
}

// Obter parâmetros
$name = trim($_POST['name'] ?? '');
$excludeId = (int)($_POST['exclude_id'] ?? 0);

// Validar entrada
if (empty($name)) {
    echo json_encode([
        'exists' => false,
        'message' => 'Nome vazio'
    ]);
    exit;
}

// Verificar se o nome é muito curto
if (strlen($name) < 2) {
    echo json_encode([
        'exists' => false,
        'message' => 'Nome muito curto para verificação'
    ]);
    exit;
}

try {
    // Preparar query baseada se está excluindo um ID (para edição)
    if ($excludeId > 0) {
        $stmt = $pdo->prepare("
            SELECT id, name, internal_code, category
            FROM items
            WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))
            AND id != ?
            LIMIT 1
        ");
        $stmt->execute([$name, $excludeId]);
    } else {
        $stmt = $pdo->prepare("
            SELECT id, name, internal_code, category
            FROM items
            WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))
            LIMIT 1
        ");
        $stmt->execute([$name]);
    }

    $existingItem = $stmt->fetch();

    if ($existingItem) {
        // Item duplicado encontrado
        $suggestions = generateNameSuggestion($name, $pdo, $excludeId);

        echo json_encode([
            'exists' => true,
            'message' => 'Nome já existe',
            'item' => [
                'id' => (int)$existingItem['id'],
                'name' => $existingItem['name'],
                'internal_code' => $existingItem['internal_code'] ?? 'ITEM' . str_pad($existingItem['id'], 5, '0', STR_PAD_LEFT),
                'category' => $existingItem['category'] ?? 'Não definida'
            ],
            'suggestion' => $suggestions
        ], JSON_UNESCAPED_UNICODE);
    } else {
        // Nome disponível
        echo json_encode([
            'exists' => false,
            'message' => 'Nome disponível',
            'suggestion' => null
        ], JSON_UNESCAPED_UNICODE);
    }

} catch (PDOException $e) {
    error_log("Erro no check_duplicate_item.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Erro no banco de dados',
        'message' => 'Erro interno do servidor'
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    error_log("Erro geral no check_duplicate_item.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Erro interno',
        'message' => 'Erro interno do servidor'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * Gerar sugestão de nome alternativo
 */
function generateNameSuggestion($originalName, $pdo, $excludeId = 0) {
    $suggestions = [];

    try {
        // Sugestões baseadas em sufixos
        $suffixes = [
            ' - Novo',
            ' - Alternativo',
            ' - Premium',
            ' - Especial',
            ' - Extra',
            ' V2',
            ' Plus',
            ' Pro'
        ];

        foreach ($suffixes as $suffix) {
            $suggestion = $originalName . $suffix;

            // Verificar se a sugestão está disponível
            try {
                if ($excludeId > 0) {
                    $stmt = $pdo->prepare("SELECT id FROM items WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND id != ?");
                    $stmt->execute([$suggestion, $excludeId]);
                } else {
                    $stmt = $pdo->prepare("SELECT id FROM items WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))");
                    $stmt->execute([$suggestion]);
                }

                if (!$stmt->fetch()) {
                    $suggestions[] = $suggestion;
                    if (count($suggestions) >= 3) break; // Limitar a 3 sugestões
                }
            } catch (PDOException $e) {
                error_log("Erro ao verificar sugestão '$suggestion': " . $e->getMessage());
                continue;
            }
        }

        // Se não encontrou sugestões com sufixos, tentar com números
        if (empty($suggestions)) {
            for ($i = 2; $i <= 10; $i++) {
                $suggestion = $originalName . ' ' . $i;

                try {
                    if ($excludeId > 0) {
                        $stmt = $pdo->prepare("SELECT id FROM items WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND id != ?");
                        $stmt->execute([$suggestion, $excludeId]);
                    } else {
                        $stmt = $pdo->prepare("SELECT id FROM items WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))");
                        $stmt->execute([$suggestion]);
                    }

                    if (!$stmt->fetch()) {
                        $suggestions[] = $suggestion;
                        if (count($suggestions) >= 3) break;
                    }
                } catch (PDOException $e) {
                    error_log("Erro ao verificar sugestão numérica '$suggestion': " . $e->getMessage());
                    continue;
                }
            }
        }
    } catch (Exception $e) {
        error_log("Erro geral ao gerar sugestões: " . $e->getMessage());
    }

    return $suggestions;
}

/**
 * Função para normalizar nomes (remover acentos, espaços extras, etc.)
 */
function normalizeName($name) {
    // Converter para minúsculas
    $name = strtolower($name);
    
    // Remover acentos
    $name = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $name);
    
    // Remover caracteres especiais, manter apenas letras, números e espaços
    $name = preg_replace('/[^a-z0-9\s]/', '', $name);
    
    // Remover espaços extras
    $name = preg_replace('/\s+/', ' ', $name);
    
    // Remover espaços no início e fim
    $name = trim($name);
    
    return $name;
}

/**
 * Verificar similaridade entre nomes (para detectar nomes muito parecidos)
 */
function checkSimilarity($name1, $name2, $threshold = 80) {
    $normalized1 = normalizeName($name1);
    $normalized2 = normalizeName($name2);
    
    // Calcular similaridade usando similar_text
    $similarity = 0;
    similar_text($normalized1, $normalized2, $similarity);
    
    return $similarity >= $threshold;
}

/**
 * Buscar itens similares (para alertar sobre possíveis duplicatas)
 */
function findSimilarItems($name, $pdo, $excludeId = 0, $limit = 5) {
    $normalizedName = normalizeName($name);
    $words = explode(' ', $normalizedName);
    
    if (empty($words)) return [];
    
    // Construir query para buscar itens com palavras similares
    $conditions = [];
    $params = [];
    
    foreach ($words as $word) {
        if (strlen($word) >= 3) { // Apenas palavras com 3+ caracteres
            $conditions[] = "LOWER(name) LIKE ?";
            $params[] = "%$word%";
        }
    }
    
    if (empty($conditions)) return [];
    
    $whereClause = implode(' OR ', $conditions);
    
    if ($excludeId > 0) {
        $whereClause = "($whereClause) AND id != ?";
        $params[] = $excludeId;
    }
    
    $sql = "SELECT id, name, internal_code, category FROM items WHERE $whereClause LIMIT $limit";
    
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}
?>
