<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_products'])) {
    try {
        // Produtos adicionais de cozinha organizados por categoria
        $additionalProducts = [
            // PRODUTOS CONGELADOS
            ['Batata Frita Congelada', 'Batata frita pré-cozida congelada', 20, 'kg'],
            ['Hambúrguer Congelado', 'Hambúrguer bovino congelado', 15, 'kg'],
            ['Nuggets de Frango', 'Nuggets de frango congelados', 10, 'kg'],
            ['Polpa de Açaí', 'Polpa de açaí congelada', 25, 'kg'],
            ['<PERSON><PERSON> de Acerola', '<PERSON><PERSON> de acerola congelada', 15, 'kg'],
            ['Camar<PERSON> Congelado', 'Camarão médio congelado', 8, 'kg'],
            
            // PRODUTOS ENLATADOS/CONSERVAS
            ['Sardinha em Lata', 'Sardinha em óleo', 30, 'latas'],
            ['Atum em Lata', 'Atum em óleo', 25, 'latas'],
            ['Palmito', 'Palmito em conserva', 20, 'vidros'],
            ['Cogumelo em Conserva', 'Cogumelo champignon', 15, 'vidros'],
            ['Pêssego em Calda', 'Pêssego em calda', 20, 'latas'],
            ['Abacaxi em Calda', 'Abacaxi em calda', 18, 'latas'],
            
            // PRODUTOS SECOS/DESIDRATADOS
            ['Uva Passa', 'Uva passa sem semente', 5, 'kg'],
            ['Damasco Seco', 'Damasco seco', 3, 'kg'],
            ['Castanha do Pará', 'Castanha do Pará', 5, 'kg'],
            ['Amendoim', 'Amendoim torrado', 8, 'kg'],
            ['Nozes', 'Nozes sem casca', 3, 'kg'],
            ['Amêndoas', 'Amêndoas sem casca', 3, 'kg'],
            
            // PRODUTOS PARA PANIFICAÇÃO
            ['Fermento Biológico', 'Fermento biológico seco', 2, 'kg'],
            ['Fermento Químico', 'Fermento em pó', 10, 'potes'],
            ['Essência de Baunilha', 'Essência de baunilha', 5, 'frascos'],
            ['Chocolate em Pó', 'Chocolate em pó 50%', 10, 'kg'],
            ['Leite Condensado', 'Leite condensado', 30, 'latas'],
            ['Leite em Pó', 'Leite em pó integral', 15, 'kg'],
            ['Coco Ralado', 'Coco ralado seco', 8, 'kg'],
            
            // PRODUTOS DIET/LIGHT
            ['Adoçante Líquido', 'Adoçante líquido stevia', 10, 'frascos'],
            ['Açúcar Demerara', 'Açúcar demerara orgânico', 15, 'kg'],
            ['Sal Light', 'Sal com redução de sódio', 8, 'kg'],
            ['Leite Desnatado em Pó', 'Leite desnatado em pó', 10, 'kg'],
            
            // PRODUTOS ORIENTAIS
            ['Shoyu', 'Molho shoyu tradicional', 15, 'frascos'],
            ['Gergelim', 'Gergelim branco', 3, 'kg'],
            ['Óleo de Gergelim', 'Óleo de gergelim', 2, 'litros'],
            ['Wasabi', 'Wasabi em pasta', 5, 'tubos'],
            ['Nori', 'Alga nori para sushi', 10, 'pacotes'],
            
            // PRODUTOS PARA SOBREMESAS
            ['Gelatina sem Sabor', 'Gelatina incolor sem sabor', 20, 'pacotes'],
            ['Gelatina Colorida', 'Gelatina sabores variados', 30, 'pacotes'],
            ['Pudim em Pó', 'Pudim em pó sabor baunilha', 15, 'pacotes'],
            ['Mousse em Pó', 'Mousse em pó chocolate', 12, 'pacotes'],
            ['Chantilly em Pó', 'Chantilly em pó', 10, 'pacotes'],
            
            // PRODUTOS FUNCIONAIS
            ['Chia', 'Semente de chia', 5, 'kg'],
            ['Linhaça', 'Linhaça dourada', 8, 'kg'],
            ['Goji Berry', 'Goji berry desidratado', 2, 'kg'],
            ['Açaí em Pó', 'Açaí em pó liofilizado', 3, 'kg'],
            
            // PRODUTOS PARA TEMPERO
            ['Alecrim', 'Alecrim desidratado', 5, 'potes'],
            ['Tomilho', 'Tomilho desidratado', 5, 'potes'],
            ['Louro', 'Folha de louro', 3, 'potes'],
            ['Curry', 'Curry em pó', 3, 'potes'],
            ['Açafrão', 'Açafrão da terra', 2, 'potes'],
            ['Colorau', 'Colorau em pó', 5, 'potes']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO items (name, description, quantity, unit) VALUES (?, ?, ?, ?)");
        $addedCount = 0;
        
        foreach ($additionalProducts as $product) {
            // Verificar se o produto já existe
            $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM items WHERE name = ?");
            $checkStmt->execute([$product[0]]);
            
            if ($checkStmt->fetchColumn() == 0) {
                $stmt->execute($product);
                $addedCount++;
            }
        }
        
        $message = "Foram adicionados $addedCount novos produtos ao estoque!";
        
    } catch (PDOException $e) {
        $error = 'Erro ao adicionar produtos: ' . $e->getMessage();
    }
}

// Contar produtos atuais
$stmt = $pdo->query("SELECT COUNT(*) FROM items");
$currentCount = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Adicionar Produtos de Cozinha - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <h2>Adicionar Produtos de Cozinha</h2>
        
        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-header">
                <h5>Produtos Adicionais para Cozinha</h5>
            </div>
            <div class="card-body">
                <p><strong>Produtos atualmente cadastrados:</strong> <?php echo $currentCount; ?></p>
                
                <p>Este script adiciona produtos complementares organizados nas seguintes categorias:</p>
                <ul>
                    <li><strong>Produtos Congelados:</strong> Batata frita, hambúrguer, nuggets, polpas de frutas</li>
                    <li><strong>Enlatados/Conservas:</strong> Sardinha, atum, palmito, cogumelos</li>
                    <li><strong>Produtos Secos:</strong> Uva passa, castanhas, amendoim, nozes</li>
                    <li><strong>Panificação:</strong> Fermentos, essências, chocolate em pó</li>
                    <li><strong>Diet/Light:</strong> Adoçantes, açúcar demerara, sal light</li>
                    <li><strong>Orientais:</strong> Shoyu, gergelim, wasabi, nori</li>
                    <li><strong>Sobremesas:</strong> Gelatinas, pudins, mousses</li>
                    <li><strong>Funcionais:</strong> Chia, linhaça, goji berry</li>
                    <li><strong>Temperos Especiais:</strong> Alecrim, tomilho, curry, açafrão</li>
                </ul>
                
                <form method="post">
                    <button type="submit" name="add_products" class="btn btn-primary" 
                            onclick="return confirm('Deseja adicionar todos os produtos complementares ao estoque?')">
                        Adicionar Produtos Complementares
                    </button>
                </form>
                
                <div class="mt-3">
                    <a href="manage_items.php" class="btn btn-secondary">Voltar para Gerenciar Itens</a>
                    <a href="add_rotisserie_products.php" class="btn btn-warning">🍖 Produtos de Rotisseria</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
