<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpar Cache - Sistema de Gerenciamento</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .step-card {
            transition: transform 0.2s;
        }
        .step-card:hover {
            transform: translateY(-2px);
        }
        .browser-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="text-center mb-4">
            <h1><i class="fas fa-sync-alt text-primary"></i> Limpar Cache do Navegador</h1>
            <p class="lead">Se você ainda está vendo erros JavaScript, o problema pode ser cache do navegador</p>
        </div>

        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card step-card h-100">
                    <div class="card-body text-center">
                        <div class="browser-icon text-primary">
                            <i class="fab fa-chrome"></i>
                        </div>
                        <h5>Google Chrome</h5>
                        <div class="text-left">
                            <strong>Método 1 - Atualização Forçada:</strong>
                            <ul>
                                <li>Pressione <kbd>Ctrl + F5</kbd></li>
                                <li>Ou <kbd>Ctrl + Shift + R</kbd></li>
                            </ul>
                            
                            <strong>Método 2 - Limpar Cache:</strong>
                            <ul>
                                <li>Pressione <kbd>Ctrl + Shift + Delete</kbd></li>
                                <li>Selecione "Imagens e arquivos em cache"</li>
                                <li>Clique em "Limpar dados"</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card step-card h-100">
                    <div class="card-body text-center">
                        <div class="browser-icon text-warning">
                            <i class="fab fa-firefox"></i>
                        </div>
                        <h5>Mozilla Firefox</h5>
                        <div class="text-left">
                            <strong>Método 1 - Atualização Forçada:</strong>
                            <ul>
                                <li>Pressione <kbd>Ctrl + F5</kbd></li>
                                <li>Ou <kbd>Ctrl + Shift + R</kbd></li>
                            </ul>
                            
                            <strong>Método 2 - Limpar Cache:</strong>
                            <ul>
                                <li>Pressione <kbd>Ctrl + Shift + Delete</kbd></li>
                                <li>Selecione "Cache"</li>
                                <li>Clique em "Limpar agora"</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card step-card h-100">
                    <div class="card-body text-center">
                        <div class="browser-icon text-info">
                            <i class="fab fa-edge"></i>
                        </div>
                        <h5>Microsoft Edge</h5>
                        <div class="text-left">
                            <strong>Método 1 - Atualização Forçada:</strong>
                            <ul>
                                <li>Pressione <kbd>Ctrl + F5</kbd></li>
                                <li>Ou <kbd>Ctrl + Shift + R</kbd></li>
                            </ul>
                            
                            <strong>Método 2 - Limpar Cache:</strong>
                            <ul>
                                <li>Pressione <kbd>Ctrl + Shift + Delete</kbd></li>
                                <li>Selecione "Imagens e arquivos em cache"</li>
                                <li>Clique em "Limpar agora"</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card step-card h-100">
                    <div class="card-body text-center">
                        <div class="browser-icon text-secondary">
                            <i class="fab fa-safari"></i>
                        </div>
                        <h5>Safari</h5>
                        <div class="text-left">
                            <strong>Método 1 - Atualização Forçada:</strong>
                            <ul>
                                <li>Pressione <kbd>Cmd + Shift + R</kbd></li>
                                <li>Ou <kbd>Cmd + Option + R</kbd></li>
                            </ul>
                            
                            <strong>Método 2 - Limpar Cache:</strong>
                            <ul>
                                <li>Menu Safari → Preferências</li>
                                <li>Aba "Avançado" → Mostrar menu Desenvolver</li>
                                <li>Menu Desenvolver → Esvaziar Caches</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card bg-light">
            <div class="card-body">
                <h5><i class="fas fa-tools"></i> Ferramentas de Desenvolvedor</h5>
                <p>Para uma limpeza mais específica, use as Ferramentas de Desenvolvedor:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>1. Abrir DevTools:</strong>
                        <ul>
                            <li>Pressione <kbd>F12</kbd></li>
                            <li>Ou clique com botão direito → "Inspecionar"</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <strong>2. Limpar Cache:</strong>
                        <ul>
                            <li>Clique com botão direito no botão de atualizar</li>
                            <li>Selecione "Esvaziar cache e recarregar"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4 border-success">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-check-circle"></i> Verificar se o Problema Foi Resolvido</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>✅ Passos para Verificar:</h6>
                        <ol>
                            <li>Limpe o cache usando um dos métodos acima</li>
                            <li>Abra o Console do Navegador (F12)</li>
                            <li>Acesse a página manage_items.php</li>
                            <li>Verifique se não há erros em vermelho</li>
                            <li>Teste as funcionalidades da página</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>🎯 Funcionalidades para Testar:</h6>
                        <ul>
                            <li>Validação de nome em tempo real</li>
                            <li>Sugestões de nomes alternativos</li>
                            <li>Botão "Limpar Campos"</li>
                            <li>Exibição de códigos de barras</li>
                            <li>Impressão de códigos de barras</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="btn-group" role="group">
                <a href="manage_items.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-arrow-right"></i> Ir para Gerenciamento de Itens
                </a>
                <a href="debug_js.php" class="btn btn-info btn-lg">
                    <i class="fas fa-bug"></i> Página de Debug
                </a>
            </div>
        </div>

        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-lightbulb"></i> Dica Importante:</h6>
            <p class="mb-0">
                Se após limpar o cache você ainda vir erros JavaScript, pode ser que o navegador esteja 
                carregando uma versão antiga do arquivo. Neste caso, aguarde alguns minutos ou reinicie 
                o navegador completamente.
            </p>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Adicionar timestamp para forçar atualização
        console.log('🕒 Página carregada em:', new Date().toLocaleString());
        console.log('🔄 Se você ainda vê erros JavaScript, limpe o cache do navegador');
        
        // Verificar se há erros JavaScript
        window.addEventListener('error', function(e) {
            console.error('❌ Erro JavaScript detectado:', e.error);
        });
        
        // Mostrar informações do navegador
        console.log('🌐 Navegador:', navigator.userAgent);
    </script>
</body>
</html>
