import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Card,
  Button,
  ActivityIndicator,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Toast from 'react-native-toast-message';

import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { theme, commonStyles, statusColors } from '../theme/theme';

export default function DashboardScreen({ navigation }) {
  const [stats, setStats] = useState(null);
  const [recentRequests, setRecentRequests] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const { user, logout } = useAuth();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);

      // Carregar estatísticas
      const statsResponse = await apiService.getStats();
      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      // Carregar requisições recentes
      const requestsResponse = await apiService.getRequests(1, 5);
      if (requestsResponse.success) {
        setRecentRequests(requestsResponse.data.data);
      }

    } catch (error) {
      console.error('Erro ao carregar dashboard:', error);
      Toast.show({
        type: 'error',
        text1: 'Erro',
        text2: 'Erro ao carregar dados do dashboard',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadDashboardData();
    setIsRefreshing(false);
  }, []);

  const handleLogout = () => {
    logout();
  };

  const getStatusColor = (status) => {
    return statusColors[status] || statusColors.pending;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <View style={[commonStyles.container, commonStyles.centerContent]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Carregando dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={commonStyles.container}
      contentContainerStyle={styles.content}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.welcomeText}>Olá, {user?.username}!</Text>
          <Text style={styles.dateText}>
            {new Date().toLocaleDateString('pt-BR', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </View>
        <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
          <Icon name="logout" size={24} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>

      {/* Estatísticas */}
      {stats && (
        <Card style={styles.statsCard}>
          <Card.Content>
            <Text style={styles.cardTitle}>Resumo das Requisições</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.total}</Text>
                <Text style={styles.statLabel}>Total</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: statusColors.pending.text }]}>
                  {stats.pending}
                </Text>
                <Text style={styles.statLabel}>Pendentes</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: statusColors.approved.text }]}>
                  {stats.approved}
                </Text>
                <Text style={styles.statLabel}>Aprovadas</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: statusColors.delivered.text }]}>
                  {stats.delivered}
                </Text>
                <Text style={styles.statLabel}>Entregues</Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Ações Rápidas */}
      <Card style={styles.actionsCard}>
        <Card.Content>
          <Text style={styles.cardTitle}>Ações Rápidas</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Nova')}
            >
              <Icon name="add-circle" size={32} color={theme.colors.primary} />
              <Text style={styles.actionText}>Nova Requisição</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Requisições')}
            >
              <Icon name="assignment" size={32} color={theme.colors.info} />
              <Text style={styles.actionText}>Minhas Requisições</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Itens')}
            >
              <Icon name="inventory" size={32} color={theme.colors.success} />
              <Text style={styles.actionText}>Produtos</Text>
            </TouchableOpacity>
          </View>
        </Card.Content>
      </Card>

      {/* Requisições Recentes */}
      <Card style={styles.recentCard}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Requisições Recentes</Text>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Requisições')}
              compact
            >
              Ver todas
            </Button>
          </View>

          {recentRequests.length > 0 ? (
            recentRequests.map((request) => (
              <TouchableOpacity
                key={request.id}
                style={styles.requestItem}
                onPress={() =>
                  navigation.navigate('RequestDetails', { requestId: request.id })
                }
              >
                <View style={styles.requestHeader}>
                  <Text style={styles.requestTitle}>
                    {request.title || `Requisição #${request.id}`}
                  </Text>
                  <Chip
                    mode="outlined"
                    style={{
                      backgroundColor: getStatusColor(request.status).background,
                    }}
                    textStyle={{
                      color: getStatusColor(request.status).text,
                      fontSize: 12,
                    }}
                  >
                    {request.status === 'pending' && 'Pendente'}
                    {request.status === 'approved' && 'Aprovada'}
                    {request.status === 'rejected' && 'Rejeitada'}
                    {request.status === 'delivered' && 'Entregue'}
                  </Chip>
                </View>
                <View style={styles.requestInfo}>
                  <Text style={styles.requestDate}>
                    {formatDate(request.request_date)}
                  </Text>
                  <Text style={styles.requestItems}>
                    {request.item_count} item(ns)
                  </Text>
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Icon name="assignment" size={48} color={theme.colors.textSecondary} />
              <Text style={styles.emptyText}>Nenhuma requisição encontrada</Text>
              <Button
                mode="contained"
                onPress={() => navigation.navigate('Nova')}
                style={styles.emptyButton}
              >
                Criar primeira requisição
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  content: {
    padding: theme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  dateText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textTransform: 'capitalize',
  },
  logoutButton: {
    padding: theme.spacing.sm,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    color: theme.colors.textSecondary,
  },
  statsCard: {
    marginBottom: theme.spacing.md,
    ...theme.shadows.small,
  },
  actionsCard: {
    marginBottom: theme.spacing.md,
    ...theme.shadows.small,
  },
  recentCard: {
    marginBottom: theme.spacing.md,
    ...theme.shadows.small,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    alignItems: 'center',
    padding: theme.spacing.md,
  },
  actionText: {
    fontSize: 12,
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },
  requestItem: {
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  requestTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  requestInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  requestDate: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  requestItems: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  emptyButton: {
    marginTop: theme.spacing.sm,
  },
});
