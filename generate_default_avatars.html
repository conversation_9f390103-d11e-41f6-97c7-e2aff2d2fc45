<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerador de Avatars Padrão</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
        }
        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .avatar-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .avatar-display {
            margin: 10px 0;
        }
        canvas {
            border-radius: 50%;
            border: 3px solid #007bff;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            font-size: 12px;
            cursor: pointer;
            border: none;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 Gerador de Avatars Padrão</h1>
            <p>Sistema de Requisição de Material de Cozinha</p>
        </div>

        <div class="avatar-grid">
            <div class="avatar-item">
                <h3>Thumb (32x32)</h3>
                <div class="avatar-display">
                    <canvas id="avatar-thumb" width="32" height="32"></canvas>
                </div>
                <button class="btn" onclick="downloadAvatar('avatar-thumb', 'default-avatar-thumb.png')">
                    📥 Download
                </button>
            </div>

            <div class="avatar-item">
                <h3>Small (50x50)</h3>
                <div class="avatar-display">
                    <canvas id="avatar-small" width="50" height="50"></canvas>
                </div>
                <button class="btn" onclick="downloadAvatar('avatar-small', 'default-avatar-small.png')">
                    📥 Download
                </button>
            </div>

            <div class="avatar-item">
                <h3>Medium (100x100)</h3>
                <div class="avatar-display">
                    <canvas id="avatar-medium" width="100" height="100"></canvas>
                </div>
                <button class="btn" onclick="downloadAvatar('avatar-medium', 'default-avatar-medium.png')">
                    📥 Download
                </button>
            </div>

            <div class="avatar-item">
                <h3>Large (200x200)</h3>
                <div class="avatar-display">
                    <canvas id="avatar-large" width="200" height="200"></canvas>
                </div>
                <button class="btn" onclick="downloadAvatar('avatar-large', 'default-avatar-large.png')">
                    📥 Download
                </button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="downloadAllAvatars()" style="font-size: 16px; padding: 12px 24px;">
                📦 Download Todos os Avatars
            </button>
        </div>
    </div>

    <script>
        function drawDefaultAvatar(canvas, size) {
            const ctx = canvas.getContext('2d');
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size / 2 - 2;
            
            // Limpar canvas
            ctx.clearRect(0, 0, size, size);
            
            // Fundo circular com gradiente
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#6c757d');
            gradient.addColorStop(1, '#495057');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Ícone de usuário
            ctx.fillStyle = '#ffffff';
            ctx.globalAlpha = 0.8;
            
            // Cabeça (círculo)
            const headRadius = size * 0.15;
            ctx.beginPath();
            ctx.arc(centerX, centerY - size * 0.1, headRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Corpo (semicírculo)
            const bodyRadius = size * 0.25;
            ctx.beginPath();
            ctx.arc(centerX, centerY + size * 0.3, bodyRadius, Math.PI, 0, false);
            ctx.fill();
            
            ctx.globalAlpha = 1;
        }

        function generateAllAvatars() {
            const sizes = [
                {id: 'avatar-thumb', size: 32},
                {id: 'avatar-small', size: 50},
                {id: 'avatar-medium', size: 100},
                {id: 'avatar-large', size: 200}
            ];
            
            sizes.forEach(item => {
                const canvas = document.getElementById(item.id);
                drawDefaultAvatar(canvas, item.size);
            });
        }

        function downloadAvatar(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function downloadAllAvatars() {
            const avatars = [
                {id: 'avatar-thumb', name: 'default-avatar-thumb.png'},
                {id: 'avatar-small', name: 'default-avatar-small.png'},
                {id: 'avatar-medium', name: 'default-avatar-medium.png'},
                {id: 'avatar-large', name: 'default-avatar-large.png'}
            ];
            
            avatars.forEach((avatar, index) => {
                setTimeout(() => {
                    downloadAvatar(avatar.id, avatar.name);
                }, index * 500);
            });
        }

        // Gerar avatars quando a página carregar
        window.addEventListener('load', generateAllAvatars);
    </script>
</body>
</html>
