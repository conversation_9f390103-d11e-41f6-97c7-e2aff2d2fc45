<?php
require_once 'config/db_connect.php';

// Dados do usuário admin
$username = 'admin';
$password = 'admin123';
$role = 'admin';

// Gerar hash da senha
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

// Verificar se o usuário já existe
$stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
$stmt->execute([$username]);
$user = $stmt->fetch();

if ($user) {
    echo "Usuário admin já existe!";
} else {
    // Inserir novo usuário admin
    $stmt = $pdo->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
    if ($stmt->execute([$username, $hashed_password, $role])) {
        echo "Usuário admin criado com sucesso!<br>";
        echo "Username: $username<br>";
        echo "Senha: $password<br>";
        echo "Role: $role<br>";
    } else {
        echo "Erro ao criar usuário admin.";
    }
}
?>