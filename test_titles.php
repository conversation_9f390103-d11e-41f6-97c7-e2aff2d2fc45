<?php
require_once 'config/db_connect.php';

echo "<h2>🧪 Teste de Títulos de Requisições</h2>";

try {
    // Verificar se a coluna 'title' existe
    $stmt = $pdo->query("SHOW COLUMNS FROM requests LIKE 'title'");
    $columnExists = $stmt->rowCount() > 0;
    
    if ($columnExists) {
        echo "<p>✅ Coluna 'title' existe na tabela 'requests'</p>";
        
        // Buscar algumas requisições para teste
        $stmt = $pdo->query("SELECT id, title, request_date FROM requests ORDER BY id DESC LIMIT 5");
        $requests = $stmt->fetchAll();
        
        if (empty($requests)) {
            echo "<p>ℹ️ Nenhuma requisição encontrada no banco</p>";
        } else {
            echo "<h3>📋 Últimas 5 Requisições:</h3>";
            echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>T<PERSON>tulo</th><th>Data</th><th>Nome Exibido</th></tr>";
            
            foreach ($requests as $request) {
                $displayName = $request['title'] ? htmlspecialchars($request['title']) : 'Requisição #' . $request['id'];
                echo "<tr>";
                echo "<td>#" . $request['id'] . "</td>";
                echo "<td>" . ($request['title'] ? htmlspecialchars($request['title']) : '<em>NULL</em>') . "</td>";
                echo "<td>" . date('d/m/Y H:i', strtotime($request['request_date'])) . "</td>";
                echo "<td><strong>" . $displayName . "</strong></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Verificar quantas requisições têm título
        $stmt = $pdo->query("SELECT COUNT(*) as total, 
                                   SUM(CASE WHEN title IS NOT NULL AND title != '' THEN 1 ELSE 0 END) as with_title
                            FROM requests");
        $stats = $stmt->fetch();
        
        echo "<h3>📊 Estatísticas:</h3>";
        echo "<ul>";
        echo "<li><strong>Total de requisições:</strong> " . $stats['total'] . "</li>";
        echo "<li><strong>Com título personalizado:</strong> " . $stats['with_title'] . "</li>";
        echo "<li><strong>Sem título:</strong> " . ($stats['total'] - $stats['with_title']) . "</li>";
        echo "</ul>";
        
    } else {
        echo "<p>❌ Coluna 'title' NÃO existe na tabela 'requests'</p>";
        echo "<p>Execute o arquivo <a href='update_db_for_titles.php'>update_db_for_titles.php</a> para adicionar a coluna.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Erro ao verificar banco de dados: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🔗 Links de Teste:</h3>";
echo "<ul>";
echo "<li><a href='my_requests.php'>📋 Minhas Requisições</a></li>";
echo "<li><a href='manage_requests.php'>🔧 Gerenciar Requisições (Admin)</a></li>";
echo "<li><a href='request_form.php'>➕ Nova Requisição</a></li>";
echo "<li><a href='update_db_for_titles.php'>🔄 Atualizar Banco de Dados</a></li>";
echo "</ul>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Teste de Títulos - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body { padding: 20px; }
        table { margin: 20px 0; }
        th, td { padding: 8px 12px; text-align: left; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Conteúdo já foi exibido acima -->
    </div>
</body>
</html>
