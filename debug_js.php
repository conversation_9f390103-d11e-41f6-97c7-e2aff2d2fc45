<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug JavaScript - Sistema de Gerenciamento</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .console-output {
            background: #1e1e1e;
            color: #fff;
            font-family: 'Courier New', monospace;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error { color: #ff6b6b; }
        .warning { color: #feca57; }
        .info { color: #48dbfb; }
        .success { color: #1dd1a1; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2><i class="fas fa-bug"></i> Debug JavaScript - Sistema de Gerenciamento</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-code"></i> Teste de Funções JavaScript</h5>
                    </div>
                    <div class="card-body">
                        <div class="btn-group-vertical w-100">
                            <button class="btn btn-primary mb-2" onclick="testShowBarcode()">
                                <i class="fas fa-barcode"></i> Testar showBarcode()
                            </button>
                            <button class="btn btn-secondary mb-2" onclick="testClearForm()">
                                <i class="fas fa-eraser"></i> Testar clearForm()
                            </button>
                            <button class="btn btn-info mb-2" onclick="testValidateForm()">
                                <i class="fas fa-check"></i> Testar validateForm()
                            </button>
                            <button class="btn btn-warning mb-2" onclick="testAjaxCall()">
                                <i class="fas fa-wifi"></i> Testar AJAX
                            </button>
                            <button class="btn btn-success mb-2" onclick="testDOMElements()">
                                <i class="fas fa-search"></i> Verificar DOM
                            </button>
                            <button class="btn btn-danger" onclick="clearConsole()">
                                <i class="fas fa-trash"></i> Limpar Console
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-terminal"></i> Console de Debug</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-console" class="console-output">
                            <div class="info">🚀 Console de debug iniciado...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Instruções</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Abra o Console do Navegador</strong> (F12 → Console)</li>
                    <li><strong>Clique nos botões de teste</strong> para verificar cada função</li>
                    <li><strong>Observe os resultados</strong> tanto no console do navegador quanto no console de debug</li>
                    <li><strong>Verifique se há erros</strong> em vermelho no console do navegador</li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <strong>Dica:</strong> Se você ainda está vendo erros JavaScript, pode ser cache do navegador. 
                    Pressione <kbd>Ctrl+F5</kbd> para forçar atualização ou limpe o cache.
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de teste para código de barras -->
    <div class="modal fade" id="barcodeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-barcode"></i> Código de Barras
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <h6 id="barcodeItemName">Nome do Item</h6>
                    <div id="barcodeDisplay" class="font-monospace">123456789</div>
                    <div id="barcodeQR"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Função para log no console de debug
        function debugLog(message, type = 'info') {
            const console = document.getElementById('debug-console');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            console.appendChild(logEntry);
            console.scrollTop = console.scrollHeight;
        }

        // Testar função showBarcode
        function testShowBarcode() {
            debugLog('🧪 Testando showBarcode()...', 'info');
            try {
                // Simular a função showBarcode do manage_items.php
                const barcodeNameEl = document.getElementById('barcodeItemName');
                const barcodeDisplayEl = document.getElementById('barcodeDisplay');
                const barcodeQREl = document.getElementById('barcodeQR');
                
                if (!barcodeNameEl || !barcodeDisplayEl || !barcodeQREl) {
                    throw new Error('Elementos do modal não encontrados');
                }
                
                barcodeNameEl.textContent = 'Item de Teste';
                barcodeDisplayEl.textContent = '1234567890123';
                barcodeQREl.innerHTML = '<div class="text-muted">QR Code seria gerado aqui</div>';
                
                $('#barcodeModal').modal('show');
                debugLog('✅ showBarcode() funcionou corretamente', 'success');
            } catch (error) {
                debugLog(`❌ Erro em showBarcode(): ${error.message}`, 'error');
                console.error('Erro em showBarcode():', error);
            }
        }

        // Testar função clearForm
        function testClearForm() {
            debugLog('🧪 Testando clearForm()...', 'info');
            try {
                // Criar campos de teste temporários
                const testForm = document.createElement('div');
                testForm.innerHTML = `
                    <input type="text" name="test-name" value="Teste">
                    <select name="test-unit"><option value="kg" selected>kg</option></select>
                `;
                document.body.appendChild(testForm);
                
                // Simular limpeza
                testForm.querySelectorAll('input, select').forEach(field => {
                    if (!field.readOnly && !field.disabled) {
                        field.value = '';
                    }
                });
                
                document.body.removeChild(testForm);
                debugLog('✅ clearForm() funcionou corretamente', 'success');
            } catch (error) {
                debugLog(`❌ Erro em clearForm(): ${error.message}`, 'error');
                console.error('Erro em clearForm():', error);
            }
        }

        // Testar função validateForm
        function testValidateForm() {
            debugLog('🧪 Testando validateForm()...', 'info');
            try {
                // Criar campos de teste
                const testForm = document.createElement('form');
                testForm.innerHTML = `
                    <input type="text" name="name" value="Item Teste">
                    <select name="unit"><option value="kg" selected>kg</option></select>
                `;
                document.body.appendChild(testForm);
                
                const nameInput = testForm.querySelector('input[name="name"]');
                const unitSelect = testForm.querySelector('select[name="unit"]');
                
                if (!nameInput || !unitSelect) {
                    throw new Error('Campos não encontrados');
                }
                
                const name = nameInput.value.trim();
                const unit = unitSelect.value;
                
                if (!name || !unit) {
                    throw new Error('Validação falhou');
                }
                
                document.body.removeChild(testForm);
                debugLog('✅ validateForm() funcionou corretamente', 'success');
            } catch (error) {
                debugLog(`❌ Erro em validateForm(): ${error.message}`, 'error');
                console.error('Erro em validateForm():', error);
            }
        }

        // Testar chamada AJAX
        function testAjaxCall() {
            debugLog('🧪 Testando chamada AJAX...', 'info');
            try {
                fetch('check_duplicate_item.php', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'name=teste'
                })
                .then(response => {
                    debugLog(`📡 Resposta HTTP: ${response.status}`, response.ok ? 'success' : 'warning');
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    debugLog('✅ AJAX funcionou corretamente', 'success');
                    debugLog(`📄 Dados recebidos: ${JSON.stringify(data)}`, 'info');
                })
                .catch(error => {
                    debugLog(`❌ Erro AJAX: ${error.message}`, 'error');
                    console.error('Erro AJAX:', error);
                });
            } catch (error) {
                debugLog(`❌ Erro ao iniciar AJAX: ${error.message}`, 'error');
                console.error('Erro ao iniciar AJAX:', error);
            }
        }

        // Verificar elementos DOM
        function testDOMElements() {
            debugLog('🧪 Verificando elementos DOM...', 'info');
            
            const elements = [
                'input[name="name"]',
                'select[name="unit"]',
                'form[method="post"]',
                '.container',
                '#barcodeModal'
            ];
            
            elements.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    debugLog(`✅ Encontrado: ${selector}`, 'success');
                } else {
                    debugLog(`❌ Não encontrado: ${selector}`, 'warning');
                }
            });
            
            // Verificar bibliotecas
            if (typeof $ !== 'undefined') {
                debugLog('✅ jQuery carregado', 'success');
            } else {
                debugLog('❌ jQuery não encontrado', 'error');
            }
            
            if (typeof bootstrap !== 'undefined' || (typeof $ !== 'undefined' && $.fn.modal)) {
                debugLog('✅ Bootstrap carregado', 'success');
            } else {
                debugLog('❌ Bootstrap não encontrado', 'warning');
            }
        }

        // Limpar console
        function clearConsole() {
            document.getElementById('debug-console').innerHTML = '<div class="info">🚀 Console limpo...</div>';
        }

        // Inicialização
        $(document).ready(function() {
            debugLog('🎯 Sistema de debug carregado', 'success');
            debugLog('👆 Clique nos botões para testar as funções', 'info');
        });
    </script>
</body>
</html>
