<?php
// Configurações da página
$page_title = 'Minhas Requisições';
$page_subtitle = 'Gerencie suas solicitações de material de cozinha';
$page_header = true;

// Incluir layout
require_once 'includes/layout.php';
require_once 'config/db_connect.php';

// Buscar requisições do usuário (verificar se coluna title existe)
try {
    $stmt = $pdo->prepare("
        SELECT r.*, COUNT(ri.id) as item_count
        FROM requests r
        JOIN request_items ri ON r.id = ri.request_id
        WHERE r.user_id = ?
        GROUP BY r.id
        ORDER BY r.request_date DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $requests = $stmt->fetchAll();
    $hasTitleColumn = true;
} catch (PDOException $e) {
    // Fallback se coluna title não existir
    $stmt = $pdo->prepare("
        SELECT r.*, COUNT(ri.id) as item_count
        FROM requests r
        JOIN request_items ri ON r.id = ri.request_id
        WHERE r.user_id = ?
        GROUP BY r.id
        ORDER BY r.request_date DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $requests = $stmt->fetchAll();
    $hasTitleColumn = false;
}
?>
<?php if (!$hasTitleColumn): ?>
<div class="alert alert-warning alert-dismissible fade show" role="alert" aria-live="polite">
    <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
    <strong>⚠️ Atualização Disponível:</strong>
    O sistema foi atualizado para suportar nomes personalizados de requisições.
    <a href="update_db_for_titles.php" class="btn btn-sm btn-primary ms-2">Atualizar Banco de Dados</a>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar alerta"></button>
</div>
<?php endif; ?>

<?php if (empty($requests)): ?>
<div class="content-card text-center py-5">
    <i class="fas fa-inbox fa-4x text-muted mb-4" aria-hidden="true"></i>
    <h3 class="text-muted mb-3">Nenhuma requisição encontrada</h3>
    <p class="text-muted mb-4">Você ainda não possui requisições de material.</p>
    <a href="request_form.php" class="btn btn-primary btn-custom btn-lg">
        <i class="fas fa-plus me-2" aria-hidden="true"></i>
        Criar Primeira Requisição
    </a>
</div>
<?php else: ?>
<div class="content-card">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="mb-0">
            <i class="fas fa-list text-primary me-2"></i>
            Suas Requisições (<?php echo count($requests); ?>)
        </h3>
        <a href="request_form.php" class="btn btn-primary btn-custom">
            <i class="fas fa-plus me-2"></i>
            Nova Requisição
        </a>
    </div>

    <div class="table-responsive">
        <table class="table table-hover" id="dataTable" role="table" aria-label="Tabela de requisições do usuário">
            <thead class="table-dark">
                <tr role="row">
                    <th scope="col" role="columnheader">
                        <i class="fas fa-hashtag me-1" aria-hidden="true"></i>
                        Código
                    </th>
                    <th scope="col" role="columnheader">
                        <i class="fas fa-file-alt me-1" aria-hidden="true"></i>
                        Requisição
                    </th>
                    <th scope="col" role="columnheader">
                        <i class="fas fa-flag me-1" aria-hidden="true"></i>
                        Status
                    </th>
                    <th scope="col" role="columnheader">
                        <i class="fas fa-list me-1" aria-hidden="true"></i>
                        Itens
                    </th>
                    <th scope="col" role="columnheader">
                        <i class="fas fa-cogs me-1" aria-hidden="true"></i>
                        Ações
                    </th>
                </tr>
            </thead>
                <tbody>
                    <?php foreach ($requests as $request): ?>
                    <tr>
                        <td>
                            <?php
                            // Incluir biblioteca de códigos de barras
                            require_once 'includes/barcode_generator.php';

                            // Gerar código interno se não existir
                            $internalCode = $request['internal_code'] ?? 'REQ' . str_pad($request['id'], 6, '0', STR_PAD_LEFT);
                            ?>
                            <div class="text-center">
                                <small class="text-muted">ID: <?php echo $request['id']; ?></small><br>
                                <strong class="text-primary font-monospace"><?php echo htmlspecialchars($internalCode); ?></strong><br>
                                <a href="view_barcode.php?type=request&id=<?php echo $request['id']; ?>"
                                   class="btn btn-sm btn-outline-dark" title="Ver código de barras">
                                    <i class="fas fa-barcode"></i>
                                </a>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong class="text-primary"><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></strong>
                                <br><small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?></small>
                            </div>
                        </td>
                        <td>
                            <?php
                            $statusLabels = [
                                'pending' => '<span class="badge bg-warning text-dark">Pendente</span>',
                                'approved' => '<span class="badge bg-success">Aprovado</span>',
                                'rejected' => '<span class="badge bg-danger">Rejeitado</span>',
                                'delivered' => '<span class="badge bg-info">Entregue</span>'
                            ];
                            echo $statusLabels[$request['status']];
                            ?>
                        </td>
                        <td>
                            <span class="badge bg-secondary"><?php echo $request['item_count']; ?> itens</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="view_request.php?id=<?php echo $request['id']; ?>"
                                   class="btn btn-outline-info"
                                   title="Ver Detalhes"
                                   data-bs-toggle="tooltip">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php if ($request['status'] == 'pending'): ?>
                                <a href="edit_request.php?id=<?php echo $request['id']; ?>"
                                   class="btn btn-outline-warning"
                                   title="Editar"
                                   data-bs-toggle="tooltip">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php endif; ?>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button"
                                            class="btn btn-outline-success dropdown-toggle"
                                            data-bs-toggle="dropdown"
                                            title="Exportar"
                                            data-bs-toggle="tooltip">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=excel">
                                            <i class="fas fa-file-excel text-success me-2"></i>Excel
                                        </a></li>
                                        <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=pdf">
                                            <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                                        </a></li>
                                        <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=docx">
                                            <i class="fas fa-file-word text-primary me-2"></i>Word
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>

<?php
// Scripts específicos da página
$page_scripts = '
<script>
// Configurar busca na tabela
document.addEventListener("DOMContentLoaded", function() {
    if (document.getElementById("dataTable")) {
        setupTableSearch("searchInput", "dataTable");
    }
});
</script>

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Minhas Requisições",
    "description": "Página para visualizar e gerenciar requisições pessoais de material de cozinha",
    "url": "' . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . '",
    "mainEntity": {
        "@type": "ItemList",
        "name": "Lista de Requisições",
        "numberOfItems": ' . count($requests) . '
    }
}
</script>
';

// Incluir footer do layout
require_once 'includes/layout_footer.php';
?>