<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

// Buscar requisições do usuário (verificar se coluna title existe)
try {
    $stmt = $pdo->prepare("
        SELECT r.*, COUNT(ri.id) as item_count
        FROM requests r
        JOIN request_items ri ON r.id = ri.request_id
        WHERE r.user_id = ?
        GROUP BY r.id
        ORDER BY r.request_date DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $requests = $stmt->fetchAll();
    $hasTitleColumn = true;
} catch (PDOException $e) {
    // Fallback se coluna title não existir
    $stmt = $pdo->prepare("
        SELECT r.*, COUNT(ri.id) as item_count
        FROM requests r
        JOIN request_items ri ON r.id = ri.request_id
        WHERE r.user_id = ?
        GROUP BY r.id
        ORDER BY r.request_date DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $requests = $stmt->fetchAll();
    $hasTitleColumn = false;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <?php include 'includes/seo_meta.php'; ?>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .page-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .request-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border-radius: 10px;
        }
        .request-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <main class="container mt-4" role="main">
        <!-- Cabeçalho da Página -->
        <header class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-clipboard-list" aria-hidden="true"></i>
                        Minhas Requisições
                    </h1>
                    <p class="mb-0 lead">Gerencie suas solicitações de material de cozinha</p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-user-check fa-3x opacity-50" aria-hidden="true"></i>
                </div>
            </div>
        </header>

        <?php if (!$hasTitleColumn): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert" aria-live="polite">
                <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                <strong>⚠️ Atualização Disponível:</strong>
                O sistema foi atualizado para suportar nomes personalizados de requisições.
                <a href="update_db_for_titles.php" class="btn btn-sm btn-primary ml-2">Atualizar Banco de Dados</a>
                <button type="button" class="close" data-dismiss="alert" aria-label="Fechar alerta">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        <?php endif; ?>

        <?php if (empty($requests)): ?>
            <section class="alert alert-info text-center py-5" role="region" aria-labelledby="no-requests-title">
                <i class="fas fa-inbox fa-3x text-muted mb-3" aria-hidden="true"></i>
                <h3 id="no-requests-title" class="text-muted">Nenhuma requisição encontrada</h3>
                <p class="text-muted">Você ainda não possui requisições de material.</p>
                <a href="request_form.php" class="btn btn-primary">
                    <i class="fas fa-plus" aria-hidden="true"></i>
                    Criar Primeira Requisição
                </a>
            </section>
        <?php else: ?>
            <section aria-labelledby="requests-table-title">
                <h2 id="requests-table-title" class="sr-only">Lista de Requisições</h2>
                <div class="table-responsive">
                    <table class="table table-striped table-hover" role="table" aria-label="Tabela de requisições do usuário">
                        <thead class="thead-dark">
                            <tr role="row">
                                <th scope="col" role="columnheader">
                                    <i class="fas fa-hashtag" aria-hidden="true"></i>
                                    Código
                                </th>
                                <th scope="col" role="columnheader">
                                    <i class="fas fa-file-alt" aria-hidden="true"></i>
                                    Requisição
                                </th>
                                <th scope="col" role="columnheader">
                                    <i class="fas fa-flag" aria-hidden="true"></i>
                                    Status
                                </th>
                                <th scope="col" role="columnheader">
                                    <i class="fas fa-list" aria-hidden="true"></i>
                                    Itens
                                </th>
                                <th scope="col" role="columnheader">
                                    <i class="fas fa-cogs" aria-hidden="true"></i>
                                    Ações
                                </th>
                            </tr>
                        </thead>
                <tbody>
                    <?php foreach ($requests as $request): ?>
                    <tr>
                        <td>
                            <?php
                            // Incluir biblioteca de códigos de barras
                            require_once 'includes/barcode_generator.php';

                            // Gerar código interno se não existir
                            $internalCode = $request['internal_code'] ?? 'REQ' . str_pad($request['id'], 6, '0', STR_PAD_LEFT);
                            ?>
                            <div class="text-center">
                                <small class="text-muted">ID: <?php echo $request['id']; ?></small><br>
                                <strong class="text-primary font-monospace"><?php echo htmlspecialchars($internalCode); ?></strong><br>
                                <a href="view_barcode.php?type=request&id=<?php echo $request['id']; ?>"
                                   class="btn btn-sm btn-outline-dark" title="Ver código de barras">
                                    <i class="fas fa-barcode"></i>
                                </a>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong class="text-primary"><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></strong>
                                <br><small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?></small>
                            </div>
                        </td>
                        <td>
                            <?php 
                            $statusLabels = [
                                'pending' => '<span class="badge badge-warning">Pendente</span>',
                                'approved' => '<span class="badge badge-success">Aprovado</span>',
                                'rejected' => '<span class="badge badge-danger">Rejeitado</span>',
                                'delivered' => '<span class="badge badge-info">Entregue</span>'
                            ];
                            echo $statusLabels[$request['status']];
                            ?>
                        </td>
                        <td><?php echo $request['item_count']; ?> itens</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="view_request.php?id=<?php echo $request['id']; ?>" class="btn btn-info" title="Ver Detalhes">
                                    👁️
                                </a>
                                <?php if ($request['status'] == 'pending'): ?>
                                <a href="edit_request.php?id=<?php echo $request['id']; ?>" class="btn btn-warning" title="Editar">
                                    ✏️
                                </a>
                                <?php endif; ?>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" title="Exportar">
                                        📄
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=excel">
                                            📊 Excel
                                        </a>
                                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=pdf">
                                            📄 PDF
                                        </a>
                                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=docx">
                                            📝 Word
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
                </div>
            </section>
        <?php endif; ?>

        <!-- Ações da Página -->
        <div class="text-center mt-4">
            <a href="request_form.php" class="btn btn-primary btn-lg">
                <i class="fas fa-plus" aria-hidden="true"></i>
                Nova Requisição
            </a>
        </div>
    </main>

    <footer class="mt-5 py-4 bg-light text-center" role="contentinfo">
        <div class="container">
            <p class="text-muted mb-0">
                <i class="fas fa-clipboard-list" aria-hidden="true"></i>
                Minhas Requisições - Sistema de Material de Cozinha
            </p>
            <small class="text-muted">
                Gerencie suas solicitações de forma eficiente
            </small>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Schema.org para página de requisições -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Minhas Requisições",
        "description": "Página para visualizar e gerenciar requisições pessoais de material de cozinha",
        "url": "<?php echo 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>",
        "mainEntity": {
            "@type": "ItemList",
            "name": "Lista de Requisições",
            "numberOfItems": <?php echo count($requests); ?>
        }
    }
    </script>
</body>
</html>