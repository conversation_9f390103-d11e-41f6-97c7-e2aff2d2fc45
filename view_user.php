<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

$userId = (int)($_GET['id'] ?? 0);
if ($userId <= 0) {
    header('Location: manage_users.php');
    exit;
}

// Buscar dados do usuário
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$userId]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: manage_users.php');
    exit;
}

// Buscar estatísticas do usuário
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_requests,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_requests
    FROM requests 
    WHERE user_id = ?
");
$stmt->execute([$userId]);
$stats = $stmt->fetch();

// Buscar últimas requisições do usuário
$stmt = $pdo->prepare("
    SELECT r.*, COUNT(ri.id) as item_count 
    FROM requests r 
    LEFT JOIN request_items ri ON r.id = ri.request_id 
    WHERE r.user_id = ? 
    GROUP BY r.id 
    ORDER BY r.request_date DESC 
    LIMIT 10
");
$stmt->execute([$userId]);
$recentRequests = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Detalhes do Usuário - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        .user-avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #6c757d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 32px;
            margin: 0 auto 20px;
        }
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h2>👤 Detalhes do Usuário</h2>
            </div>
            <div class="col-md-4 text-right">
                <a href="manage_users.php" class="btn btn-secondary">⬅ Voltar</a>
                <a href="manage_users.php?edit=<?php echo $user['id']; ?>" class="btn btn-warning">✏️ Editar</a>
            </div>
        </div>
        
        <!-- Informações do Usuário -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="user-avatar-large">
                            <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                        </div>
                        <h4><?php echo htmlspecialchars($user['username']); ?></h4>
                        <?php if ($user['role'] == 'admin'): ?>
                            <span class="badge badge-danger">👑 Administrador</span>
                        <?php else: ?>
                            <span class="badge badge-secondary">👤 Funcionário</span>
                        <?php endif; ?>
                        <br><br>
                        <span class="badge badge-success">✅ Ativo</span>
                    </div>
                    <div class="col-md-9">
                        <h5>Informações Gerais</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>ID:</strong></td>
                                <td>#<?php echo $user['id']; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Username:</strong></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Função:</strong></td>
                                <td>
                                    <?php if ($user['role'] == 'admin'): ?>
                                        👑 Administrador
                                    <?php else: ?>
                                        👤 Funcionário
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td><span class="badge badge-success">✅ Ativo</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-12">
                <h5>📊 Estatísticas de Requisições</h5>
            </div>
            <div class="col-md-2">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $stats['total_requests']; ?></h3>
                        <small>Total</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $stats['pending_requests']; ?></h3>
                        <small>Pendentes</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $stats['approved_requests']; ?></h3>
                        <small>Aprovadas</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card bg-danger text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $stats['rejected_requests']; ?></h3>
                        <small>Rejeitadas</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card bg-info text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $stats['delivered_requests']; ?></h3>
                        <small>Entregues</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $stats['total_requests'] > 0 ? round(($stats['delivered_requests'] / $stats['total_requests']) * 100) : 0; ?>%</h3>
                        <small>Taxa Entrega</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Últimas Requisições -->
        <div class="card">
            <div class="card-header">
                <h5>📋 Últimas Requisições (10 mais recentes)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentRequests)): ?>
                    <div class="text-center text-muted py-4">
                        📋 Este usuário ainda não fez nenhuma requisição
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Requisição</th>
                                    <th>Status</th>
                                    <th>Itens</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentRequests as $request): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong class="text-primary"><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></strong>
                                            <br><small class="text-muted">ID: #<?php echo $request['id']; ?> • <?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $statusLabels = [
                                            'pending' => '<span class="badge badge-warning">Pendente</span>',
                                            'approved' => '<span class="badge badge-success">Aprovada</span>',
                                            'rejected' => '<span class="badge badge-danger">Rejeitada</span>',
                                            'delivered' => '<span class="badge badge-info">Entregue</span>'
                                        ];
                                        echo $statusLabels[$request['status']];
                                        ?>
                                    </td>
                                    <td><?php echo $request['item_count']; ?> item(ns)</td>
                                    <td>
                                        <a href="view_request.php?id=<?php echo $request['id']; ?>" 
                                           class="btn btn-sm btn-info">👁️ Ver</a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="manage_requests.php?user=<?php echo $user['id']; ?>" class="btn btn-outline-primary">
                            📋 Ver Todas as Requisições deste Usuário
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
