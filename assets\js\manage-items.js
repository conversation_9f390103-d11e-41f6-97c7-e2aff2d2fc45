/**
 * JavaScript para Gerenciamento de Itens
 * Sistema de Requisição de Material de Cozinha
 * Seguindo padrões W3C para separação de tecnologias
 */

'use strict';

// ==========================================================================
// VARIÁVEIS GLOBAIS
// ==========================================================================

let nameCheckTimeout = null;
let isNameValid = true;
let isCheckingName = false;

// ==========================================================================
// CONFIGURAÇÕES
// ==========================================================================

const CONFIG = {
    AJAX_TIMEOUT: 800,
    MESSAGE_TIMEOUT: 3000,
    ALERT_TIMEOUT: 5000,
    MIN_NAME_LENGTH: 2
};

// ==========================================================================
// UTILITÁRIOS
// ==========================================================================

/**
 * Utilitário para log de debug
 */
const Logger = {
    error: (message, error) => {
        console.error(`❌ ${message}`, error);
    },
    warn: (message) => {
        console.warn(`⚠️ ${message}`);
    },
    info: (message) => {
        console.info(`ℹ️ ${message}`);
    },
    success: (message) => {
        console.log(`✅ ${message}`);
    }
};

/**
 * Utilitário para manipulação de DOM
 */
const DOM = {
    get: (selector) => {
        const element = document.querySelector(selector);
        if (!element) {
            Logger.warn(`Elemento não encontrado: ${selector}`);
        }
        return element;
    },
    
    getAll: (selector) => {
        return document.querySelectorAll(selector);
    },
    
    create: (tag, className = '', innerHTML = '') => {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    },
    
    exists: (selector) => {
        return document.querySelector(selector) !== null;
    }
};

// ==========================================================================
// FUNÇÕES DE CÓDIGO DE BARRAS
// ==========================================================================

/**
 * Exibir modal de código de barras
 */
function showBarcode(barcode, itemName) {
    try {
        const barcodeNameEl = DOM.get('#barcodeItemName');
        const barcodeDisplayEl = DOM.get('#barcodeDisplay');
        const barcodeQREl = DOM.get('#barcodeQR');
        
        if (!barcodeNameEl || !barcodeDisplayEl || !barcodeQREl) {
            throw new Error('Elementos do modal de código de barras não encontrados');
        }
        
        barcodeNameEl.textContent = itemName;
        barcodeDisplayEl.textContent = barcode;

        // Gerar QR Code
        const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(barcode)}`;
        barcodeQREl.innerHTML = `<img src="${qrUrl}" alt="QR Code" class="img-fluid" loading="lazy">`;

        $('#barcodeModal').modal('show');
        Logger.success('Modal de código de barras exibido');
    } catch (error) {
        Logger.error('Erro ao exibir código de barras', error);
        alert('Erro ao exibir código de barras');
    }
}

/**
 * Imprimir código de barras
 */
function printBarcode() {
    try {
        const barcodeNameEl = DOM.get('#barcodeItemName');
        const barcodeDisplayEl = DOM.get('#barcodeDisplay');
        const barcodeQREl = DOM.get('#barcodeQR');
        
        if (!barcodeNameEl || !barcodeDisplayEl || !barcodeQREl) {
            throw new Error('Elementos do modal não encontrados para impressão');
        }
        
        const itemName = barcodeNameEl.textContent;
        const barcode = barcodeDisplayEl.textContent;
        const qrImg = barcodeQREl.innerHTML;

        const printWindow = window.open('', '_blank');
        if (!printWindow) {
            alert('Não foi possível abrir janela de impressão. Verifique se pop-ups estão bloqueados.');
            return;
        }
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="pt-BR">
            <head>
                <meta charset="UTF-8">
                <title>Código de Barras - ${itemName}</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        text-align: center; 
                        padding: 20px; 
                        margin: 0;
                    }
                    .barcode { 
                        font-family: 'Courier New', monospace; 
                        font-size: 18px; 
                        letter-spacing: 3px; 
                        margin: 20px 0; 
                    }
                    .item-name { 
                        font-size: 16px; 
                        font-weight: bold; 
                        margin-bottom: 10px; 
                    }
                    .qr-code { 
                        margin: 20px 0; 
                    }
                    @media print {
                        body { padding: 10px; }
                    }
                </style>
            </head>
            <body>
                <div class="item-name">${itemName}</div>
                <div class="barcode">${barcode}</div>
                <div class="qr-code">${qrImg}</div>
                <script>
                    window.onload = function() {
                        window.print();
                        setTimeout(function() {
                            window.close();
                        }, 1000);
                    };
                </script>
            </body>
            </html>
        `);
        printWindow.document.close();
        Logger.success('Janela de impressão aberta');
    } catch (error) {
        Logger.error('Erro ao imprimir código de barras', error);
        alert('Erro ao imprimir código de barras');
    }
}

// ==========================================================================
// FUNÇÕES DE FORMULÁRIO
// ==========================================================================

/**
 * Limpar formulário
 */
function clearForm() {
    if (!confirm('Tem certeza que deseja limpar todos os campos do formulário?')) {
        return;
    }
    
    try {
        // Limpar todos os campos de input
        DOM.getAll('input[type="text"], input[type="number"], textarea, select').forEach(field => {
            if (!field.readOnly && !field.disabled) {
                field.value = '';
            }
        });

        // Resetar select de unidade para primeira opção
        const unitSelect = DOM.get('select[name="unit"]');
        if (unitSelect) {
            unitSelect.selectedIndex = 0;
        }

        // Limpar validações
        clearNameValidation();
        clearNameSuggestions();

        // Focar no primeiro campo
        const firstField = DOM.get('input[name="name"]');
        if (firstField) {
            firstField.focus();
        }

        // Mostrar mensagem de confirmação
        showTemporaryMessage('Campos limpos com sucesso!', 'success');
        Logger.success('Formulário limpo');
    } catch (error) {
        Logger.error('Erro ao limpar formulário', error);
        alert('Erro ao limpar formulário');
    }
}

/**
 * Validar formulário antes do envio
 */
function validateForm() {
    try {
        const nameInput = DOM.get('input[name="name"]');
        const unitSelect = DOM.get('select[name="unit"]');

        if (!nameInput || !unitSelect) {
            Logger.error('Campos obrigatórios não encontrados');
            return false;
        }

        const name = nameInput.value.trim();
        const unit = unitSelect.value;

        if (!name) {
            alert('Por favor, preencha o nome do item.');
            nameInput.focus();
            return false;
        }

        if (!unit) {
            alert('Por favor, selecione uma unidade.');
            unitSelect.focus();
            return false;
        }

        if (isCheckingName) {
            alert('Aguarde a verificação do nome terminar.');
            return false;
        }

        if (!isNameValid) {
            alert('O nome do item já existe. Por favor, escolha um nome diferente ou use uma das sugestões.');
            nameInput.focus();
            return false;
        }

        Logger.success('Formulário validado com sucesso');
        return true;
    } catch (error) {
        Logger.error('Erro na validação do formulário', error);
        return false;
    }
}

// ==========================================================================
// FUNÇÕES DE MENSAGENS
// ==========================================================================

/**
 * Exibir mensagem temporária
 */
function showTemporaryMessage(message, type = 'info') {
    try {
        const alertDiv = DOM.create('div', `alert alert-${type} alert-dismissible fade show`);
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Fechar">
                <span aria-hidden="true">&times;</span>
            </button>
        `;

        // Inserir no topo da página
        const container = DOM.get('.container');
        if (container && container.firstElementChild) {
            const firstChild = container.firstElementChild;
            container.insertBefore(alertDiv, firstChild.nextSibling);
        } else {
            Logger.error('Container não encontrado para exibir mensagem');
            return;
        }

        // Auto-remover após timeout
        setTimeout(() => {
            if (alertDiv && alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, CONFIG.MESSAGE_TIMEOUT);
        
        Logger.success('Mensagem temporária exibida');
    } catch (error) {
        Logger.error('Erro ao exibir mensagem temporária', error);
    }
}

// ==========================================================================
// FUNÇÕES DE VALIDAÇÃO DE NOME
// ==========================================================================

/**
 * Verificar nome duplicado via AJAX
 */
function checkDuplicateName(name, excludeId = 0) {
    if (name.length < CONFIG.MIN_NAME_LENGTH) {
        clearNameValidation();
        return;
    }

    isCheckingName = true;
    showNameValidation('Verificando...', 'info');

    const formData = new FormData();
    formData.append('name', name);
    if (excludeId > 0) {
        formData.append('exclude_id', excludeId);
    }

    fetch('check_duplicate_item.php', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        isCheckingName = false;

        if (data.error) {
            showNameValidation('Erro na verificação', 'danger');
            isNameValid = false;
        } else if (data.exists) {
            const message = `Nome já existe! Item: "${data.item.name}" (${data.item.internal_code})`;
            showNameValidation(message, 'danger');
            isNameValid = false;

            // Mostrar sugestões se disponíveis
            if (data.suggestion && data.suggestion.length > 0) {
                showNameSuggestions(data.suggestion);
            }
        } else {
            showNameValidation('Nome disponível ✓', 'success');
            isNameValid = true;
            clearNameSuggestions();
        }
    })
    .catch(error => {
        isCheckingName = false;
        Logger.error('Erro na verificação AJAX', error);
        showNameValidation('Erro na verificação - continuando...', 'warning');
        isNameValid = true; // Permitir continuar em caso de erro
    });
}

/**
 * Mostrar status da validação do nome
 */
function showNameValidation(message, type) {
    try {
        let validationDiv = DOM.get('#name-validation');

        if (!validationDiv) {
            validationDiv = DOM.create('div', 'mt-1');
            validationDiv.id = 'name-validation';

            const nameInput = DOM.get('input[name="name"]');
            if (nameInput && nameInput.parentNode) {
                nameInput.parentNode.appendChild(validationDiv);
            } else {
                Logger.error('Campo de nome não encontrado para validação');
                return;
            }
        }

        const iconMap = {
            'info': 'fas fa-spinner fa-spin',
            'success': 'fas fa-check-circle',
            'danger': 'fas fa-exclamation-triangle',
            'warning': 'fas fa-exclamation-circle'
        };

        validationDiv.innerHTML = `
            <small class="text-${type}">
                <i class="${iconMap[type] || 'fas fa-info-circle'}"></i>
                ${message}
            </small>
        `;
    } catch (error) {
        Logger.error('Erro ao exibir validação do nome', error);
    }
}

/**
 * Limpar validação do nome
 */
function clearNameValidation() {
    try {
        const validationDiv = DOM.get('#name-validation');
        if (validationDiv) {
            validationDiv.innerHTML = '';
        }
        isNameValid = true;
    } catch (error) {
        Logger.error('Erro ao limpar validação', error);
    }
}

/**
 * Mostrar sugestões de nomes alternativos
 */
function showNameSuggestions(suggestions) {
    try {
        if (!Array.isArray(suggestions) || suggestions.length === 0) {
            return;
        }

        let suggestionsDiv = DOM.get('#name-suggestions');

        if (!suggestionsDiv) {
            suggestionsDiv = DOM.create('div', 'mt-2');
            suggestionsDiv.id = 'name-suggestions';

            const nameInput = DOM.get('input[name="name"]');
            if (nameInput && nameInput.parentNode) {
                nameInput.parentNode.appendChild(suggestionsDiv);
            } else {
                Logger.error('Campo de nome não encontrado para sugestões');
                return;
            }
        }

        let html = '<small class="text-muted"><strong>Sugestões:</strong></small><br>';
        suggestions.forEach(suggestion => {
            const escapedSuggestion = suggestion.replace(/'/g, "\\'").replace(/"/g, '\\"');
            html += `
                <button type="button" class="btn btn-sm btn-outline-primary mr-1 mb-1 suggestion-btn"
                        onclick="applySuggestion('${escapedSuggestion}')">
                    ${suggestion}
                </button>
            `;
        });

        suggestionsDiv.innerHTML = html;
        Logger.success('Sugestões de nome exibidas');
    } catch (error) {
        Logger.error('Erro ao exibir sugestões', error);
    }
}

/**
 * Limpar sugestões
 */
function clearNameSuggestions() {
    try {
        const suggestionsDiv = DOM.get('#name-suggestions');
        if (suggestionsDiv) {
            suggestionsDiv.innerHTML = '';
        }
    } catch (error) {
        Logger.error('Erro ao limpar sugestões', error);
    }
}

/**
 * Aplicar sugestão de nome
 */
function applySuggestion(suggestion) {
    try {
        const nameInput = DOM.get('input[name="name"]');
        if (!nameInput) {
            Logger.error('Campo de nome não encontrado');
            return;
        }

        nameInput.value = suggestion;
        nameInput.focus();

        // Verificar o novo nome
        clearTimeout(nameCheckTimeout);
        nameCheckTimeout = setTimeout(() => {
            const excludeIdInput = DOM.get('input[name="id"]');
            const excludeId = excludeIdInput ? excludeIdInput.value : 0;
            checkDuplicateName(suggestion, excludeId);
        }, 500);

        clearNameSuggestions();
        Logger.success('Sugestão aplicada');
    } catch (error) {
        Logger.error('Erro ao aplicar sugestão', error);
    }
}

// ==========================================================================
// INICIALIZAÇÃO E EVENT LISTENERS
// ==========================================================================

/**
 * Configurar event listeners para campos obrigatórios
 */
function setupRequiredFieldsHighlight() {
    DOM.getAll('input[required], select[required]').forEach(field => {
        field.addEventListener('blur', function() {
            try {
                if (!this.value.trim()) {
                    this.classList.add('border-warning');
                } else {
                    this.classList.remove('border-warning');
                }
            } catch (error) {
                Logger.error('Erro no evento blur', error);
            }
        });
    });
}

/**
 * Configurar validação em tempo real para o campo nome
 */
function setupNameValidation() {
    const nameInput = DOM.get('input[name="name"]');
    if (!nameInput) {
        Logger.warn('Campo de nome não encontrado para validação');
        return;
    }

    // Verificar ao digitar (com delay)
    nameInput.addEventListener('input', function() {
        try {
            const name = this.value.trim();
            const excludeIdInput = DOM.get('input[name="id"]');
            const excludeId = excludeIdInput ? excludeIdInput.value : 0;

            clearTimeout(nameCheckTimeout);
            clearNameSuggestions();

            if (name.length >= CONFIG.MIN_NAME_LENGTH) {
                nameCheckTimeout = setTimeout(() => {
                    checkDuplicateName(name, excludeId);
                }, CONFIG.AJAX_TIMEOUT);
            } else {
                clearNameValidation();
            }
        } catch (error) {
            Logger.error('Erro no evento input', error);
        }
    });

    // Verificar ao sair do campo
    nameInput.addEventListener('blur', function() {
        try {
            const name = this.value.trim();
            const excludeIdInput = DOM.get('input[name="id"]');
            const excludeId = excludeIdInput ? excludeIdInput.value : 0;

            if (name.length >= CONFIG.MIN_NAME_LENGTH) {
                clearTimeout(nameCheckTimeout);
                checkDuplicateName(name, excludeId);
            }
        } catch (error) {
            Logger.error('Erro no evento blur do nome', error);
        }
    });

    // Limpar validação ao focar
    nameInput.addEventListener('focus', function() {
        try {
            this.classList.remove('border-warning', 'border-danger');
        } catch (error) {
            Logger.error('Erro no evento focus', error);
        }
    });
}

/**
 * Configurar atalhos de teclado
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        try {
            // Ctrl + N = Novo item
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                window.location.href = 'manage_items.php';
            }

            // Ctrl + L = Limpar campos (apenas se não estiver editando)
            if (e.ctrlKey && e.key === 'l' && !DOM.exists('input[name="id"]')) {
                e.preventDefault();
                clearForm();
            }
        } catch (error) {
            Logger.error('Erro nos atalhos de teclado', error);
        }
    });
}

/**
 * Auto-remover alertas após timeout
 */
function setupAutoRemoveAlerts() {
    setTimeout(() => {
        try {
            DOM.getAll('.alert-success, .alert-info').forEach(alert => {
                const closeBtn = alert.querySelector('.close');
                if (closeBtn) {
                    closeBtn.click();
                }
            });
        } catch (error) {
            Logger.error('Erro ao remover alertas', error);
        }
    }, CONFIG.ALERT_TIMEOUT);
}

/**
 * Configurar validação do formulário
 */
function setupFormValidation() {
    const form = DOM.get('form[method="post"]');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
            }
        });
    } else {
        Logger.warn('Formulário não encontrado');
    }
}

/**
 * Verificar dependências carregadas
 */
function checkDependencies() {
    // Verificar se jQuery está carregado
    if (typeof $ === 'undefined') {
        Logger.error('jQuery não foi carregado corretamente');
    } else {
        Logger.success('jQuery carregado');
    }

    // Verificar se Bootstrap está carregado
    if (typeof bootstrap === 'undefined' && (typeof $ === 'undefined' || !$.fn.modal)) {
        Logger.warn('Bootstrap JavaScript pode não estar carregado corretamente');
    } else {
        Logger.success('Bootstrap carregado');
    }
}

/**
 * Inicialização principal
 */
function initializeManageItems() {
    try {
        Logger.info('Inicializando sistema de gerenciamento de itens...');

        // Verificar dependências
        checkDependencies();

        // Configurar event listeners
        setupRequiredFieldsHighlight();
        setupNameValidation();
        setupKeyboardShortcuts();
        setupFormValidation();
        setupAutoRemoveAlerts();

        // Auto-focus no campo nome se não estiver editando
        if (!DOM.exists('input[name="id"]')) {
            const nameField = DOM.get('input[name="name"]');
            if (nameField) {
                nameField.focus();
            }
        }

        Logger.success('Sistema inicializado com sucesso');
    } catch (error) {
        Logger.error('Erro na inicialização', error);
    }
}

// ==========================================================================
// EXPORTAR FUNÇÕES GLOBAIS (para compatibilidade com onclick)
// ==========================================================================

// Tornar funções disponíveis globalmente para uso em onclick
window.showBarcode = showBarcode;
window.printBarcode = printBarcode;
window.clearForm = clearForm;
window.validateForm = validateForm;
window.applySuggestion = applySuggestion;

// ==========================================================================
// INICIALIZAÇÃO AUTOMÁTICA
// ==========================================================================

// Inicializar quando o DOM estiver pronto
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeManageItems);
} else {
    initializeManageItems();
}

// Inicializar também quando jQuery estiver pronto (para compatibilidade)
if (typeof $ !== 'undefined') {
    $(document).ready(initializeManageItems);
}
