<?php
// Debug do sistema de layout
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Debug do Sistema de Layout</h1>";

// Teste 1: Sessão
echo "<h2>1. Teste de Sessão</h2>";
session_start();
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'debug_user';
    $_SESSION['role'] = 'admin';
    echo "✅ Sessão criada para teste<br>";
} else {
    echo "✅ Sessão já existe<br>";
}
echo "User ID: " . $_SESSION['user_id'] . "<br>";
echo "Username: " . $_SESSION['username'] . "<br>";
echo "Role: " . $_SESSION['role'] . "<br><br>";

// Teste 2: Arquivos
echo "<h2>2. Teste de Arquivos</h2>";
$files_to_check = [
    'includes/page_config.php',
    'includes/layout.php',
    'includes/layout_footer.php',
    'config/db_connect.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file existe<br>";
    } else {
        echo "❌ $file NÃO existe<br>";
    }
}
echo "<br>";

// Teste 3: Incluir page_config
echo "<h2>3. Teste de Inclusão - page_config.php</h2>";
try {
    require_once 'includes/page_config.php';
    echo "✅ page_config.php incluído com sucesso<br>";
    
    // Verificar funções
    if (function_exists('initPage')) {
        echo "✅ Função initPage() existe<br>";
    } else {
        echo "❌ Função initPage() NÃO existe<br>";
    }
    
    if (function_exists('isActivePage')) {
        echo "✅ Função isActivePage() existe<br>";
    } else {
        echo "❌ Função isActivePage() NÃO existe<br>";
    }
    
    if (function_exists('isAdmin')) {
        echo "✅ Função isAdmin() existe<br>";
    } else {
        echo "❌ Função isAdmin() NÃO existe<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erro ao incluir page_config.php: " . $e->getMessage() . "<br>";
}
echo "<br>";

// Teste 4: Configuração da página
echo "<h2>4. Teste de Configuração</h2>";
try {
    initPage([
        'page_title' => 'Debug Page',
        'page_subtitle' => 'Página de debug do sistema',
    ]);
    echo "✅ initPage() executado com sucesso<br>";
    
    // Verificar variáveis globais
    global $page_title, $page_subtitle;
    echo "Page Title: " . ($page_title ?? 'NÃO DEFINIDO') . "<br>";
    echo "Page Subtitle: " . ($page_subtitle ?? 'NÃO DEFINIDO') . "<br>";
    
} catch (Exception $e) {
    echo "❌ Erro ao executar initPage(): " . $e->getMessage() . "<br>";
}
echo "<br>";

// Teste 5: Layout básico
echo "<h2>5. Teste de Layout Básico</h2>";
try {
    // Definir variáveis necessárias se não existirem
    if (!isset($page_title)) $page_title = 'Debug Page';
    if (!isset($page_subtitle)) $page_subtitle = 'Página de debug';
    if (!isset($page_header)) $page_header = true;
    
    echo "✅ Variáveis de layout definidas<br>";
    echo "Tentando incluir layout.php...<br>";
    
    // Incluir layout
    require_once 'includes/layout.php';
    echo "✅ Layout incluído com sucesso<br>";
    
} catch (Exception $e) {
    echo "❌ Erro ao incluir layout: " . $e->getMessage() . "<br>";
}
?>

<!-- Conteúdo de teste -->
<div class="content-card" style="margin: 20px; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <h2>🎉 Layout Carregado com Sucesso!</h2>
    <p>Se você está vendo este conteúdo com o layout aplicado, significa que o sistema está funcionando.</p>
    
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>
        <strong>Sucesso!</strong> O sistema de layout padronizado está operacional.
    </div>
    
    <h3>📱 Teste de Responsividade:</h3>
    <ol>
        <li>Redimensione a janela para simular mobile</li>
        <li>Clique no botão ☰ para abrir a sidebar</li>
        <li>Teste o overlay clicando fora da sidebar</li>
        <li>Pressione ESC para fechar</li>
    </ol>
    
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-desktop fa-2x mb-2"></i>
                    <h5>Desktop</h5>
                    <p class="mb-0">Layout completo</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-tablet-alt fa-2x mb-2"></i>
                    <h5>Tablet</h5>
                    <p class="mb-0">Layout adaptado</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                    <h5>Mobile</h5>
                    <p class="mb-0">Sidebar overlay</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Incluir footer
try {
    require_once 'includes/layout_footer.php';
    echo "<!-- ✅ Footer incluído com sucesso -->";
} catch (Exception $e) {
    echo "<!-- ❌ Erro ao incluir footer: " . $e->getMessage() . " -->";
}
?>
