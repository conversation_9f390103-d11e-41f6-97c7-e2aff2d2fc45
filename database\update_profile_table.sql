-- Atualização da tabela users para suporte a perfil completo
-- Execute este script para adicionar as colunas necessárias

-- Adicionar colunas de perfil à tabela users
ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar VARCHAR(255) DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS full_name VA<PERSON>HAR(255) DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS email VARCHAR(255) DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20) DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS department VARCHAR(100) DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS position VARCHAR(100) DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS bio TEXT DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS birth_date DATE DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS hire_date DATE DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP DEFAULT NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS preferences JSON DEFAULT NULL;

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_department ON users(department);
CREATE INDEX IF NOT EXISTS idx_users_full_name ON users(full_name);

-- Atualizar registros existentes com dados padrão
UPDATE users SET 
    full_name = COALESCE(full_name, username),
    email = COALESCE(email, CONCAT(username, '@empresa.com')),
    department = COALESCE(department, 'Não informado'),
    position = COALESCE(position, 'Funcionário'),
    profile_updated_at = CURRENT_TIMESTAMP
WHERE full_name IS NULL OR email IS NULL;

-- Criar tabela para log de uploads de avatar
CREATE TABLE IF NOT EXISTS avatar_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_avatar_user_id (user_id),
    INDEX idx_avatar_active (is_active)
);

-- Comentários para documentação
ALTER TABLE users 
    MODIFY COLUMN avatar VARCHAR(255) COMMENT 'Caminho para o arquivo de avatar do usuário',
    MODIFY COLUMN full_name VARCHAR(255) COMMENT 'Nome completo do usuário',
    MODIFY COLUMN email VARCHAR(255) COMMENT 'Email do usuário',
    MODIFY COLUMN phone VARCHAR(20) COMMENT 'Telefone do usuário',
    MODIFY COLUMN department VARCHAR(100) COMMENT 'Departamento do usuário',
    MODIFY COLUMN position VARCHAR(100) COMMENT 'Cargo do usuário',
    MODIFY COLUMN bio TEXT COMMENT 'Biografia/descrição do usuário',
    MODIFY COLUMN birth_date DATE COMMENT 'Data de nascimento',
    MODIFY COLUMN hire_date DATE COMMENT 'Data de contratação',
    MODIFY COLUMN last_login TIMESTAMP COMMENT 'Último login do usuário',
    MODIFY COLUMN preferences JSON COMMENT 'Preferências do usuário em formato JSON';

-- Verificar se as alterações foram aplicadas
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users' 
    AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
