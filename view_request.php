<?php
// Iniciar sessão se não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Incluir configuração de página
require_once 'includes/page_config.php';

// Incluir conexão com banco
require_once 'config/db_connect.php';

if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$requestId = (int)$_GET['id'];

// Verificar se o usuário tem permissão para ver esta requisição
$stmt = $pdo->prepare("
    SELECT r.*, u.username 
    FROM requests r 
    JOIN users u ON r.user_id = u.id 
    WHERE r.id = ?
");
$stmt->execute([$requestId]);
$request = $stmt->fetch();

if (!$request || ($request['user_id'] != $_SESSION['user_id'] && $_SESSION['role'] != 'admin')) {
    header('Location: index.php');
    exit;
}

// Buscar itens da requisição
$stmt = $pdo->prepare("
    SELECT ri.*, i.name, i.description, i.unit
    FROM request_items ri
    JOIN items i ON ri.item_id = i.id
    WHERE ri.request_id = ?
");
$stmt->execute([$requestId]);
$items = $stmt->fetchAll();

// Aplicar configuração automática da página
initPage([
    'page_title' => htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']),
    'page_subtitle' => 'Detalhes da Requisição #' . $request['id'],
    'page_icon' => 'fas fa-file-alt'
]);

// Incluir layout
require_once 'includes/layout.php';
?>
<!-- Cabeçalho da Página -->
<div class="content-card">
    <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
        <div>
            <h3 class="mb-1"><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></h3>
            <p class="text-muted mb-0">Detalhes da Requisição #<?php echo $request['id']; ?></p>
        </div>
        <div class="d-flex gap-2 flex-wrap">
            <a href="<?php echo $_SESSION['role'] == 'admin' ? 'manage_requests.php' : 'my_requests.php'; ?>"
               class="btn btn-secondary btn-custom">
                <i class="fas fa-arrow-left me-2"></i>Voltar
            </a>

            <?php if ($request['status'] == 'pending' && ($_SESSION['role'] == 'admin' || $request['user_id'] == $_SESSION['user_id'])): ?>
            <a href="edit_request.php?id=<?php echo $request['id']; ?>" class="btn btn-warning btn-custom">
                <i class="fas fa-edit me-2"></i>Editar
            </a>
            <?php endif; ?>

            <div class="btn-group" role="group">
                <button type="button" class="btn btn-success btn-custom dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-2"></i>Exportar
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=excel">
                        <i class="fas fa-file-excel text-success me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=pdf">
                        <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                    </a></li>
                    <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=docx">
                        <i class="fas fa-file-word text-primary me-2"></i>Word
                    </a></li>
                </ul>
            </div>

            <a href="view_barcode.php?type=request&id=<?php echo $request['id']; ?>" class="btn btn-dark btn-custom">
                <i class="fas fa-barcode me-2"></i>Código de Barras
            </a>
        </div>
    </div>
</div>

<!-- Informações da Requisição -->
<div class="content-card">
    <h5 class="mb-4">
        <i class="fas fa-info-circle text-primary me-2"></i>
        Informações Gerais
    </h5>

    <div class="row g-4">
        <div class="col-md-6">
            <div class="info-item">
                <label class="form-label fw-bold text-muted">ID da Requisição</label>
                <p class="mb-0 fs-5">#<?php echo $request['id']; ?></p>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <label class="form-label fw-bold text-muted">Solicitante</label>
                <p class="mb-0 fs-5">
                    <i class="fas fa-user text-primary me-2"></i>
                    <?php echo htmlspecialchars($request['username']); ?>
                </p>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <label class="form-label fw-bold text-muted">Data da Requisição</label>
                <p class="mb-0 fs-5">
                    <i class="fas fa-calendar text-info me-2"></i>
                    <?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?>
                </p>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <label class="form-label fw-bold text-muted">Status</label>
                <p class="mb-0">
                    <?php
                    $statusLabels = [
                        'pending' => 'Pendente',
                        'approved' => 'Aprovada',
                        'rejected' => 'Rejeitada',
                        'delivered' => 'Entregue'
                    ];
                    $statusColors = [
                        'pending' => 'bg-warning text-dark',
                        'approved' => 'bg-success',
                        'rejected' => 'bg-danger',
                        'delivered' => 'bg-info'
                    ];
                    ?>
                    <span class="badge <?php echo $statusColors[$request['status']]; ?> fs-6">
                        <?php echo $statusLabels[$request['status']]; ?>
                    </span>
                </p>
            </div>
        </div>
    </div>

    <!-- Informações de Códigos -->
    <?php
    // Incluir biblioteca de códigos de barras
    require_once 'includes/barcode_generator.php';

    // Gerar códigos se não existirem
    $internalCode = $request['internal_code'] ?? 'REQ' . str_pad($request['id'], 6, '0', STR_PAD_LEFT);
    $barcode = $request['barcode'] ?? generateRequestBarcode($request['id']);
    ?>
    <div class="row g-3 mt-3">
        <div class="col-md-6">
            <div class="bg-light p-3 rounded">
                <h6 class="mb-2">
                    <i class="fas fa-hashtag text-primary me-2"></i>
                    Código Interno
                </h6>
                <p class="mb-2">
                    <span class="h5 text-primary font-monospace"><?php echo htmlspecialchars($internalCode); ?></span>
                </p>
                <small class="text-muted">Código único para identificação interna</small>
            </div>
        </div>
        <div class="col-md-6">
            <div class="bg-light p-3 rounded">
                <h6 class="mb-2">
                    <i class="fas fa-barcode text-dark me-2"></i>
                    Código de Barras
                </h6>
                <p class="mb-2">
                    <span class="h6 font-monospace"><?php echo htmlspecialchars($barcode); ?></span>
                </p>
                <small class="text-muted">
                    <a href="view_barcode.php?type=request&id=<?php echo $request['id']; ?>" class="text-decoration-none">
                        <i class="fas fa-external-link-alt me-1"></i>Ver código completo
                    </a>
                </small>
            </div>
        </div>
    </div>

    <!-- Campos Adicionais (se existirem) -->
    <?php if (!empty($request['priority']) || !empty($request['department']) || !empty($request['notes'])): ?>
    <div class="row g-3 mt-3">
        <?php if (!empty($request['priority'])): ?>
        <div class="col-md-4">
            <div class="info-item">
                <label class="form-label fw-bold text-muted">Prioridade</label>
                <p class="mb-0">
                    <?php
                    $priorityLabels = [
                        'low' => '🟢 Baixa',
                        'medium' => '🟡 Média',
                        'high' => '🟠 Alta',
                        'urgent' => '🔴 Urgente'
                    ];
                    $priorityColors = [
                        'low' => 'bg-success',
                        'medium' => 'bg-warning text-dark',
                        'high' => 'bg-warning text-dark',
                        'urgent' => 'bg-danger'
                    ];
                    ?>
                    <span class="badge <?php echo $priorityColors[$request['priority']] ?? 'bg-secondary'; ?> fs-6">
                        <?php echo $priorityLabels[$request['priority']] ?? ucfirst($request['priority']); ?>
                    </span>
                </p>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($request['department'])): ?>
        <div class="col-md-4">
            <div class="info-item">
                <label class="form-label fw-bold text-muted">Departamento</label>
                <p class="mb-0">
                    <i class="fas fa-building text-info me-2"></i>
                    <?php echo htmlspecialchars($request['department']); ?>
                </p>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($request['notes'])): ?>
        <div class="col-md-4">
            <div class="info-item">
                <label class="form-label fw-bold text-muted">Observações</label>
                <p class="mb-0 text-muted"><?php echo nl2br(htmlspecialchars($request['notes'])); ?></p>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<!-- Itens Solicitados -->
<div class="content-card">
    <h5 class="mb-4">
        <i class="fas fa-list text-success me-2"></i>
        Itens Solicitados
    </h5>

    <?php if (empty($items)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Nenhum item encontrado para esta requisição.
        </div>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Item</th>
                        <th>Descrição</th>
                        <th>Quantidade</th>
                        <th>Unidade</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($items as $item): ?>
                    <tr>
                        <td class="fw-bold"><?php echo htmlspecialchars($item['name']); ?></td>
                        <td class="text-muted"><?php echo htmlspecialchars($item['description']); ?></td>
                        <td>
                            <span class="badge bg-primary fs-6"><?php echo $item['quantity']; ?></span>
                        </td>
                        <td>
                            <span class="badge bg-secondary"><?php echo htmlspecialchars($item['unit']); ?></span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="alert alert-light border">
            <i class="fas fa-info-circle text-primary me-2"></i>
            <strong>Total de itens diferentes:</strong> <?php echo count($items); ?>
        </div>
    <?php endif; ?>
</div>

<!-- Ações para Admin -->
<?php if ($_SESSION['role'] == 'admin' && $request['status'] == 'pending'): ?>
<div class="content-card">
    <h5 class="mb-4">
        <i class="fas fa-cogs text-warning me-2"></i>
        Ações Administrativas
    </h5>

    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
        <button type="button" class="btn btn-success btn-lg btn-custom"
                onclick="confirmApproval(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo count($items); ?>)">
            <i class="fas fa-check-circle me-2"></i>Aprovar Requisição
        </button>
        <button type="button" class="btn btn-danger btn-lg btn-custom"
                onclick="confirmRejection(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo count($items); ?>)">
            <i class="fas fa-times-circle me-2"></i>Rejeitar Requisição
        </button>
    </div>
</div>
<?php elseif ($_SESSION['role'] == 'admin' && $request['status'] == 'approved'): ?>
<div class="content-card">
    <h5 class="mb-4">
        <i class="fas fa-cogs text-warning me-2"></i>
        Ações Administrativas
    </h5>

    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
        <button type="button" class="btn btn-primary btn-lg btn-custom"
                onclick="confirmDelivery(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo count($items); ?>)">
            <i class="fas fa-truck me-2"></i>Marcar como Entregue
        </button>
    </div>
</div>
<?php endif; ?>

<!-- Modal de Confirmação de Aprovação -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    Confirmar Aprovação
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Atenção:</strong> Esta ação aprovará a requisição e permitirá que os itens sejam preparados para entrega.
                </div>

                <h6><strong>Detalhes da Requisição:</strong></h6>
                <ul class="list-unstyled">
                    <li><strong>Nome:</strong> <span id="approval-request-name"></span></li>
                    <li><strong>Solicitante:</strong> <span id="approval-username"></span></li>
                    <li><strong>Total de Itens:</strong> <span id="approval-item-count"></span></li>
                    <li><strong>Data da Solicitação:</strong> <?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?></li>
                </ul>

                <div class="mb-3">
                    <label for="approval-notes" class="form-label">
                        <i class="fas fa-sticky-note me-2"></i>
                        Observações da Aprovação (opcional)
                    </label>
                    <textarea id="approval-notes" class="form-control" rows="3"
                              placeholder="Adicione observações sobre a aprovação, instruções especiais, etc..."></textarea>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Importante:</strong> Após a aprovação, a requisição ficará disponível para entrega e não poderá mais ser editada.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancelar
                </button>
                <button type="button" class="btn btn-success btn-lg" onclick="executeApproval()">
                    <i class="fas fa-check-circle me-2"></i>Confirmar Aprovação
                </button>
            </div>
        </div>
    </div>
</div>

<?php
// Scripts específicos da página
$page_scripts = '
<style>
.info-item {
    padding: 0.75rem;
    border-left: 3px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}
.font-monospace {
    font-family: "Courier New", monospace;
}
</style>
<script>
function confirmApproval(requestId, requestName, username, itemCount) {
    if (confirm("Tem certeza que deseja aprovar esta requisição?")) {
        window.location.href = "manage_requests.php?action=approve&id=" + requestId;
    }
}

function confirmRejection(requestId, requestName, username, itemCount) {
    const reason = prompt("Motivo da rejeição:");
    if (reason) {
        window.location.href = "manage_requests.php?action=reject&id=" + requestId + "&reason=" + encodeURIComponent(reason);
    }
}

function confirmDelivery(requestId, requestName, username, itemCount) {
    if (confirm("Tem certeza que deseja marcar esta requisição como entregue?")) {
        window.location.href = "manage_requests.php?action=deliver&id=" + requestId;
    }
}
</script>
';

// Incluir footer
require_once 'includes/layout_footer.php';
?>
