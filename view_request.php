<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$requestId = (int)$_GET['id'];

// Verificar se o usuário tem permissão para ver esta requisição
$stmt = $pdo->prepare("
    SELECT r.*, u.username 
    FROM requests r 
    JOIN users u ON r.user_id = u.id 
    WHERE r.id = ?
");
$stmt->execute([$requestId]);
$request = $stmt->fetch();

if (!$request || ($request['user_id'] != $_SESSION['user_id'] && $_SESSION['role'] != 'admin')) {
    header('Location: index.php');
    exit;
}

// Buscar itens da requisição
$stmt = $pdo->prepare("
    SELECT ri.*, i.name, i.description, i.unit 
    FROM request_items ri 
    JOIN items i ON ri.item_id = i.id 
    WHERE ri.request_id = ?
");
$stmt->execute([$requestId]);
$items = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html>
<head>
    <title><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?> - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h2><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></h2>
                <small class="text-muted">Detalhes da Requisição #<?php echo $request['id']; ?></small>
            </div>
            <div class="col-md-4 text-right">
                <a href="<?php echo $_SESSION['role'] == 'admin' ? 'manage_requests.php' : 'my_requests.php'; ?>"
                   class="btn btn-secondary">⬅ Voltar</a>

                <?php if ($request['status'] == 'pending' && ($_SESSION['role'] == 'admin' || $request['user_id'] == $_SESSION['user_id'])): ?>
                <a href="edit_request.php?id=<?php echo $request['id']; ?>" class="btn btn-warning">
                    ✏️ Editar
                </a>
                <?php endif; ?>

                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
                        📄 Exportar
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=excel">
                            📊 Excel
                        </a>
                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=pdf">
                            📄 PDF
                        </a>
                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=docx">
                            📝 Word
                        </a>
                    </div>
                </div>

                <a href="view_barcode.php?type=request&id=<?php echo $request['id']; ?>" class="btn btn-dark">
                    <i class="fas fa-barcode"></i> Código de Barras
                </a>
            </div>
        </div>

        <!-- Informações da Requisição -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Informações Gerais</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <strong>Nome da Requisição:</strong><br>
                        <h4 class="text-primary"><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <strong>ID da Requisição:</strong><br>
                        #<?php echo $request['id']; ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Solicitante:</strong><br>
                        <?php echo htmlspecialchars($request['username']); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Data da Requisição:</strong><br>
                        <?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Status:</strong><br>
                        <?php
                        $statusLabels = [
                            'pending' => 'Pendente',
                            'approved' => 'Aprovada',
                            'rejected' => 'Rejeitada',
                            'delivered' => 'Entregue'
                        ];
                        $statusColors = [
                            'pending' => 'warning',
                            'approved' => 'success',
                            'rejected' => 'danger',
                            'delivered' => 'info'
                        ];
                        ?>
                        <span class="badge badge-<?php echo $statusColors[$request['status']]; ?>">
                            <?php echo $statusLabels[$request['status']]; ?>
                        </span>
                    </div>
                </div>

                <!-- Informações de Códigos -->
                <?php
                // Incluir biblioteca de códigos de barras
                require_once 'includes/barcode_generator.php';

                // Gerar códigos se não existirem
                $internalCode = $request['internal_code'] ?? 'REQ' . str_pad($request['id'], 6, '0', STR_PAD_LEFT);
                $barcode = $request['barcode'] ?? generateRequestBarcode($request['id']);
                ?>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-hashtag text-primary"></i>
                                    Código Interno
                                </h6>
                                <p class="card-text">
                                    <span class="h5 text-primary font-monospace"><?php echo htmlspecialchars($internalCode); ?></span>
                                </p>
                                <small class="text-muted">Código único para identificação interna</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-barcode text-dark"></i>
                                    Código de Barras
                                </h6>
                                <p class="card-text">
                                    <span class="h6 font-monospace"><?php echo htmlspecialchars($barcode); ?></span>
                                </p>
                                <small class="text-muted">
                                    <a href="view_barcode.php?type=request&id=<?php echo $request['id']; ?>" class="text-decoration-none">
                                        <i class="fas fa-external-link-alt"></i> Ver código completo
                                    </a>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Campos Adicionais (se existirem) -->
                <?php if (!empty($request['priority']) || !empty($request['department']) || !empty($request['notes'])): ?>
                <div class="row mt-3">
                    <?php if (!empty($request['priority'])): ?>
                    <div class="col-md-4">
                        <strong>Prioridade:</strong><br>
                        <?php
                        $priorityLabels = [
                            'low' => '🟢 Baixa',
                            'medium' => '🟡 Média',
                            'high' => '🟠 Alta',
                            'urgent' => '🔴 Urgente'
                        ];
                        $priorityColors = [
                            'low' => 'success',
                            'medium' => 'warning',
                            'high' => 'orange',
                            'urgent' => 'danger'
                        ];
                        ?>
                        <span class="badge badge-<?php echo $priorityColors[$request['priority']] ?? 'secondary'; ?>">
                            <?php echo $priorityLabels[$request['priority']] ?? ucfirst($request['priority']); ?>
                        </span>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($request['department'])): ?>
                    <div class="col-md-4">
                        <strong>Departamento:</strong><br>
                        <i class="fas fa-building text-info"></i>
                        <?php echo htmlspecialchars($request['department']); ?>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($request['notes'])): ?>
                    <div class="col-md-4">
                        <strong>Observações:</strong><br>
                        <small class="text-muted"><?php echo nl2br(htmlspecialchars($request['notes'])); ?></small>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Itens Solicitados -->
        <div class="card">
            <div class="card-header">
                <h5>Itens Solicitados</h5>
            </div>
            <div class="card-body">
                <?php if (empty($items)): ?>
                    <p>Nenhum item encontrado para esta requisição.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Descrição</th>
                                    <th>Quantidade Solicitada</th>
                                    <th>Unidade</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['description']); ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td><?php echo htmlspecialchars($item['unit']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <strong>Total de itens diferentes: </strong><?php echo count($items); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Ações para Admin -->
        <?php if ($_SESSION['role'] == 'admin' && $request['status'] == 'pending'): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5>Ações Administrativas</h5>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success btn-lg"
                            onclick="confirmApproval(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo count($items); ?>)">
                        <i class="fas fa-check-circle"></i> Aprovar Requisição
                    </button>
                    <button type="button" class="btn btn-danger btn-lg"
                            onclick="confirmRejection(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo count($items); ?>)">
                        <i class="fas fa-times-circle"></i> Rejeitar Requisição
                    </button>
                </div>
            </div>
        </div>
        <?php elseif ($_SESSION['role'] == 'admin' && $request['status'] == 'approved'): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5>Ações Administrativas</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary btn-lg"
                        onclick="confirmDelivery(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo count($items); ?>)">
                    <i class="fas fa-truck"></i> Marcar como Entregue
                </button>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Modal de Confirmação de Aprovação -->
    <div class="modal fade" id="approvalModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-check-circle"></i>
                        Confirmar Aprovação
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Atenção:</strong> Esta ação aprovará a requisição e permitirá que os itens sejam preparados para entrega.
                    </div>

                    <h6><strong>Detalhes da Requisição:</strong></h6>
                    <ul class="list-unstyled">
                        <li><strong>Nome:</strong> <span id="approval-request-name"></span></li>
                        <li><strong>Solicitante:</strong> <span id="approval-username"></span></li>
                        <li><strong>Total de Itens:</strong> <span id="approval-item-count"></span></li>
                        <li><strong>Data da Solicitação:</strong> <?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?></li>
                    </ul>

                    <div class="form-group">
                        <label for="approval-notes">
                            <i class="fas fa-sticky-note"></i>
                            Observações da Aprovação (opcional):
                        </label>
                        <textarea id="approval-notes" class="form-control" rows="3"
                                  placeholder="Adicione observações sobre a aprovação, instruções especiais, etc..."></textarea>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Importante:</strong> Após a aprovação, a requisição ficará disponível para entrega e não poderá mais ser editada.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="button" class="btn btn-success btn-lg" onclick="executeApproval()">
                        <i class="fas fa-check-circle"></i> Confirmar Aprovação
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação de Rejeição -->
    <div class="modal fade" id="rejectionModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-times-circle"></i>
                        Confirmar Rejeição
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Atenção:</strong> Esta ação rejeitará a requisição permanentemente.
                    </div>

                    <h6><strong>Detalhes da Requisição:</strong></h6>
                    <ul class="list-unstyled">
                        <li><strong>Nome:</strong> <span id="rejection-request-name"></span></li>
                        <li><strong>Solicitante:</strong> <span id="rejection-username"></span></li>
                        <li><strong>Total de Itens:</strong> <span id="rejection-item-count"></span></li>
                        <li><strong>Data da Solicitação:</strong> <?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?></li>
                    </ul>

                    <div class="form-group">
                        <label for="rejection-reason">
                            <i class="fas fa-comment-alt"></i>
                            Motivo da Rejeição *:
                        </label>
                        <select id="rejection-reason" class="form-control" required>
                            <option value="">Selecione um motivo...</option>
                            <option value="itens_indisponiveis">Itens indisponíveis no estoque</option>
                            <option value="orcamento_insuficiente">Orçamento insuficiente</option>
                            <option value="requisicao_duplicada">Requisição duplicada</option>
                            <option value="itens_desnecessarios">Itens considerados desnecessários</option>
                            <option value="fora_prazo">Solicitação fora do prazo</option>
                            <option value="informacoes_incompletas">Informações incompletas</option>
                            <option value="outro">Outro motivo</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="rejection-notes">
                            <i class="fas fa-sticky-note"></i>
                            Observações Adicionais:
                        </label>
                        <textarea id="rejection-notes" class="form-control" rows="3"
                                  placeholder="Explique detalhadamente o motivo da rejeição para que o solicitante possa entender..."></textarea>
                    </div>

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        <strong>Importante:</strong> O solicitante será notificado sobre a rejeição e o motivo informado.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="button" class="btn btn-danger btn-lg" onclick="executeRejection()">
                        <i class="fas fa-times-circle"></i> Confirmar Rejeição
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação de Entrega -->
    <div class="modal fade" id="deliveryModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-truck"></i>
                        Confirmar Entrega
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Confirmação:</strong> Marque como entregue apenas após a entrega física dos itens.
                    </div>

                    <h6><strong>Detalhes da Requisição:</strong></h6>
                    <ul class="list-unstyled">
                        <li><strong>Nome:</strong> <span id="delivery-request-name"></span></li>
                        <li><strong>Solicitante:</strong> <span id="delivery-username"></span></li>
                        <li><strong>Total de Itens:</strong> <span id="delivery-item-count"></span></li>
                        <li><strong>Data da Aprovação:</strong> <span id="delivery-approval-date"></span></li>
                    </ul>

                    <div class="form-group">
                        <label for="delivery-notes">
                            <i class="fas fa-sticky-note"></i>
                            Observações da Entrega (opcional):
                        </label>
                        <textarea id="delivery-notes" class="form-control" rows="3"
                                  placeholder="Informações sobre a entrega, local, responsável, etc..."></textarea>
                    </div>

                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>Resultado:</strong> Após confirmar, a requisição será marcada como concluída.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="button" class="btn btn-primary btn-lg" onclick="executeDelivery()">
                        <i class="fas fa-truck"></i> Confirmar Entrega
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let currentRequestId = null;

        function confirmApproval(requestId, requestName, username, itemCount) {
            currentRequestId = requestId;
            document.getElementById('approval-request-name').textContent = requestName;
            document.getElementById('approval-username').textContent = username;
            document.getElementById('approval-item-count').textContent = itemCount + ' item(ns)';
            document.getElementById('approval-notes').value = '';
            $('#approvalModal').modal('show');
        }

        function confirmRejection(requestId, requestName, username, itemCount) {
            currentRequestId = requestId;
            document.getElementById('rejection-request-name').textContent = requestName;
            document.getElementById('rejection-username').textContent = username;
            document.getElementById('rejection-item-count').textContent = itemCount + ' item(ns)';
            document.getElementById('rejection-reason').value = '';
            document.getElementById('rejection-notes').value = '';
            $('#rejectionModal').modal('show');
        }

        function confirmDelivery(requestId, requestName, username, itemCount) {
            currentRequestId = requestId;
            document.getElementById('delivery-request-name').textContent = requestName;
            document.getElementById('delivery-username').textContent = username;
            document.getElementById('delivery-item-count').textContent = itemCount + ' item(ns)';
            document.getElementById('delivery-approval-date').textContent = 'Hoje - ' + new Date().toLocaleDateString('pt-BR');
            document.getElementById('delivery-notes').value = '';
            $('#deliveryModal').modal('show');
        }

        function executeApproval() {
            const notes = document.getElementById('approval-notes').value;

            // Mostrar loading
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
            btn.disabled = true;

            // Construir URL com parâmetros
            let url = `manage_requests.php?action=approve&id=${currentRequestId}`;
            if (notes.trim()) {
                url += `&notes=${encodeURIComponent(notes)}`;
            }

            // Redirecionar
            window.location.href = url;
        }

        function executeRejection() {
            const reason = document.getElementById('rejection-reason').value;
            const notes = document.getElementById('rejection-notes').value;

            if (!reason) {
                alert('Por favor, selecione um motivo para a rejeição.');
                document.getElementById('rejection-reason').focus();
                return;
            }

            // Mostrar loading
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
            btn.disabled = true;

            // Construir URL com parâmetros
            let url = `manage_requests.php?action=reject&id=${currentRequestId}&reason=${encodeURIComponent(reason)}`;
            if (notes.trim()) {
                url += `&notes=${encodeURIComponent(notes)}`;
            }

            // Redirecionar
            window.location.href = url;
        }

        function executeDelivery() {
            const notes = document.getElementById('delivery-notes').value;

            // Mostrar loading
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
            btn.disabled = true;

            // Construir URL com parâmetros
            let url = `manage_requests.php?action=deliver&id=${currentRequestId}`;
            if (notes.trim()) {
                url += `&notes=${encodeURIComponent(notes)}`;
            }

            // Redirecionar
            window.location.href = url;
        }

        // Validação em tempo real para o motivo de rejeição
        document.getElementById('rejection-reason').addEventListener('change', function() {
            const notesField = document.getElementById('rejection-notes');
            if (this.value === 'outro') {
                notesField.required = true;
                notesField.placeholder = 'Por favor, explique detalhadamente o motivo da rejeição...';
                notesField.focus();
            } else {
                notesField.required = false;
                notesField.placeholder = 'Observações adicionais (opcional)...';
            }
        });
    </script>
</body>
</html>