<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$requestId = (int)$_GET['id'];

// Verificar se o usuário tem permissão para ver esta requisição
$stmt = $pdo->prepare("
    SELECT r.*, u.username 
    FROM requests r 
    JOIN users u ON r.user_id = u.id 
    WHERE r.id = ?
");
$stmt->execute([$requestId]);
$request = $stmt->fetch();

if (!$request || ($request['user_id'] != $_SESSION['user_id'] && $_SESSION['role'] != 'admin')) {
    header('Location: index.php');
    exit;
}

// Buscar itens da requisição
$stmt = $pdo->prepare("
    SELECT ri.*, i.name, i.description, i.unit 
    FROM request_items ri 
    JOIN items i ON ri.item_id = i.id 
    WHERE ri.request_id = ?
");
$stmt->execute([$requestId]);
$items = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html>
<head>
    <title><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?> - Sistema de Requisição</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h2><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></h2>
                <small class="text-muted">Detalhes da Requisição #<?php echo $request['id']; ?></small>
            </div>
            <div class="col-md-4 text-right">
                <a href="<?php echo $_SESSION['role'] == 'admin' ? 'manage_requests.php' : 'my_requests.php'; ?>"
                   class="btn btn-secondary">⬅ Voltar</a>

                <?php if ($request['status'] == 'pending' && ($_SESSION['role'] == 'admin' || $request['user_id'] == $_SESSION['user_id'])): ?>
                <a href="edit_request.php?id=<?php echo $request['id']; ?>" class="btn btn-warning">
                    ✏️ Editar
                </a>
                <?php endif; ?>

                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
                        📄 Exportar
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=excel">
                            📊 Excel
                        </a>
                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=pdf">
                            📄 PDF
                        </a>
                        <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=docx">
                            📝 Word
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações da Requisição -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Informações Gerais</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <strong>Nome da Requisição:</strong><br>
                        <h4 class="text-primary"><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <strong>ID da Requisição:</strong><br>
                        #<?php echo $request['id']; ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Solicitante:</strong><br>
                        <?php echo htmlspecialchars($request['username']); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Data da Requisição:</strong><br>
                        <?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Status:</strong><br>
                        <?php
                        $statusLabels = [
                            'pending' => 'Pendente',
                            'approved' => 'Aprovada',
                            'rejected' => 'Rejeitada',
                            'delivered' => 'Entregue'
                        ];
                        $statusColors = [
                            'pending' => 'warning',
                            'approved' => 'success',
                            'rejected' => 'danger',
                            'delivered' => 'info'
                        ];
                        ?>
                        <span class="badge badge-<?php echo $statusColors[$request['status']]; ?>">
                            <?php echo $statusLabels[$request['status']]; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Itens Solicitados -->
        <div class="card">
            <div class="card-header">
                <h5>Itens Solicitados</h5>
            </div>
            <div class="card-body">
                <?php if (empty($items)): ?>
                    <p>Nenhum item encontrado para esta requisição.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Descrição</th>
                                    <th>Quantidade Solicitada</th>
                                    <th>Unidade</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['description']); ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td><?php echo htmlspecialchars($item['unit']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <strong>Total de itens diferentes: </strong><?php echo count($items); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Ações para Admin -->
        <?php if ($_SESSION['role'] == 'admin' && $request['status'] == 'pending'): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5>Ações Administrativas</h5>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <a href="manage_requests.php?action=approve&id=<?php echo $request['id']; ?>"
                       class="btn btn-success"
                       onclick="return confirm('Tem certeza que deseja aprovar esta requisição?')">
                        Aprovar Requisição
                    </a>
                    <a href="manage_requests.php?action=reject&id=<?php echo $request['id']; ?>"
                       class="btn btn-danger"
                       onclick="return confirm('Tem certeza que deseja rejeitar esta requisição?')">
                        Rejeitar Requisição
                    </a>
                </div>
            </div>
        </div>
        <?php elseif ($_SESSION['role'] == 'admin' && $request['status'] == 'approved'): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5>Ações Administrativas</h5>
            </div>
            <div class="card-body">
                <a href="manage_requests.php?action=deliver&id=<?php echo $request['id']; ?>"
                   class="btn btn-primary"
                   onclick="return confirm('Confirmar que os itens foram entregues?')">
                    Marcar como Entregue
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>