<?php
echo "<h1>🔍 Teste de Conexão com Banco de Dados</h1>";

// Configurações do banco
$host = 'localhost';
$dbname = 'kitchen_inventory';
$username = 'root';
$password = '123mudar';

echo "<h2>📋 Configurações:</h2>";
echo "<ul>";
echo "<li><strong>Host:</strong> $host</li>";
echo "<li><strong>Database:</strong> $dbname</li>";
echo "<li><strong>Username:</strong> $username</li>";
echo "<li><strong>Password:</strong> " . (empty($password) ? 'Vazia' : 'Definida') . "</li>";
echo "</ul>";

echo "<h2>🔧 Testes de Conexão:</h2>";

// Teste 1: Verificar se extensão PDO está carregada
echo "<h3>1. Verificar Extensão PDO:</h3>";
if (extension_loaded('pdo')) {
    echo "✅ Extensão PDO está carregada<br>";
} else {
    echo "❌ Extensão PDO NÃO está carregada<br>";
}

if (extension_loaded('pdo_mysql')) {
    echo "✅ Extensão PDO MySQL está carregada<br>";
} else {
    echo "❌ Extensão PDO MySQL NÃO está carregada<br>";
}

// Teste 2: Tentar conectar sem especificar banco
echo "<h3>2. Teste de Conexão com MySQL (sem banco específico):</h3>";
try {
    $pdo_test = new PDO("mysql:host=$host", $username, $password);
    echo "✅ Conexão com MySQL estabelecida com sucesso<br>";
    
    // Listar bancos disponíveis
    echo "<h4>📁 Bancos de dados disponíveis:</h4>";
    $stmt = $pdo_test->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<ul>";
    foreach ($databases as $db) {
        echo "<li>$db</li>";
    }
    echo "</ul>";
    
    // Verificar se o banco kitchen_inventory existe
    if (in_array($dbname, $databases)) {
        echo "✅ Banco '$dbname' existe<br>";
    } else {
        echo "❌ Banco '$dbname' NÃO existe<br>";
        echo "<p><strong>🔧 Solução:</strong> Criar o banco '$dbname'</p>";
    }
    
} catch (PDOException $e) {
    echo "❌ Erro na conexão com MySQL: " . $e->getMessage() . "<br>";
}

// Teste 3: Tentar conectar com banco específico
echo "<h3>3. Teste de Conexão com Banco Específico:</h3>";
try {
    $pdo_specific = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo_specific->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Conexão com banco '$dbname' estabelecida com sucesso<br>";
    
    // Verificar tabelas
    echo "<h4>📊 Tabelas no banco:</h4>";
    $stmt = $pdo_specific->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "⚠️ Nenhuma tabela encontrada no banco<br>";
        echo "<p><strong>🔧 Solução:</strong> Executar script de criação de tabelas</p>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        // Verificar tabelas essenciais
        $essential_tables = ['users', 'items', 'requests', 'request_items'];
        $missing_tables = array_diff($essential_tables, $tables);
        
        if (empty($missing_tables)) {
            echo "✅ Todas as tabelas essenciais estão presentes<br>";
        } else {
            echo "❌ Tabelas faltando: " . implode(', ', $missing_tables) . "<br>";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Erro na conexão com banco '$dbname': " . $e->getMessage() . "<br>";
    
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "<p><strong>🔧 Solução:</strong> O banco '$dbname' não existe. Precisa ser criado.</p>";
    }
}

// Teste 4: Verificar arquivo de configuração
echo "<h3>4. Verificar Arquivo de Configuração:</h3>";
if (file_exists('config/db_connect.php')) {
    echo "✅ Arquivo config/db_connect.php existe<br>";
    
    // Incluir e testar
    ob_start();
    include 'config/db_connect.php';
    $output = ob_get_clean();
    
    if (isset($pdo) && $pdo !== null) {
        echo "✅ Variável \$pdo foi criada com sucesso<br>";
        echo "✅ Conexão através do arquivo de configuração funcionando<br>";
    } else {
        echo "❌ Variável \$pdo não foi criada ou é null<br>";
        if (isset($GLOBALS['db_connected']) && !$GLOBALS['db_connected']) {
            echo "⚠️ Flag db_connected indica falha na conexão<br>";
        }
    }
} else {
    echo "❌ Arquivo config/db_connect.php NÃO existe<br>";
}

echo "<h2>🎯 Resumo e Próximos Passos:</h2>";

// Determinar próximos passos baseado nos testes
if (!extension_loaded('pdo') || !extension_loaded('pdo_mysql')) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Problema: Extensões PHP</h4>";
    echo "<p>As extensões PDO não estão carregadas. Verifique a configuração do PHP no WAMP.</p>";
    echo "</div>";
} elseif (!isset($pdo_test)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Problema: Conexão MySQL</h4>";
    echo "<p>Não foi possível conectar ao MySQL. Verifique se o serviço está rodando no WAMP.</p>";
    echo "</div>";
} elseif (!isset($pdo_specific)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ Problema: Banco de Dados</h4>";
    echo "<p>O banco '$dbname' não existe ou não pode ser acessado.</p>";
    echo "<p><strong>Solução:</strong> <a href='create_database.php'>Criar banco de dados</a></p>";
    echo "</div>";
} elseif (empty($tables)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ Problema: Tabelas</h4>";
    echo "<p>O banco existe mas não tem tabelas.</p>";
    echo "<p><strong>Solução:</strong> <a href='create_tables.php'>Criar tabelas</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #d1edff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Tudo Funcionando!</h4>";
    echo "<p>A conexão com o banco está funcionando corretamente.</p>";
    echo "<p><a href='index.php'>Voltar ao sistema</a></p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Teste executado em: " . date('d/m/Y H:i:s') . "</small></p>";
?>
