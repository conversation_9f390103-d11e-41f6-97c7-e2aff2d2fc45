<?php
require_once 'BaseController.php';

/**
 * Controller de Requisições
 * Gerencia todas as operações relacionadas a requisições
 */
class RequestController extends BaseController {
    
    /**
     * Cria nova requisição
     */
    public function create($data) {
        $this->requireAuth();
        
        // Validar dados
        $errors = $this->validate($data, [
            'title' => ['required' => true, 'max' => 255],
            'items' => ['required' => true, 'custom' => function($items) {
                if (!is_array($items) || empty($items)) {
                    return 'Pelo menos um item deve ser selecionado';
                }
                return true;
            }]
        ]);
        
        if (!empty($errors)) {
            throw new Exception('Dados inválidos: ' . implode(', ', $errors));
        }
        
        $this->db->beginTransaction();
        
        try {
            // Gerar código interno
            $tempId = time(); // ID temporário para gerar código
            $internalCode = $this->generateInternalCode('REQ', $tempId);
            
            // Criar requisição
            $requestData = [
                'user_id' => $this->user['id'],
                'title' => $this->sanitize($data['title']),
                'priority' => $data['priority'] ?? 'normal',
                'department' => $data['department'] ?? '',
                'notes' => $data['notes'] ?? '',
                'internal_code' => $internalCode,
                'status' => 'pending',
                'request_date' => date('Y-m-d H:i:s')
            ];
            
            // Tentar inserir com fallback progressivo
            try {
                $requestId = $this->db->insert('requests', $requestData);
            } catch (Exception $e) {
                // Fallback para estrutura básica
                $basicData = [
                    'user_id' => $requestData['user_id'],
                    'status' => $requestData['status'],
                    'request_date' => $requestData['request_date']
                ];
                $requestId = $this->db->insert('requests', $basicData);
            }
            
            // Atualizar código interno com ID real
            $realInternalCode = $this->generateInternalCode('REQ', $requestId);
            $this->db->update('requests', 
                ['internal_code' => $realInternalCode], 
                'id = ?', 
                [$requestId]
            );
            
            // Adicionar itens
            $itemCount = 0;
            foreach ($data['items'] as $itemId => $quantity) {
                $quantity = (int)$quantity;
                if ($quantity > 0) {
                    // Verificar se item existe
                    $item = $this->db->fetchOne("SELECT id FROM items WHERE id = ?", [$itemId]);
                    if ($item) {
                        $this->db->insert('request_items', [
                            'request_id' => $requestId,
                            'item_id' => $itemId,
                            'quantity' => $quantity
                        ]);
                        $itemCount++;
                    }
                }
            }
            
            if ($itemCount === 0) {
                throw new Exception('Nenhum item válido foi adicionado à requisição');
            }
            
            $this->db->commit();
            
            // Log da atividade
            $this->logActivity('requisicao_criada', [
                'request_id' => $requestId,
                'internal_code' => $realInternalCode,
                'item_count' => $itemCount
            ]);
            
            return [
                'id' => $requestId,
                'internal_code' => $realInternalCode,
                'item_count' => $itemCount
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Busca requisições do usuário
     */
    public function getUserRequests($userId, $page = 1, $perPage = 10) {
        $where = 'r.user_id = ?';
        $params = [$userId];
        
        $sql = "
            SELECT r.*, COUNT(ri.id) as item_count 
            FROM requests r 
            LEFT JOIN request_items ri ON r.id = ri.request_id 
            WHERE {$where}
            GROUP BY r.id 
            ORDER BY r.request_date DESC
        ";
        
        // Implementar paginação manual para query complexa
        $page = max(1, (int)$page);
        $offset = ($page - 1) * $perPage;
        
        // Contar total
        $countSql = "SELECT COUNT(DISTINCT r.id) FROM requests r WHERE {$where}";
        $total = $this->db->fetchColumn($countSql, $params);
        
        // Buscar dados com paginação
        if ($offset > 0) {
            $sql .= " LIMIT ?, ?";
            $finalParams = array_merge($params, [$offset, $perPage]);
        } else {
            $sql .= " LIMIT ?";
            $finalParams = array_merge($params, [$perPage]);
        }
        
        $data = $this->db->fetchAll($sql, $finalParams);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'perPage' => $perPage,
            'totalPages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * Busca todas as requisições (admin)
     */
    public function getAllRequests($page = 1, $perPage = 20, $status = null) {
        $this->requireAdmin();
        
        $where = '';
        $params = [];
        
        if ($status) {
            $where = 'r.status = ?';
            $params[] = $status;
        }
        
        $sql = "
            SELECT r.*, u.username, COUNT(ri.id) as item_count 
            FROM requests r 
            JOIN users u ON r.user_id = u.id 
            LEFT JOIN request_items ri ON r.id = ri.request_id 
        ";
        
        if ($where) {
            $sql .= " WHERE {$where}";
        }
        
        $sql .= " GROUP BY r.id ORDER BY r.request_date DESC";
        
        // Paginação
        $page = max(1, (int)$page);
        $offset = ($page - 1) * $perPage;
        
        // Contar total
        $countWhere = $where ? "WHERE {$where}" : '';
        $total = $this->db->fetchColumn("SELECT COUNT(DISTINCT r.id) FROM requests r {$countWhere}", $params);
        
        // Buscar dados
        if ($offset > 0) {
            $sql .= " LIMIT ?, ?";
            $finalParams = array_merge($params, [$offset, $perPage]);
        } else {
            $sql .= " LIMIT ?";
            $finalParams = array_merge($params, [$perPage]);
        }
        
        $data = $this->db->fetchAll($sql, $finalParams);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'perPage' => $perPage,
            'totalPages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * Busca detalhes de uma requisição
     */
    public function getRequestDetails($id) {
        $this->requireAuth();
        
        // Buscar requisição
        $request = $this->db->fetchOne("
            SELECT r.*, u.username 
            FROM requests r 
            JOIN users u ON r.user_id = u.id 
            WHERE r.id = ?
        ", [$id]);
        
        if (!$request) {
            throw new Exception('Requisição não encontrada');
        }
        
        // Verificar permissão
        if (!$this->isAdmin() && $request['user_id'] != $this->user['id']) {
            throw new Exception('Acesso negado');
        }
        
        // Buscar itens
        $items = $this->db->fetchAll("
            SELECT ri.*, i.name, i.description, i.unit 
            FROM request_items ri 
            JOIN items i ON ri.item_id = i.id 
            WHERE ri.request_id = ? 
            ORDER BY i.name
        ", [$id]);
        
        return [
            'request' => $request,
            'items' => $items
        ];
    }
    
    /**
     * Atualiza status da requisição
     */
    public function updateStatus($id, $status, $notes = '', $reason = '') {
        $this->requireAdmin();
        
        $validStatuses = ['pending', 'approved', 'rejected', 'delivered'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception('Status inválido');
        }
        
        // Buscar requisição
        $request = $this->db->fetchOne("SELECT * FROM requests WHERE id = ?", [$id]);
        if (!$request) {
            throw new Exception('Requisição não encontrada');
        }
        
        // Atualizar status
        $updateData = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($notes) {
            $updateData['admin_notes'] = $notes;
        }
        
        $this->db->update('requests', $updateData, 'id = ?', [$id]);
        
        // Registrar log se tabela existir
        try {
            $this->db->insert('request_logs', [
                'request_id' => $id,
                'action' => $status,
                'user_id' => $this->user['id'],
                'notes' => $notes,
                'reason' => $reason,
                'action_date' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Ignorar se tabela não existir
        }
        
        // Log da atividade
        $this->logActivity('requisicao_status_alterado', [
            'request_id' => $id,
            'old_status' => $request['status'],
            'new_status' => $status,
            'notes' => $notes,
            'reason' => $reason
        ]);
        
        return true;
    }
    
    /**
     * Exclui requisição
     */
    public function delete($id) {
        $this->requireAuth();

        // Buscar requisição
        $request = $this->db->fetchOne("SELECT * FROM requests WHERE id = ?", [$id]);
        if (!$request) {
            throw new Exception('Requisição não encontrada');
        }

        // Verificar permissão
        if (!$this->isAdmin() && $request['user_id'] != $this->user['id']) {
            throw new Exception('Acesso negado');
        }

        // Só permitir exclusão de requisições pendentes
        if ($request['status'] !== 'pending') {
            throw new Exception('Apenas requisições pendentes podem ser excluídas');
        }

        $this->db->beginTransaction();

        try {
            // Excluir itens da requisição
            $this->db->delete('request_items', 'request_id = ?', [$id]);

            // Excluir requisição
            $this->db->delete('requests', 'id = ?', [$id]);

            $this->db->commit();

            // Log da atividade
            $this->logActivity('requisicao_excluida', [
                'request_id' => $id,
                'internal_code' => $request['internal_code'] ?? ''
            ]);

            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Busca estatísticas das requisições
     */
    public function getStats($userId = null) {
        $where = '';
        $params = [];

        if ($userId && !$this->isAdmin()) {
            $where = 'WHERE user_id = ?';
            $params[] = $userId;
        }

        $stats = [
            'total' => $this->db->fetchColumn("SELECT COUNT(*) FROM requests {$where}", $params),
            'pending' => $this->db->fetchColumn("SELECT COUNT(*) FROM requests {$where} " . ($where ? 'AND' : 'WHERE') . " status = 'pending'", array_merge($params, ['pending'])),
            'approved' => $this->db->fetchColumn("SELECT COUNT(*) FROM requests {$where} " . ($where ? 'AND' : 'WHERE') . " status = 'approved'", array_merge($params, ['approved'])),
            'rejected' => $this->db->fetchColumn("SELECT COUNT(*) FROM requests {$where} " . ($where ? 'AND' : 'WHERE') . " status = 'rejected'", array_merge($params, ['rejected'])),
            'delivered' => $this->db->fetchColumn("SELECT COUNT(*) FROM requests {$where} " . ($where ? 'AND' : 'WHERE') . " status = 'delivered'", array_merge($params, ['delivered']))
        ];

        return $stats;
    }
}
?>
