<?php
// Iniciar sessão se não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Incluir configuração de página
require_once 'includes/page_config.php';

// Aplicar configuração automática da página
initPage();

// Incluir dependências primeiro
require_once 'config/db_connect.php';
require_once 'includes/barcode_generator.php';

// Incluir layout
require_once 'includes/layout.php';

$success = '';
$error = '';

// Parâmetros de pesquisa e paginação
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 20;
$offset = ($page - 1) * $itemsPerPage;

// Construir query de busca
$whereClause = '';
$params = [];

if (!empty($search)) {
    $whereClause = "WHERE name LIKE ? OR description LIKE ?";
    $params = ["%$search%", "%$search%"];
}

// Verificar se a conexão com banco está disponível
$items = [];
$totalItems = 0;
$totalPages = 0;

// Verificar se a conexão está disponível usando múltiplas verificações
$db_available = false;
if (isset($pdo) && $pdo !== null) {
    $db_available = true;
} elseif (isset($GLOBALS['db_connected']) && $GLOBALS['db_connected'] === true) {
    // Tentar reconectar se a flag global indica que deveria estar conectado
    try {
        $host = 'localhost';
        $dbname = 'kitchen_inventory';
        $username = 'root';
        $password = '123mudar';
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $db_available = true;
    } catch (PDOException $e) {
        $db_available = false;
    }
}

if ($db_available && isset($pdo) && $pdo !== null) {
    try {
        // Contar total de itens para paginação
        $countSql = "SELECT COUNT(*) FROM items $whereClause";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $totalItems = $countStmt->fetchColumn();
        $totalPages = ceil($totalItems / $itemsPerPage);

        // Buscar itens com paginação
        $sql = "SELECT * FROM items $whereClause ORDER BY name LIMIT ? OFFSET ?";
        $stmt = $pdo->prepare($sql);
        $paginationParams = array_merge($params, [$itemsPerPage, $offset]);
        $stmt->execute($paginationParams);
        $items = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = "Erro ao buscar itens: " . $e->getMessage();
        $items = [];
        $totalItems = 0;
        $totalPages = 0;
    }
} else {
    $error = "Conexão com banco de dados não disponível.";
}

// Função para destacar termos de pesquisa
function highlightSearch($text, $search) {
    if (empty($search) || empty($text)) {
        return htmlspecialchars($text);
    }

    $highlighted = htmlspecialchars($text);
    $searchTerms = explode(' ', $search);

    foreach ($searchTerms as $term) {
        if (strlen(trim($term)) > 2) {
            $highlighted = preg_replace(
                '/(' . preg_quote(trim($term), '/') . ')/i',
                '<mark>$1</mark>',
                $highlighted
            );
        }
    }

    return $highlighted;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['items'])) {
    if (!isset($pdo) || $pdo === null) {
        $error = "Erro: Conexão com banco de dados não disponível.";
    } else {
        try {
            $pdo->beginTransaction();

        // Obter dados da requisição
        $title = trim($_POST['title'] ?? '');
        if (empty($title)) {
            $title = 'Requisição #' . date('Y-m-d H:i');
        }

        $priority = $_POST['priority'] ?? 'medium';
        $department = trim($_POST['department'] ?? '');
        $notes = trim($_POST['notes'] ?? '');

        // Gerar códigos internos e de barras
        $internalCode = generateRequestInternalCode($pdo);
        $barcode = generateRequestBarcode(0); // Será atualizado após inserção

        // Criar nova requisição com novos campos
        try {
            $stmt = $pdo->prepare("
                INSERT INTO requests (user_id, title, priority, department, notes, internal_code, barcode)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$_SESSION['user_id'], $title, $priority, $department, $notes, $internalCode, $barcode]);
            $requestId = $pdo->lastInsertId();

            // Atualizar código de barras com ID real
            $finalBarcode = generateRequestBarcode($requestId);
            $updateStmt = $pdo->prepare("UPDATE requests SET barcode = ? WHERE id = ?");
            $updateStmt->execute([$finalBarcode, $requestId]);

        } catch (PDOException $e) {
            // Fallback para versões antigas do banco
            if (strpos($e->getMessage(), 'priority') !== false ||
                strpos($e->getMessage(), 'department') !== false ||
                strpos($e->getMessage(), 'internal_code') !== false) {

                // Tentar apenas com title
                try {
                    $stmt = $pdo->prepare("INSERT INTO requests (user_id, title) VALUES (?, ?)");
                    $stmt->execute([$_SESSION['user_id'], $title]);
                    $requestId = $pdo->lastInsertId();
                    $error = "Aviso: Sistema precisa ser atualizado para suportar novos campos. <a href='update_database_codes.php'>Clique aqui para atualizar</a>";
                } catch (PDOException $e2) {
                    // Fallback final
                    $stmt = $pdo->prepare("INSERT INTO requests (user_id) VALUES (?)");
                    $stmt->execute([$_SESSION['user_id']]);
                    $requestId = $pdo->lastInsertId();
                    $error = "Aviso: Sistema precisa ser atualizado. <a href='update_database_codes.php'>Clique aqui para atualizar</a>";
                }
            } else {
                throw $e;
            }
        }

        // Adicionar itens à requisição
        $itemCount = 0;
        if (isset($_POST['items']) && is_array($_POST['items'])) {
            foreach ($_POST['items'] as $itemId => $quantity) {
                $quantity = (int)$quantity;
                if ($quantity > 0) {
                    // Verificar se o item existe
                    $checkStmt = $pdo->prepare("SELECT id FROM items WHERE id = ?");
                    $checkStmt->execute([$itemId]);
                    if ($checkStmt->fetch()) {
                        $stmt = $pdo->prepare("INSERT INTO request_items (request_id, item_id, quantity) VALUES (?, ?, ?)");
                        $stmt->execute([$requestId, $itemId, $quantity]);
                        $itemCount++;
                    }
                }
            }
        }

        if ($itemCount == 0) {
            throw new Exception("Nenhum item selecionado ou itens inválidos");
        }

        $pdo->commit();
        $success = "Requisição enviada com sucesso! $itemCount item(ns) solicitado(s).";

        // Limpar variáveis de pesquisa após sucesso
        $search = '';
        $page = 1;

        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "Erro ao enviar requisição: " . $e->getMessage();
        }
    }
}
?>

<!-- Mensagens de feedback -->
<?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $success; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <div class="content-card text-center">
        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
        <h3>Requisição Criada com Sucesso!</h3>
        <p class="text-muted mb-4">Sua requisição foi enviada e está aguardando aprovação.</p>
        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
            <a href="my_requests.php" class="btn btn-primary btn-custom">
                <i class="fas fa-list me-2"></i>Ver Minhas Requisições
            </a>
            <a href="request_form.php" class="btn btn-outline-secondary btn-custom">
                <i class="fas fa-plus me-2"></i>Nova Requisição
            </a>
        </div>
    </div>
<?php elseif ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (!$success): ?>
<!-- Campo de Pesquisa -->
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-search text-primary me-2"></i>
        Pesquisar Produtos
    </h5>
    <form method="get" class="mb-0">
        <div class="row">
            <div class="col-md-8 mb-3">
                <input type="text" name="search" class="form-control"
                       placeholder="Digite o nome ou descrição do produto..."
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <button type="submit" class="btn btn-primary btn-custom w-100">
                    <i class="fas fa-search me-2"></i>Pesquisar
                </button>
            </div>
        </div>
        <?php if (!empty($search)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Pesquisando por: "<strong><?php echo htmlspecialchars($search); ?></strong>"
            - <?php echo $totalItems; ?> produto(s) encontrado(s)
            <a href="request_form.php" class="btn btn-sm btn-outline-secondary ms-2">
                <i class="fas fa-times me-1"></i>Limpar Pesquisa
            </a>
        </div>
        <?php endif; ?>
    </form>
</div>

            <!-- Informações de Paginação -->
            <?php if ($totalItems > 0): ?>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <small class="text-muted">
                        Mostrando <?php echo min($offset + 1, $totalItems); ?> -
                        <?php echo min($offset + $itemsPerPage, $totalItems); ?> de
                        <?php echo $totalItems; ?> produtos
                    </small>
                </div>
                <div>
                    <small class="text-muted">Página <?php echo $page; ?> de <?php echo $totalPages; ?></small>
                </div>
            </div>
            <?php endif; ?>

<form method="post">
    <!-- Informações da requisição expandidas -->
    <div class="content-card">
        <h5 class="mb-4">
            <i class="fas fa-edit text-primary me-2"></i>
            Informações da Requisição
        </h5>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="title" class="form-label">
                    <i class="fas fa-tag text-primary me-2"></i>
                    Nome da Requisição
                </label>
                <input type="text" name="title" id="title" class="form-control"
                       placeholder="Ex: Estoque Semanal, Festa Junina, Ingredientes para Pizza..."
                       maxlength="255">
                <div class="form-text">
                    Deixe em branco para gerar automaticamente
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <label for="priority" class="form-label">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Prioridade
                </label>
                <select name="priority" id="priority" class="form-select">
                    <option value="low">🟢 Baixa - Não urgente</option>
                    <option value="medium" selected>🟡 Média - Normal</option>
                    <option value="high">🟠 Alta - Importante</option>
                    <option value="urgent">🔴 Urgente - Crítica</option>
                </select>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="department" class="form-label">
                    <i class="fas fa-building text-info me-2"></i>
                    Departamento/Setor
                </label>
                <input type="text" name="department" id="department" class="form-control"
                       placeholder="Ex: Cozinha Principal, Confeitaria, Eventos..."
                       maxlength="100">
                <div class="form-text">
                    Opcional - Especifique o setor solicitante
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <label for="notes" class="form-label">
                    <i class="fas fa-sticky-note text-secondary me-2"></i>
                    Observações
                </label>
                <textarea name="notes" id="notes" class="form-control" rows="3"
                          placeholder="Observações adicionais, instruções especiais, etc..."
                          maxlength="500"></textarea>
                <div class="form-text">
                    Opcional - Máximo 500 caracteres
                </div>
            </div>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Informação:</strong> Após criar a requisição, será gerado automaticamente um
            <strong>código interno</strong> e <strong>código de barras</strong> para rastreamento.
        </div>
    </div>

                <!-- Preservar parâmetros de pesquisa no formulário -->
                <?php if (!empty($search)): ?>
                    <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                <?php endif; ?>
                <input type="hidden" name="page" value="<?php echo $page; ?>">

    <!-- Lista de Produtos -->
    <div class="content-card">
        <h5 class="mb-3">
            <i class="fas fa-list text-success me-2"></i>
            Selecionar Produtos
        </h5>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th width="25%">Item</th>
                        <th width="35%">Descrição</th>
                        <th width="15%">Unidade</th>
                        <th width="25%">Quantidade</th>
                    </tr>
                </thead>
                        <tbody>
                            <?php if (empty($items)): ?>
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <?php if (!empty($search)): ?>
                                        🔍 Nenhum produto encontrado para "<strong><?php echo htmlspecialchars($search); ?></strong>"
                                        <br><small>Tente pesquisar com outros termos</small>
                                    <?php else: ?>
                                        📦 Nenhum produto cadastrado no sistema
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php else: ?>
                                <?php foreach ($items as $item): ?>
                                <tr class="item-row" data-item-id="<?php echo $item['id']; ?>">
                                    <td>
                                        <strong><?php echo highlightSearch($item['name'], $search); ?></strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo highlightSearch($item['description'], $search); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo htmlspecialchars($item['unit']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <input type="number" name="items[<?php echo $item['id']; ?>]"
                                               class="form-control form-control-sm quantity-input"
                                               min="0" value="0" onchange="updateRowHighlight(this)"
                                               style="width: 100px;">
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

                <?php if (!empty($items)): ?>
                <!-- Controles de Paginação -->
                <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center mb-4">
                    <nav aria-label="Navegação de páginas">
                        <ul class="pagination">
                            <!-- Primeira página -->
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    ⏮ Primeira
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Página anterior -->
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    ⏪ Anterior
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Páginas numeradas -->
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);

                            for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <!-- Próxima página -->
                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    Próxima ⏩
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Última página -->
                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    Última ⏭
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>

            <!-- Botões de Ação -->
            <div class="content-card">
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <button type="submit" class="btn btn-primary btn-custom btn-lg">
                        <i class="fas fa-paper-plane me-2"></i>Enviar Requisição
                    </button>
                    <a href="my_requests.php" class="btn btn-outline-secondary btn-custom btn-lg">
                        <i class="fas fa-list me-2"></i>Minhas Requisições
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </form>
    <?php endif; ?>

<?php
// Scripts específicos da página
$page_scripts = '
<style>
.item-row {
    transition: background-color 0.2s;
}
.item-row.selected {
    background-color: #e3f2fd !important;
}
mark {
    background-color: #ffeb3b;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: bold;
}
.quantity-input {
    width: 100px !important;
}
</style>
<script>
// Função para destacar linhas com quantidade selecionada
function updateRowHighlight(input) {
    const row = input.closest("tr");
    const quantity = parseInt(input.value) || 0;

    if (quantity > 0) {
        row.classList.add("selected");
    } else {
        row.classList.remove("selected");
    }

    updateSelectedCount();
}

// Função para contar itens selecionados
function updateSelectedCount() {
    const selectedInputs = document.querySelectorAll(".quantity-input");
    let selectedCount = 0;
    let totalQuantity = 0;

    selectedInputs.forEach(input => {
        const quantity = parseInt(input.value) || 0;
        if (quantity > 0) {
            selectedCount++;
            totalQuantity += quantity;
        }
    });

    // Atualizar contador se existir
    const counter = document.getElementById("selected-counter");
    if (counter) {
        counter.textContent = `${selectedCount} item(ns) selecionado(s) - Total: ${totalQuantity}`;
    }
}

// Função para validar formulário antes do envio
function validateForm() {
    const selectedInputs = document.querySelectorAll(".quantity-input");
    let hasSelection = false;

    selectedInputs.forEach(input => {
        if (parseInt(input.value) > 0) {
            hasSelection = true;
        }
    });

    if (!hasSelection) {
        alert("⚠️ Selecione pelo menos um item para fazer a requisição!");
        return false;
    }

    return confirm("📋 Confirma o envio da requisição com os itens selecionados?");
}

// Adicionar validação ao formulário
document.addEventListener("DOMContentLoaded", function() {
    const form = document.querySelector("form[method=post]");
    if (form) {
        form.addEventListener("submit", function(e) {
            if (!validateForm()) {
                e.preventDefault();
            }
        });
    }

    // Adicionar contador de itens selecionados
    const submitButton = document.querySelector("button[type=submit]");
    if (submitButton) {
        const counter = document.createElement("div");
        counter.id = "selected-counter";
        counter.className = "text-center text-muted mt-2";
        counter.textContent = "0 item(ns) selecionado(s) - Total: 0";
        submitButton.parentNode.insertBefore(counter, submitButton);
    }

    // Focar no campo de pesquisa
    const searchInput = document.querySelector("input[name=search]");
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});

// Atalho de teclado para pesquisa (Ctrl+F)
document.addEventListener("keydown", function(e) {
    if (e.ctrlKey && e.key === "f") {
        e.preventDefault();
        const searchInput = document.querySelector("input[name=search]");
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }
});
</script>
';

// Incluir footer
require_once 'includes/layout_footer.php';
?>