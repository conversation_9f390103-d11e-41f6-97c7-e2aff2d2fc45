<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';
require_once 'includes/barcode_generator.php';

$success = '';
$error = '';

// Parâmetros de pesquisa e paginação
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 20;
$offset = ($page - 1) * $itemsPerPage;

// Construir query de busca
$whereClause = '';
$params = [];

if (!empty($search)) {
    $whereClause = "WHERE name LIKE ? OR description LIKE ?";
    $params = ["%$search%", "%$search%"];
}

// Contar total de itens para paginação
$countSql = "SELECT COUNT(*) FROM items $whereClause";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalItems = $countStmt->fetchColumn();
$totalPages = ceil($totalItems / $itemsPerPage);

// Buscar itens com paginação
$sql = "SELECT * FROM items $whereClause ORDER BY name LIMIT $itemsPerPage OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$items = $stmt->fetchAll();

// Função para destacar termos de pesquisa
function highlightSearch($text, $search) {
    if (empty($search) || empty($text)) {
        return htmlspecialchars($text);
    }

    $highlighted = htmlspecialchars($text);
    $searchTerms = explode(' ', $search);

    foreach ($searchTerms as $term) {
        if (strlen(trim($term)) > 2) {
            $highlighted = preg_replace(
                '/(' . preg_quote(trim($term), '/') . ')/i',
                '<mark>$1</mark>',
                $highlighted
            );
        }
    }

    return $highlighted;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['items'])) {
    try {
        $pdo->beginTransaction();

        // Obter dados da requisição
        $title = trim($_POST['title'] ?? '');
        if (empty($title)) {
            $title = 'Requisição #' . date('Y-m-d H:i');
        }

        $priority = $_POST['priority'] ?? 'medium';
        $department = trim($_POST['department'] ?? '');
        $notes = trim($_POST['notes'] ?? '');

        // Gerar códigos internos e de barras
        $internalCode = generateRequestInternalCode($pdo);
        $barcode = generateRequestBarcode(0); // Será atualizado após inserção

        // Criar nova requisição com novos campos
        try {
            $stmt = $pdo->prepare("
                INSERT INTO requests (user_id, title, priority, department, notes, internal_code, barcode)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$_SESSION['user_id'], $title, $priority, $department, $notes, $internalCode, $barcode]);
            $requestId = $pdo->lastInsertId();

            // Atualizar código de barras com ID real
            $finalBarcode = generateRequestBarcode($requestId);
            $updateStmt = $pdo->prepare("UPDATE requests SET barcode = ? WHERE id = ?");
            $updateStmt->execute([$finalBarcode, $requestId]);

        } catch (PDOException $e) {
            // Fallback para versões antigas do banco
            if (strpos($e->getMessage(), 'priority') !== false ||
                strpos($e->getMessage(), 'department') !== false ||
                strpos($e->getMessage(), 'internal_code') !== false) {

                // Tentar apenas com title
                try {
                    $stmt = $pdo->prepare("INSERT INTO requests (user_id, title) VALUES (?, ?)");
                    $stmt->execute([$_SESSION['user_id'], $title]);
                    $requestId = $pdo->lastInsertId();
                    $error = "Aviso: Sistema precisa ser atualizado para suportar novos campos. <a href='update_database_codes.php'>Clique aqui para atualizar</a>";
                } catch (PDOException $e2) {
                    // Fallback final
                    $stmt = $pdo->prepare("INSERT INTO requests (user_id) VALUES (?)");
                    $stmt->execute([$_SESSION['user_id']]);
                    $requestId = $pdo->lastInsertId();
                    $error = "Aviso: Sistema precisa ser atualizado. <a href='update_database_codes.php'>Clique aqui para atualizar</a>";
                }
            } else {
                throw $e;
            }
        }

        // Adicionar itens à requisição
        $itemCount = 0;
        if (isset($_POST['items']) && is_array($_POST['items'])) {
            foreach ($_POST['items'] as $itemId => $quantity) {
                $quantity = (int)$quantity;
                if ($quantity > 0) {
                    // Verificar se o item existe
                    $checkStmt = $pdo->prepare("SELECT id FROM items WHERE id = ?");
                    $checkStmt->execute([$itemId]);
                    if ($checkStmt->fetch()) {
                        $stmt = $pdo->prepare("INSERT INTO request_items (request_id, item_id, quantity) VALUES (?, ?, ?)");
                        $stmt->execute([$requestId, $itemId, $quantity]);
                        $itemCount++;
                    }
                }
            }
        }

        if ($itemCount == 0) {
            throw new Exception("Nenhum item selecionado ou itens inválidos");
        }

        $pdo->commit();
        $success = "Requisição enviada com sucesso! $itemCount item(ns) solicitado(s).";

        // Limpar variáveis de pesquisa após sucesso
        $search = '';
        $page = 1;

    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "Erro ao enviar requisição: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Nova Requisição - Sistema de Requisição de Material de Cozinha</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        .search-highlight {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .quantity-input {
            width: 100px !important;
        }
        .table-hover tbody tr:hover {
            background-color: #f8f9fa;
        }
        .pagination .page-link {
            color: #007bff;
        }
        .pagination .page-item.active .page-link {
            background-color: #007bff;
            border-color: #007bff;
        }
        .item-row {
            transition: background-color 0.2s;
        }
        .item-row.selected {
            background-color: #e3f2fd !important;
        }
        mark {
            background-color: #ffeb3b;
            padding: 1px 2px;
            border-radius: 2px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <h2>Nova Requisição de Material</h2>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
            <a href="my_requests.php" class="btn btn-primary">Ver Minhas Requisições</a>
        <?php elseif ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php else: ?>
            <!-- Campo de Pesquisa -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>🔍 Pesquisar Produtos</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="mb-0">
                        <div class="row">
                            <div class="col-md-8">
                                <input type="text" name="search" class="form-control"
                                       placeholder="Digite o nome ou descrição do produto..."
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary btn-block">
                                    🔍 Pesquisar
                                </button>
                            </div>
                        </div>
                        <?php if (!empty($search)): ?>
                        <div class="mt-2">
                            <small class="text-muted">
                                Pesquisando por: "<strong><?php echo htmlspecialchars($search); ?></strong>"
                                - <?php echo $totalItems; ?> produto(s) encontrado(s)
                            </small>
                            <a href="request_form.php" class="btn btn-sm btn-outline-secondary ml-2">
                                ✖ Limpar Pesquisa
                            </a>
                        </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <!-- Informações de Paginação -->
            <?php if ($totalItems > 0): ?>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <small class="text-muted">
                        Mostrando <?php echo min($offset + 1, $totalItems); ?> -
                        <?php echo min($offset + $itemsPerPage, $totalItems); ?> de
                        <?php echo $totalItems; ?> produtos
                    </small>
                </div>
                <div>
                    <small class="text-muted">Página <?php echo $page; ?> de <?php echo $totalPages; ?></small>
                </div>
            </div>
            <?php endif; ?>

            <form method="post">
                <!-- Informações da requisição expandidas -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>📝 Informações da Requisição</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="title">
                                        <i class="fas fa-tag text-primary"></i>
                                        Nome da Requisição *
                                    </label>
                                    <input type="text" name="title" id="title" class="form-control"
                                           placeholder="Ex: Estoque Semanal, Festa Junina, Ingredientes para Pizza..."
                                           maxlength="255">
                                    <small class="text-muted">
                                        Deixe em branco para gerar automaticamente
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                        Prioridade
                                    </label>
                                    <select name="priority" id="priority" class="form-control">
                                        <option value="low">🟢 Baixa - Não urgente</option>
                                        <option value="medium" selected>🟡 Média - Normal</option>
                                        <option value="high">🟠 Alta - Importante</option>
                                        <option value="urgent">🔴 Urgente - Crítica</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="department">
                                        <i class="fas fa-building text-info"></i>
                                        Departamento/Setor
                                    </label>
                                    <input type="text" name="department" id="department" class="form-control"
                                           placeholder="Ex: Cozinha Principal, Confeitaria, Eventos..."
                                           maxlength="100">
                                    <small class="text-muted">
                                        Opcional - Especifique o setor solicitante
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="notes">
                                        <i class="fas fa-sticky-note text-secondary"></i>
                                        Observações
                                    </label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3"
                                              placeholder="Observações adicionais, instruções especiais, etc..."
                                              maxlength="500"></textarea>
                                    <small class="text-muted">
                                        Opcional - Máximo 500 caracteres
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Informação:</strong> Após criar a requisição, será gerado automaticamente um
                            <strong>código interno</strong> e <strong>código de barras</strong> para rastreamento.
                        </div>
                    </div>
                </div>

                <!-- Preservar parâmetros de pesquisa no formulário -->
                <?php if (!empty($search)): ?>
                    <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                <?php endif; ?>
                <input type="hidden" name="page" value="<?php echo $page; ?>">

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th width="25%">Item</th>
                                <th width="35%">Descrição</th>
                                <th width="15%">Unidade</th>
                                <th width="25%">Quantidade</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($items)): ?>
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <?php if (!empty($search)): ?>
                                        🔍 Nenhum produto encontrado para "<strong><?php echo htmlspecialchars($search); ?></strong>"
                                        <br><small>Tente pesquisar com outros termos</small>
                                    <?php else: ?>
                                        📦 Nenhum produto cadastrado no sistema
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php else: ?>
                                <?php foreach ($items as $item): ?>
                                <tr class="item-row" data-item-id="<?php echo $item['id']; ?>">
                                    <td>
                                        <strong><?php echo highlightSearch($item['name'], $search); ?></strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo highlightSearch($item['description'], $search); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">
                                            <?php echo htmlspecialchars($item['unit']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <input type="number" name="items[<?php echo $item['id']; ?>]"
                                               class="form-control form-control-sm quantity-input"
                                               min="0" value="0" onchange="updateRowHighlight(this)">
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <?php if (!empty($items)): ?>
                <!-- Controles de Paginação -->
                <?php if ($totalPages > 1): ?>
                <div class="d-flex justify-content-center mb-4">
                    <nav aria-label="Navegação de páginas">
                        <ul class="pagination">
                            <!-- Primeira página -->
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    ⏮ Primeira
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Página anterior -->
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    ⏪ Anterior
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Páginas numeradas -->
                            <?php
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);

                            for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <!-- Próxima página -->
                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    Próxima ⏩
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- Última página -->
                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                    Última ⏭
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>

                <!-- Botão de Envio -->
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        📋 Enviar Requisição
                    </button>
                    <a href="my_requests.php" class="btn btn-outline-secondary btn-lg ml-2">
                        📄 Minhas Requisições
                    </a>
                </div>
                <?php endif; ?>
            </form>
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Função para destacar linhas com quantidade selecionada
        function updateRowHighlight(input) {
            const row = input.closest('tr');
            const quantity = parseInt(input.value) || 0;

            if (quantity > 0) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }

            updateSelectedCount();
        }

        // Função para contar itens selecionados
        function updateSelectedCount() {
            const selectedInputs = document.querySelectorAll('.quantity-input');
            let selectedCount = 0;
            let totalQuantity = 0;

            selectedInputs.forEach(input => {
                const quantity = parseInt(input.value) || 0;
                if (quantity > 0) {
                    selectedCount++;
                    totalQuantity += quantity;
                }
            });

            // Atualizar contador se existir
            const counter = document.getElementById('selected-counter');
            if (counter) {
                counter.textContent = `${selectedCount} item(ns) selecionado(s) - Total: ${totalQuantity}`;
            }
        }

        // Função para validar formulário antes do envio
        function validateForm() {
            const selectedInputs = document.querySelectorAll('.quantity-input');
            let hasSelection = false;

            selectedInputs.forEach(input => {
                if (parseInt(input.value) > 0) {
                    hasSelection = true;
                }
            });

            if (!hasSelection) {
                alert('⚠️ Selecione pelo menos um item para fazer a requisição!');
                return false;
            }

            return confirm('📋 Confirma o envio da requisição com os itens selecionados?');
        }

        // Adicionar validação ao formulário
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form[method="post"]');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateForm()) {
                        e.preventDefault();
                    }
                });
            }

            // Adicionar contador de itens selecionados
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton) {
                const counter = document.createElement('div');
                counter.id = 'selected-counter';
                counter.className = 'text-center text-muted mt-2';
                counter.textContent = '0 item(ns) selecionado(s) - Total: 0';
                submitButton.parentNode.insertBefore(counter, submitButton);
            }

            // Focar no campo de pesquisa
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });

        // Atalho de teclado para pesquisa (Ctrl+F)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                const searchInput = document.querySelector('input[name="search"]');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            }
        });
    </script>
</body>
</html>