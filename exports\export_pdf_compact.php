<?php
// Exportação compacta para PDF
if (!isset($exportData)) {
    exit('Dados de exportação não encontrados');
}

$request = $exportData['request'];
$items = $exportData['items'];
$statusLabel = $exportData['status_label'];

// Calcular totais
$totalItems = count($items);
$totalQuantity = array_sum(array_column($items, 'quantity'));

// Nome do arquivo
$filename = 'requisicao_' . $request['id'] . '_' . date('Y-m-d') . '.pdf';

// Headers para PDF
header('Content-Type: text/html; charset=UTF-8');
header('Content-Disposition: inline; filename="' . $filename . '"');

// Gerar HTML compacto para PDF
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page { size: A4; margin: 0.8cm; }
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; font-size: 9px; line-height: 1.1; }
        .header { background: #007bff; color: white; padding: 6px; text-align: center; margin-bottom: 6px; }
        .header h1 { margin: 0; font-size: 13px; }
        .header h2 { margin: 2px 0 0 0; font-size: 11px; font-weight: normal; }
        .info-table { width: 100%; border-collapse: collapse; margin-bottom: 6px; font-size: 8px; }
        .info-table td { border: 1px solid #ddd; padding: 2px; }
        .info-label { background: #f8f9fa; font-weight: bold; width: 25%; }
        .items-table { width: 100%; border-collapse: collapse; font-size: 8px; margin-bottom: 8px; }
        .items-table th { background: #007bff; color: white; padding: 3px; border: 1px solid #0056b3; text-align: center; }
        .items-table td { border: 1px solid #ddd; padding: 2px; }
        .items-table tr:nth-child(even) { background: #f8f9fa; }
        .signature-table { width: 100%; border-collapse: collapse; margin-top: 10px; font-size: 8px; }
        .signature-cell { border: 1px solid #ddd; padding: 6px; text-align: center; width: 33.33%; }
        .signature-line { border-bottom: 1px solid #000; margin: 8px 0; height: 1px; }
        .footer { text-align: center; font-size: 7px; color: #666; margin-top: 8px; }
        .status-pending { background: #ffc107; color: #000; padding: 1px 4px; border-radius: 2px; font-size: 8px; font-weight: bold; }
        .status-approved { background: #28a745; color: white; padding: 1px 4px; border-radius: 2px; font-size: 8px; font-weight: bold; }
        .status-rejected { background: #dc3545; color: white; padding: 1px 4px; border-radius: 2px; font-size: 8px; font-weight: bold; }
        .status-delivered { background: #17a2b8; color: white; padding: 1px 4px; border-radius: 2px; font-size: 8px; font-weight: bold; }
        .priority-urgent { color: #dc3545; font-weight: bold; text-decoration: underline; }
        .priority-high { color: #dc3545; font-weight: bold; }
        .priority-medium { color: #ffc107; font-weight: bold; }
        .priority-low { color: #28a745; font-weight: bold; }
        .summary-box { margin-bottom: 8px; padding: 4px; background: #f8f9fa; border-left: 3px solid #007bff; font-size: 8px; }
    </style>
</head>
<body>

    <!-- Cabeçalho -->
    <div class="header">
        <h1>🍽️ REQUISIÇÃO DE MATERIAL</h1>
        <h2>' . htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']) . '</h2>
    </div>

    <!-- Seção: Informações Gerais -->
    <div style="margin-bottom: 6px; padding: 3px; background: #e3f2fd; border-left: 2px solid #007bff; font-size: 9px; font-weight: bold;">
        📋 INFORMAÇÕES GERAIS
    </div>
    <table class="info-table">
        <tr>
            <td class="info-label">ID</td>
            <td>#' . $request['id'] . '</td>
            <td class="info-label">Solicitante</td>
            <td>' . htmlspecialchars($request['username']) . '</td>
        </tr>
        <tr>
            <td class="info-label">Data</td>
            <td>' . date('d/m/Y H:i', strtotime($request['request_date'])) . '</td>
            <td class="info-label">Status</td>
            <td><span class="status-' . $request['status'] . '">' . strtoupper($statusLabel) . '</span></td>
        </tr>';

// Adicionar campos extras se existirem
if (!empty($request['priority']) || !empty($request['department'])) {
    echo '<tr>';
    if (!empty($request['priority'])) {
        $priorityLabels = ['low' => 'Baixa', 'medium' => 'Média', 'high' => 'Alta', 'urgent' => 'URGENTE'];
        $priorityLabel = $priorityLabels[$request['priority']] ?? ucfirst($request['priority']);
        echo '<td class="info-label">Prioridade</td>
              <td><span class="priority-' . $request['priority'] . '">' . $priorityLabel . '</span></td>';
    } else {
        echo '<td></td><td></td>';
    }
    
    if (!empty($request['department'])) {
        echo '<td class="info-label">Departamento</td>
              <td>' . htmlspecialchars($request['department']) . '</td>';
    } else {
        echo '<td></td><td></td>';
    }
    echo '</tr>';
}

echo '</table>';

// Resumo compacto
echo '<div class="summary-box">
    <strong>RESUMO:</strong> ' . $totalItems . ' tipos de itens • ' . $totalQuantity . ' unidades totais • Gerado em ' . date('d/m/Y H:i') . '
</div>';

// Seção: Observações Gerais
echo '<div style="margin-top: 6px; margin-bottom: 4px; padding: 3px; background: #e8f5e8; border-left: 2px solid #28a745; font-size: 9px; font-weight: bold;">
    📝 OBSERVAÇÕES GERAIS
</div>';

if (!empty($request['notes'])) {
    echo '<div style="margin-bottom: 6px; padding: 4px; background: #fff3cd; border-left: 2px solid #ffc107; font-size: 8px;">
        <strong>Observações da Requisição:</strong><br>
        ' . nl2br(htmlspecialchars($request['notes'])) . '
    </div>';
} else {
    echo '<div style="margin-bottom: 6px; padding: 6px; border: 1px solid #ddd; background: #f8f9fa; font-size: 7px; color: #666;">
        <div style="border-bottom: 1px solid #ddd; margin-bottom: 3px; height: 8px;"></div>
        <div style="border-bottom: 1px solid #ddd; margin-bottom: 3px; height: 8px;"></div>
        <div style="border-bottom: 1px solid #ddd; margin-bottom: 3px; height: 8px;"></div>
        <small>Espaço para observações adicionais</small>
    </div>';
}

// Tabela de itens
echo '<table class="items-table">
    <thead>
        <tr>
            <th style="width: 5%;">#</th>
            <th style="width: 35%;">Item</th>
            <th style="width: 45%;">Descrição</th>
            <th style="width: 8%;">Qtd</th>
            <th style="width: 7%;">Un</th>
        </tr>
    </thead>
    <tbody>';

$itemNumber = 1;
foreach ($items as $item) {
    echo '<tr>
        <td style="text-align: center;">' . $itemNumber . '</td>
        <td><strong>' . htmlspecialchars($item['name']) . '</strong></td>
        <td>' . htmlspecialchars(substr($item['description'], 0, 60)) . (strlen($item['description']) > 60 ? '...' : '') . '</td>
        <td style="text-align: center; font-weight: bold;">' . $item['quantity'] . '</td>
        <td style="text-align: center;">' . htmlspecialchars($item['unit']) . '</td>
    </tr>';
    $itemNumber++;
}

echo '</tbody>
</table>';

// Assinaturas compactas
echo '<table class="signature-table">
    <tr>
        <td class="signature-cell">
            <strong>SOLICITANTE</strong><br>
            <div class="signature-line"></div>
            ' . htmlspecialchars($request['username']) . '<br>
            <small>' . date('d/m/Y', strtotime($request['request_date'])) . '</small>
        </td>
        <td class="signature-cell">
            <strong>APROVAÇÃO</strong><br>
            <div class="signature-line"></div>
            _____________________<br>
            <small>___/___/______</small>
        </td>
        <td class="signature-cell">
            <strong>ENTREGA</strong><br>
            <div class="signature-line"></div>
            _____________________<br>
            <small>___/___/______</small>
        </td>
    </tr>
</table>';

// Rodapé
echo '<div class="footer">
    Sistema de Requisição • Documento gerado em ' . date('d/m/Y H:i') . ' • ID: #' . $request['id'] . ' • Status: ' . strtoupper($statusLabel) . '
</div>

<script>
    window.onload = function() {
        window.print();
    }
</script>

</body>
</html>';
?>
