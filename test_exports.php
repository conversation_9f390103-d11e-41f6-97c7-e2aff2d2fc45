<?php
// Arquivo de teste para verificar as exportações
session_start();

// Simular dados de sessão para teste
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['role'] = 'admin';

// Dados de teste
$exportData = [
    'request' => [
        'id' => 1,
        'title' => 'Teste de Requisição',
        'username' => '<PERSON>',
        'user_id' => 1,
        'request_date' => '2024-01-15 10:30:00',
        'status' => 'approved',
        'priority' => 'high',
        'department' => 'Cozinha',
        'notes' => 'Requisição urgente para evento especial'
    ],
    'items' => [
        [
            'name' => 'Arroz Branco',
            'description' => 'Arroz branco tipo 1, pacote de 5kg',
            'quantity' => 10,
            'unit' => 'kg'
        ],
        [
            'name' => 'Feijão Preto',
            'description' => 'Feij<PERSON> preto selecionado, pacote de 1kg',
            'quantity' => 5,
            'unit' => 'kg'
        ],
        [
            'name' => 'Óleo de Soja',
            'description' => 'Óleo de soja refinado, garrafa de 900ml',
            'quantity' => 3,
            'unit' => 'un'
        ],
        [
            'name' => 'Sal Refinado',
            'description' => 'Sal refinado iodado, pacote de 1kg',
            'quantity' => 2,
            'unit' => 'kg'
        ],
        [
            'name' => 'Açúcar Cristal',
            'description' => 'Açúcar cristal especial, pacote de 1kg',
            'quantity' => 4,
            'unit' => 'kg'
        ]
    ],
    'status_label' => 'Aprovada',
    'export_date' => date('d/m/Y H:i:s'),
    'total_items' => 5,
    'total_quantity' => 24
];

// Verificar qual formato foi solicitado
$format = $_GET['format'] ?? 'excel';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Teste de Exportações</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-button { 
            display: inline-block; 
            padding: 10px 20px; 
            margin: 10px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
        }
        .test-button:hover { background: #0056b3; }
        .info-box { 
            background: #f8f9fa; 
            padding: 15px; 
            border-left: 4px solid #007bff; 
            margin: 20px 0; 
        }
        .error-box { 
            background: #f8d7da; 
            padding: 15px; 
            border-left: 4px solid #dc3545; 
            margin: 20px 0; 
            color: #721c24;
        }
        .success-box { 
            background: #d4edda; 
            padding: 15px; 
            border-left: 4px solid #28a745; 
            margin: 20px 0; 
            color: #155724;
        }
    </style>
</head>
<body>
    <div class='test-container'>
        <h1>🧪 Teste de Exportações</h1>
        
        <div class='info-box'>
            <h3>📊 Dados de Teste Carregados:</h3>
            <ul>
                <li><strong>Requisição:</strong> #" . $exportData['request']['id'] . " - " . $exportData['request']['title'] . "</li>
                <li><strong>Solicitante:</strong> " . $exportData['request']['username'] . "</li>
                <li><strong>Status:</strong> " . $exportData['status_label'] . "</li>
                <li><strong>Itens:</strong> " . count($exportData['items']) . " tipos</li>
                <li><strong>Quantidade Total:</strong> " . $exportData['total_quantity'] . " unidades</li>
            </ul>
        </div>
        
        <h3>🔗 Testar Exportações:</h3>
        <a href='test_exports.php?format=excel' class='test-button'>📊 Testar Excel</a>
        <a href='test_exports.php?format=pdf' class='test-button'>📄 Testar PDF</a>
        <a href='test_exports.php?format=docx' class='test-button'>📝 Testar DOCX</a>
        
        <br><br>
        <a href='my_requests.php' class='test-button' style='background: #6c757d;'>← Voltar ao Sistema</a>";

// Se um formato foi solicitado, tentar a exportação
if (isset($_GET['format'])) {
    echo "<div class='info-box'>
            <h3>🔄 Testando Exportação: " . strtoupper($format) . "</h3>
            <p>Tentando gerar arquivo...</p>
          </div>";
    
    try {
        switch ($format) {
            case 'excel':
                echo "<div class='success-box'>✅ Redirecionando para exportação Excel...</div>";
                echo "<script>setTimeout(function(){ window.location.href = 'test_exports.php?format=excel&execute=1'; }, 2000);</script>";
                if (isset($_GET['execute'])) {
                    require_once 'exports/export_excel.php';
                }
                break;
                
            case 'pdf':
                echo "<div class='success-box'>✅ Redirecionando para exportação PDF...</div>";
                echo "<script>setTimeout(function(){ window.location.href = 'test_exports.php?format=pdf&execute=1'; }, 2000);</script>";
                if (isset($_GET['execute'])) {
                    require_once 'exports/export_pdf_compact.php';
                }
                break;
                
            case 'docx':
                echo "<div class='success-box'>✅ Redirecionando para exportação DOCX...</div>";
                echo "<script>setTimeout(function(){ window.location.href = 'test_exports.php?format=docx&execute=1'; }, 2000);</script>";
                if (isset($_GET['execute'])) {
                    require_once 'exports/export_docx_compact.php';
                }
                break;
                
            default:
                echo "<div class='error-box'>❌ Formato não reconhecido: " . htmlspecialchars($format) . "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error-box'>
                <h4>❌ Erro na Exportação:</h4>
                <p><strong>Mensagem:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
                <p><strong>Arquivo:</strong> " . htmlspecialchars($e->getFile()) . "</p>
                <p><strong>Linha:</strong> " . $e->getLine() . "</p>
              </div>";
    }
}

echo "    </div>
</body>
</html>";
?>
