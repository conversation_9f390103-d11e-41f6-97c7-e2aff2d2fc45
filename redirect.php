<?php
/**
 * Arquivo de redirecionamento para compatibilidade
 * Redireciona URLs antigas para o novo sistema de roteamento
 */

// Mapear arquivos antigos para novas páginas
$redirectMap = [
    'index.php' => 'dashboard',
    'request_form.php' => 'nova-requisicao',
    'my_requests.php' => 'minhas-requisicoes',
    'profile.php' => 'perfil',
    'manage_requests.php' => 'gerenciar-requisicoes',
    'manage_items.php' => 'gerenciar-itens',
    'manage_users.php' => 'gerenciar-usuarios',
    'view_request.php' => 'ver-requisicao',
    'edit_request.php' => 'editar-requisicao',
    'view_user.php' => 'ver-usuario',
    'setup_products.php' => 'configurar-produtos',
    'add_kitchen_products.php' => 'adicionar-produtos-cozinha',
    'test_layout.php' => 'teste-layout',
    'debug_layout.php' => 'debug-sistema',
    'test_system.php' => 'verificar-sistema'
];

// Obter arquivo atual
$currentFile = basename($_SERVER['PHP_SELF']);

// Verificar se deve redirecionar
if (isset($redirectMap[$currentFile])) {
    $newPage = $redirectMap[$currentFile];
    
    // Construir nova URL
    $newUrl = 'app.php?page=' . urlencode($newPage);
    
    // Preservar parâmetros GET
    $params = $_GET;
    foreach ($params as $key => $value) {
        if ($key !== 'page') {
            $newUrl .= '&' . urlencode($key) . '=' . urlencode($value);
        }
    }
    
    // Redirecionar permanentemente
    header('Location: ' . $newUrl, true, 301);
    exit;
}

// Se não há redirecionamento, continuar normalmente
?>
