<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';
require_once 'includes/barcode_generator.php';

$id = (int)($_GET['id'] ?? 0);
$type = $_GET['type'] ?? 'request'; // 'request' ou 'item'

if ($id <= 0) {
    header('Location: index.php');
    exit;
}

$data = null;
$title = '';
$code = '';
$barcode = '';

try {
    if ($type === 'request') {
        // Buscar requisição
        $stmt = $pdo->prepare("
            SELECT r.*, u.username 
            FROM requests r 
            JOIN users u ON r.user_id = u.id 
            WHERE r.id = ?
        ");
        $stmt->execute([$id]);
        $data = $stmt->fetch();
        
        if (!$data) {
            throw new Exception('Requisição não encontrada');
        }
        
        // Verificar permissão
        if ($_SESSION['role'] != 'admin' && $data['user_id'] != $_SESSION['user_id']) {
            header('Location: my_requests.php');
            exit;
        }
        
        $title = $data['title'] ?? 'Requisição #' . $data['id'];
        $code = $data['internal_code'] ?? 'REQ' . str_pad($data['id'], 6, '0', STR_PAD_LEFT);
        $barcode = $data['barcode'] ?? generateRequestBarcode($data['id']);
        
    } else {
        // Buscar item
        $stmt = $pdo->prepare("SELECT * FROM items WHERE id = ?");
        $stmt->execute([$id]);
        $data = $stmt->fetch();
        
        if (!$data) {
            throw new Exception('Item não encontrado');
        }
        
        // Verificar permissão (apenas admin pode ver códigos de itens)
        if ($_SESSION['role'] != 'admin') {
            header('Location: index.php');
            exit;
        }
        
        $title = $data['name'];
        $code = $data['internal_code'] ?? 'ITEM' . str_pad($data['id'], 5, '0', STR_PAD_LEFT);
        $barcode = $data['barcode'] ?? generateItemBarcode($data['id']);
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Código de Barras - <?php echo htmlspecialchars($title); ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .barcode-container {
            background: white;
            border: 2px solid #007bff;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .barcode-display {
            font-family: 'Courier New', monospace;
            font-size: 24px;
            letter-spacing: 4px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        .barcode-visual {
            background: linear-gradient(90deg, 
                #000 2px, #fff 2px, #fff 4px, #000 4px, #000 6px, #fff 6px, #fff 8px, #000 8px,
                #000 10px, #fff 10px, #fff 12px, #000 12px, #000 14px, #fff 14px, #fff 16px, #000 16px
            );
            background-size: 16px 100%;
            height: 60px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .qr-code {
            margin: 20px 0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
            .barcode-container { border: 2px solid #000; box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <div class="text-center">
                <a href="index.php" class="btn btn-primary">Voltar ao Sistema</a>
            </div>
        <?php else: ?>
            
            <!-- Navegação -->
            <div class="row no-print mb-4">
                <div class="col-md-6">
                    <h2>
                        <i class="fas fa-barcode text-primary"></i>
                        Código de Barras
                    </h2>
                </div>
                <div class="col-md-6 text-right">
                    <a href="<?php echo $type === 'request' ? ($_SESSION['role'] == 'admin' ? 'manage_requests.php' : 'my_requests.php') : 'manage_items.php'; ?>" 
                       class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </a>
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="fas fa-print"></i> Imprimir
                    </button>
                </div>
            </div>

            <!-- Container do Código de Barras -->
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="barcode-container">
                        
                        <!-- Título -->
                        <h3 class="text-primary mb-4">
                            <?php if ($type === 'request'): ?>
                                <i class="fas fa-clipboard-list"></i>
                                REQUISIÇÃO DE MATERIAL
                            <?php else: ?>
                                <i class="fas fa-box"></i>
                                PRODUTO/ITEM
                            <?php endif; ?>
                        </h3>
                        
                        <!-- Nome/Título -->
                        <h4 class="mb-4"><?php echo htmlspecialchars($title); ?></h4>
                        
                        <!-- Informações -->
                        <div class="info-grid">
                            <div class="info-card">
                                <strong>Código Interno:</strong><br>
                                <span class="h5 text-primary"><?php echo htmlspecialchars($code); ?></span>
                            </div>
                            <div class="info-card">
                                <strong>ID do Sistema:</strong><br>
                                <span class="h5 text-info">#<?php echo $data['id']; ?></span>
                            </div>
                        </div>
                        
                        <?php if ($type === 'request'): ?>
                        <div class="info-grid">
                            <div class="info-card">
                                <strong>Solicitante:</strong><br>
                                <?php echo htmlspecialchars($data['username']); ?>
                            </div>
                            <div class="info-card">
                                <strong>Data:</strong><br>
                                <?php echo date('d/m/Y H:i', strtotime($data['request_date'])); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Código de Barras Visual -->
                        <div class="barcode-visual"></div>
                        
                        <!-- Código de Barras Numérico -->
                        <div class="barcode-display">
                            <?php echo htmlspecialchars($barcode); ?>
                        </div>
                        
                        <!-- QR Code -->
                        <div class="qr-code">
                            <h6>QR Code:</h6>
                            <img src="<?php echo generateQRCode($barcode, 150); ?>" 
                                 alt="QR Code" class="img-fluid">
                        </div>
                        
                        <!-- Informações Adicionais -->
                        <div class="mt-4 text-muted">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                Gerado em: <?php echo date('d/m/Y H:i:s'); ?> | 
                                Sistema de Requisição v2.0
                            </small>
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <!-- Ações Adicionais -->
            <div class="row no-print mt-4">
                <div class="col-md-12 text-center">
                    <div class="btn-group">
                        <?php if ($type === 'request'): ?>
                            <a href="view_request.php?id=<?php echo $data['id']; ?>" class="btn btn-info">
                                <i class="fas fa-eye"></i> Ver Requisição
                            </a>
                            <?php if ($data['status'] === 'pending' && ($_SESSION['role'] == 'admin' || $data['user_id'] == $_SESSION['user_id'])): ?>
                            <a href="edit_request.php?id=<?php echo $data['id']; ?>" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Editar
                            </a>
                            <?php endif; ?>
                        <?php else: ?>
                            <a href="manage_items.php?edit=<?php echo $data['id']; ?>" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Editar Item
                            </a>
                        <?php endif; ?>
                        
                        <button onclick="downloadBarcode()" class="btn btn-success">
                            <i class="fas fa-download"></i> Download
                        </button>
                        
                        <button onclick="shareBarcode()" class="btn btn-primary">
                            <i class="fas fa-share"></i> Compartilhar
                        </button>
                    </div>
                </div>
            </div>
            
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function downloadBarcode() {
            // Criar um canvas para gerar imagem do código de barras
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = 400;
            canvas.height = 200;
            
            // Fundo branco
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Texto do código
            ctx.fillStyle = 'black';
            ctx.font = '16px Courier New';
            ctx.textAlign = 'center';
            ctx.fillText('<?php echo htmlspecialchars($barcode); ?>', canvas.width/2, canvas.height - 20);
            
            // Simular barras
            ctx.fillStyle = 'black';
            for (let i = 0; i < canvas.width; i += 4) {
                if (Math.random() > 0.5) {
                    ctx.fillRect(i, 20, 2, 120);
                }
            }
            
            // Download
            const link = document.createElement('a');
            link.download = '<?php echo $type; ?>_<?php echo $data['id']; ?>_barcode.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function shareBarcode() {
            if (navigator.share) {
                navigator.share({
                    title: '<?php echo htmlspecialchars($title); ?>',
                    text: 'Código: <?php echo htmlspecialchars($barcode); ?>',
                    url: window.location.href
                });
            } else {
                // Fallback: copiar para clipboard
                navigator.clipboard.writeText('<?php echo htmlspecialchars($barcode); ?>').then(() => {
                    alert('Código copiado para a área de transferência!');
                });
            }
        }
    </script>
</body>
</html>
