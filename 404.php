<?php
// Definir status 404
http_response_code(404);

// Iniciar sessão se não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Incluir configuração de página
require_once 'includes/page_config.php';

// Aplicar configuração automática da página
initPage([
    'page_title' => 'Página Não Encontrada',
    'page_subtitle' => 'Erro 404 - A página solicitada não foi encontrada',
    'page_icon' => 'fas fa-exclamation-triangle',
    'meta_description' => 'A página solicitada não foi encontrada no sistema de requisição de material de cozinha. Navegue para outras seções do sistema.',
    'meta_keywords' => 'erro 404, página não encontrada, sistema requisição, navegação',
    'meta_robots' => 'noindex, nofollow'
]);

// Incluir layout
require_once 'includes/layout.php';
?>
<!-- Página de Erro 404 -->
<div class="content-card text-center">
    <div class="mb-4">
        <i class="fas fa-exclamation-triangle fa-5x text-warning mb-3"></i>
        <h1 class="display-1 text-muted">404</h1>
        <h2 class="mb-3">Página Não Encontrada</h2>
        <p class="lead text-muted mb-4">
            Ops! A página que você está procurando não existe ou foi movida.
            Isso pode acontecer quando um link está quebrado ou você digitou um endereço incorreto.
        </p>
    </div>

    <div class="d-grid gap-2 d-md-flex justify-content-md-center mb-4">
        <?php if (isset($_SESSION['user_id'])): ?>
            <a href="?page=dashboard" class="btn btn-primary btn-custom btn-lg">
                <i class="fas fa-home me-2"></i>Dashboard
            </a>
        <?php else: ?>
            <a href="login.php" class="btn btn-primary btn-custom btn-lg">
                <i class="fas fa-sign-in-alt me-2"></i>Fazer Login
            </a>
        <?php endif; ?>
        <button onclick="history.back()" class="btn btn-secondary btn-custom btn-lg">
            <i class="fas fa-arrow-left me-2"></i>Voltar
        </button>
    </div>
</div>

<!-- Sugestões de Navegação -->
<div class="content-card">
    <h3 class="mb-4">
        <i class="fas fa-lightbulb text-warning me-2"></i>
        Sugestões de Navegação
    </h3>

    <div class="row">
        <?php if (isset($_SESSION['user_id'])): ?>
            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-plus fa-2x text-primary mb-3"></i>
                        <h5 class="card-title">Nova Requisição</h5>
                        <p class="card-text text-muted">Criar uma nova solicitação de material</p>
                        <a href="?page=nova-requisicao" class="btn btn-primary btn-custom">Acessar</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-list fa-2x text-info mb-3"></i>
                        <h5 class="card-title">Minhas Requisições</h5>
                        <p class="card-text text-muted">Ver histórico de solicitações</p>
                        <a href="?page=minhas-requisicoes" class="btn btn-info btn-custom">Acessar</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-user fa-2x text-success mb-3"></i>
                        <h5 class="card-title">Meu Perfil</h5>
                        <p class="card-text text-muted">Gerenciar informações pessoais</p>
                        <a href="?page=perfil" class="btn btn-success btn-custom">Acessar</a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="col-md-6 mb-3">
                <div class="card h-100 border-0 bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-sign-in-alt fa-2x text-primary mb-3"></i>
                        <h5 class="card-title">Fazer Login</h5>
                        <p class="card-text text-muted">Acessar o sistema com suas credenciais</p>
                        <a href="login.php" class="btn btn-primary btn-custom">Acessar</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card h-100 border-0 bg-light">
                    <div class="card-body text-center">
                        <i class="fas fa-info-circle fa-2x text-info mb-3"></i>
                        <h5 class="card-title">Sobre o Sistema</h5>
                        <p class="card-text text-muted">Informações sobre o sistema de requisições</p>
                        <a href="about.php" class="btn btn-info btn-custom">Acessar</a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Informações Adicionais -->
<div class="content-card">
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Precisa de ajuda?</strong> Se você acredita que isso é um erro, entre em contato com o administrador do sistema.
    </div>
</div>
<?php
// Scripts específicos da página
$page_scripts = '
<script>
// Log do erro 404 para análise
console.log("404 Error - Page not found:", window.location.href);

// Focar no botão principal após carregamento
document.addEventListener("DOMContentLoaded", function() {
    const homeButton = document.querySelector(".btn-primary");
    if (homeButton) {
        homeButton.focus();
    }
});
</script>
';

// Incluir footer
require_once 'includes/layout_footer.php';
?>
