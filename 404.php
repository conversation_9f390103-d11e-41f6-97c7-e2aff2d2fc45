<?php
// Definir status 404
http_response_code(404);

// Configurações SEO para página 404
$page_config = [
    'title' => 'Página Não Encontrada - Sistema de Requisição',
    'description' => 'A página solicitada não foi encontrada no sistema de requisição de material de cozinha. Navegue para outras seções do sistema.',
    'keywords' => 'erro 404, página não encontrada, sistema requisição, navegação'
];
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="<?php echo htmlspecialchars($page_config['description']); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($page_config['keywords']); ?>">
    <meta name="robots" content="noindex, nofollow">
    <title><?php echo htmlspecialchars($page_config['title']); ?></title>
    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }
        .error-number {
            font-size: 8rem;
            font-weight: bold;
            color: #007bff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            line-height: 1;
        }
        .error-icon {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 2rem;
        }
        .error-title {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
        }
        .error-description {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .btn-home {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 123, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        .btn-secondary-custom {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        .btn-secondary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
            color: white;
            text-decoration: none;
        }
        .suggestions {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            text-align: left;
        }
        .suggestion-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: background-color 0.2s;
        }
        .suggestion-item:hover {
            background-color: #e9ecef;
        }
        .suggestion-icon {
            font-size: 1.5rem;
            color: #007bff;
            margin-right: 1rem;
            width: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-search" aria-hidden="true"></i>
            </div>
            
            <div class="error-number" aria-label="Erro 404">404</div>
            
            <h1 class="error-title">Página Não Encontrada</h1>
            
            <p class="error-description">
                Ops! A página que você está procurando não existe ou foi movida. 
                Isso pode acontecer quando um link está quebrado ou você digitou um endereço incorreto.
            </p>
            
            <div class="mb-4">
                <a href="index.php" class="btn-home">
                    <i class="fas fa-home" aria-hidden="true"></i>
                    Página Inicial
                </a>
                <a href="javascript:history.back()" class="btn-secondary-custom">
                    <i class="fas fa-arrow-left" aria-hidden="true"></i>
                    Voltar
                </a>
            </div>
            
            <div class="suggestions">
                <h3 class="mb-3">
                    <i class="fas fa-lightbulb text-warning" aria-hidden="true"></i>
                    Sugestões de Navegação
                </h3>
                
                <div class="suggestion-item">
                    <div class="suggestion-icon">
                        <i class="fas fa-plus" aria-hidden="true"></i>
                    </div>
                    <div>
                        <strong>Nova Requisição</strong><br>
                        <small class="text-muted">Criar uma nova solicitação de material</small><br>
                        <a href="request_form.php" class="text-primary">Acessar →</a>
                    </div>
                </div>
                
                <div class="suggestion-item">
                    <div class="suggestion-icon">
                        <i class="fas fa-list" aria-hidden="true"></i>
                    </div>
                    <div>
                        <strong>Minhas Requisições</strong><br>
                        <small class="text-muted">Ver histórico de solicitações</small><br>
                        <a href="my_requests.php" class="text-primary">Acessar →</a>
                    </div>
                </div>
                
                <div class="suggestion-item">
                    <div class="suggestion-icon">
                        <i class="fas fa-sign-in-alt" aria-hidden="true"></i>
                    </div>
                    <div>
                        <strong>Fazer Login</strong><br>
                        <small class="text-muted">Acessar o sistema com suas credenciais</small><br>
                        <a href="login.php" class="text-primary">Acessar →</a>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <small class="text-muted">
                    <i class="fas fa-info-circle" aria-hidden="true"></i>
                    Se você acredita que isso é um erro, entre em contato com o administrador do sistema.
                </small>
            </div>
        </div>
    </div>
    
    <script>
        // Log do erro 404 para análise (opcional)
        console.log('404 Error - Page not found:', window.location.href);
        
        // Focar no botão principal após carregamento
        document.addEventListener('DOMContentLoaded', function() {
            const homeButton = document.querySelector('.btn-home');
            if (homeButton) {
                homeButton.focus();
            }
        });
    </script>
    
    <!-- Schema.org para página de erro -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Página Não Encontrada - 404",
        "description": "Página de erro 404 do sistema de requisição de material de cozinha",
        "url": "<?php echo 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>",
        "mainEntity": {
            "@type": "Thing",
            "name": "Erro 404",
            "description": "Página não encontrada"
        }
    }
    </script>
</body>
</html>
