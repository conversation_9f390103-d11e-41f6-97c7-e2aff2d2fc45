<?php
// Teste do novo sistema de layout
session_start();

// Simular usuário logado para teste
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'teste';
    $_SESSION['role'] = 'admin';
}

// Incluir configuração de página
require_once 'includes/page_config.php';

// Configuração customizada para teste
initPage([
    'page_title' => 'Teste de Layout',
    'page_subtitle' => 'Testando o novo sistema padronizado',
    'page_icon' => 'fas fa-flask',
]);

// Incluir layout
require_once 'includes/layout.php';
?>

<!-- Teste do Layout Padronizado -->
<div class="content-card">
    <h2>
        <i class="fas fa-flask text-primary me-2"></i>
        Teste do Sistema de Layout
    </h2>
    <p class="lead">Este é um teste do novo sistema de layout padronizado.</p>
    
    <div class="row">
        <div class="col-md-6">
            <h4>✅ Funcionalidades Testadas:</h4>
            <ul class="list-group">
                <li class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>
                    Configuração automática de página
                </li>
                <li class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>
                    Header responsivo
                </li>
                <li class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>
                    Sidebar com overlay mobile
                </li>
                <li class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>
                    Layout padronizado
                </li>
                <li class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>
                    Sistema de configuração centralizada
                </li>
            </ul>
        </div>
        <div class="col-md-6">
            <h4>📱 Teste de Responsividade:</h4>
            <div class="alert alert-info">
                <i class="fas fa-mobile-alt me-2"></i>
                <strong>Instruções:</strong>
                <ol class="mb-0 mt-2">
                    <li>Redimensione a janela para testar mobile</li>
                    <li>Clique no botão ☰ para abrir a sidebar</li>
                    <li>Teste o overlay clicando fora da sidebar</li>
                    <li>Pressione ESC para fechar a sidebar</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Teste de Cards -->
<div class="content-card">
    <h3>
        <i class="fas fa-th-large text-info me-2"></i>
        Teste de Cards Responsivos
    </h3>
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-desktop fa-2x mb-3"></i>
                    <h5 class="card-title">Desktop</h5>
                    <p class="card-text">Layout completo com sidebar fixa</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-tablet-alt fa-2x mb-3"></i>
                    <h5 class="card-title">Tablet</h5>
                    <p class="card-text">Layout adaptado para telas médias</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-mobile-alt fa-2x mb-3"></i>
                    <h5 class="card-title">Mobile</h5>
                    <p class="card-text">Sidebar com overlay e header compacto</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Teste de Formulário -->
<div class="content-card">
    <h3>
        <i class="fas fa-edit text-warning me-2"></i>
        Teste de Formulário
    </h3>
    <form>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="test_input" class="form-label">Campo de Teste</label>
                <input type="text" class="form-control" id="test_input" placeholder="Digite algo...">
            </div>
            <div class="col-md-6 mb-3">
                <label for="test_select" class="form-label">Seleção de Teste</label>
                <select class="form-select" id="test_select">
                    <option selected>Escolha uma opção...</option>
                    <option value="1">Opção 1</option>
                    <option value="2">Opção 2</option>
                    <option value="3">Opção 3</option>
                </select>
            </div>
        </div>
        <div class="mb-3">
            <label for="test_textarea" class="form-label">Área de Texto</label>
            <textarea class="form-control" id="test_textarea" rows="3" placeholder="Digite uma mensagem..."></textarea>
        </div>
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <button type="button" class="btn btn-secondary me-md-2">Cancelar</button>
            <button type="submit" class="btn btn-primary">Enviar Teste</button>
        </div>
    </form>
</div>

<!-- Teste de Alertas -->
<div class="content-card">
    <h3>
        <i class="fas fa-bell text-danger me-2"></i>
        Teste de Alertas
    </h3>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <strong>Sucesso!</strong> O sistema de layout está funcionando perfeitamente.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Informação:</strong> Este alerta será fechado automaticamente em 5 segundos.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Atenção:</strong> Teste a responsividade redimensionando a janela.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>

<!-- Teste de Botões -->
<div class="content-card">
    <h3>
        <i class="fas fa-mouse-pointer text-secondary me-2"></i>
        Teste de Botões e Interações
    </h3>
    <div class="row">
        <div class="col-md-6 mb-3">
            <h5>Botões Padrão:</h5>
            <div class="d-flex flex-wrap gap-2">
                <button type="button" class="btn btn-primary btn-custom">Primário</button>
                <button type="button" class="btn btn-secondary btn-custom">Secundário</button>
                <button type="button" class="btn btn-success btn-custom">Sucesso</button>
                <button type="button" class="btn btn-danger btn-custom">Perigo</button>
                <button type="button" class="btn btn-warning btn-custom">Aviso</button>
                <button type="button" class="btn btn-info btn-custom">Info</button>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <h5>Botões com Ícones:</h5>
            <div class="d-flex flex-wrap gap-2">
                <button type="button" class="btn btn-outline-primary btn-custom">
                    <i class="fas fa-plus me-1"></i>Adicionar
                </button>
                <button type="button" class="btn btn-outline-success btn-custom">
                    <i class="fas fa-save me-1"></i>Salvar
                </button>
                <button type="button" class="btn btn-outline-danger btn-custom">
                    <i class="fas fa-trash me-1"></i>Excluir
                </button>
            </div>
        </div>
    </div>
</div>

<?php
// Scripts específicos da página de teste
$page_scripts = '
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Teste de toast
    setTimeout(function() {
        if (typeof showToast === "function") {
            showToast("Sistema de layout carregado com sucesso!", "success");
        }
    }, 1000);
    
    // Teste de validação de formulário
    const form = document.querySelector("form");
    if (form) {
        form.addEventListener("submit", function(e) {
            e.preventDefault();
            if (typeof showToast === "function") {
                showToast("Formulário de teste enviado!", "info");
            }
        });
    }
    
    // Log de teste no console
    console.log("✅ Sistema de layout padronizado carregado");
    console.log("📱 Teste a responsividade redimensionando a janela");
    console.log("🎯 Clique no botão ☰ para testar a sidebar mobile");
});
</script>
';

// Incluir footer do layout
require_once 'includes/layout_footer.php';
?>
