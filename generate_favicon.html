<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerador de Favicon - Sistema de Requisição</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
        }
        .favicon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .preview-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .preview-item h3 {
            margin: 0 0 15px 0;
            color: #007bff;
        }
        .favicon-display {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 80px;
        }
        .size-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        .download-section {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .code-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        canvas {
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍽️ Gerador de Favicon</h1>
            <p>Sistema de Requisição de Material de Cozinha</p>
        </div>

        <div class="instructions">
            <h3>📋 Instruções:</h3>
            <ol>
                <li>Visualize os favicons gerados abaixo</li>
                <li>Clique nos botões de download para baixar os arquivos</li>
                <li>Copie o código HTML fornecido</li>
                <li>Adicione os arquivos à pasta <code>assets/images/</code></li>
                <li>Inclua o código HTML no <code>&lt;head&gt;</code> das páginas</li>
            </ol>
        </div>

        <div class="favicon-preview">
            <!-- 16x16 -->
            <div class="preview-item">
                <h3>Favicon 16x16</h3>
                <div class="favicon-display">
                    <canvas id="favicon16" width="16" height="16"></canvas>
                </div>
                <div class="size-label">16x16 pixels - Aba do navegador</div>
                <button class="btn" onclick="downloadFavicon('favicon16', 'favicon-16x16.png')">
                    📥 Download PNG
                </button>
            </div>

            <!-- 32x32 -->
            <div class="preview-item">
                <h3>Favicon 32x32</h3>
                <div class="favicon-display">
                    <canvas id="favicon32" width="32" height="32"></canvas>
                </div>
                <div class="size-label">32x32 pixels - Aba do navegador (HD)</div>
                <button class="btn" onclick="downloadFavicon('favicon32', 'favicon-32x32.png')">
                    📥 Download PNG
                </button>
            </div>

            <!-- 48x48 -->
            <div class="preview-item">
                <h3>Favicon 48x48</h3>
                <div class="favicon-display">
                    <canvas id="favicon48" width="48" height="48"></canvas>
                </div>
                <div class="size-label">48x48 pixels - Favoritos</div>
                <button class="btn" onclick="downloadFavicon('favicon48', 'favicon-48x48.png')">
                    📥 Download PNG
                </button>
            </div>

            <!-- 64x64 -->
            <div class="preview-item">
                <h3>Favicon 64x64</h3>
                <div class="favicon-display">
                    <canvas id="favicon64" width="64" height="64"></canvas>
                </div>
                <div class="size-label">64x64 pixels - Desktop</div>
                <button class="btn" onclick="downloadFavicon('favicon64', 'favicon-64x64.png')">
                    📥 Download PNG
                </button>
            </div>

            <!-- 128x128 -->
            <div class="preview-item">
                <h3>Favicon 128x128</h3>
                <div class="favicon-display">
                    <canvas id="favicon128" width="128" height="128"></canvas>
                </div>
                <div class="size-label">128x128 pixels - Chrome Web Store</div>
                <button class="btn" onclick="downloadFavicon('favicon128', 'favicon-128x128.png')">
                    📥 Download PNG
                </button>
            </div>

            <!-- 180x180 -->
            <div class="preview-item">
                <h3>Apple Touch Icon</h3>
                <div class="favicon-display">
                    <canvas id="favicon180" width="180" height="180"></canvas>
                </div>
                <div class="size-label">180x180 pixels - iOS Safari</div>
                <button class="btn" onclick="downloadFavicon('favicon180', 'apple-touch-icon.png')">
                    📥 Download PNG
                </button>
            </div>
        </div>

        <div class="download-section">
            <h3>📦 Download Completo</h3>
            <p>Baixe todos os favicons de uma vez:</p>
            <button class="btn" onclick="downloadAllFavicons()">
                📥 Download Todos os Favicons
            </button>
            <button class="btn" onclick="generateICO()">
                📥 Gerar favicon.ico
            </button>
        </div>

        <div class="code-section">
            <h3>💻 Código HTML para incluir no &lt;head&gt;</h3>
            <div class="code">
&lt;!-- Favicons --&gt;
&lt;link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png"&gt;
&lt;link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png"&gt;
&lt;link rel="icon" type="image/png" sizes="48x48" href="assets/images/favicon-48x48.png"&gt;
&lt;link rel="icon" type="image/png" sizes="64x64" href="assets/images/favicon-64x64.png"&gt;
&lt;link rel="icon" type="image/png" sizes="128x128" href="assets/images/favicon-128x128.png"&gt;

&lt;!-- Apple Touch Icon --&gt;
&lt;link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png"&gt;

&lt;!-- Favicon tradicional --&gt;
&lt;link rel="shortcut icon" href="assets/images/favicon.ico"&gt;

&lt;!-- Manifest para PWA --&gt;
&lt;link rel="manifest" href="assets/images/site.webmanifest"&gt;

&lt;!-- Meta tags para mobile --&gt;
&lt;meta name="theme-color" content="#007bff"&gt;
&lt;meta name="msapplication-TileColor" content="#007bff"&gt;
&lt;meta name="msapplication-config" content="assets/images/browserconfig.xml"&gt;
            </div>
        </div>
    </div>

    <script>
        // Função para desenhar o favicon
        function drawFavicon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 64; // Escala baseada no tamanho original de 64px
            
            // Limpar canvas
            ctx.clearRect(0, 0, size, size);
            
            // Fundo circular com gradiente
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#007bff');
            gradient.addColorStop(1, '#0056b3');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, (size/2) - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Prato
            ctx.fillStyle = '#ffffff';
            ctx.globalAlpha = 0.9;
            ctx.beginPath();
            ctx.arc(size/2, size * 0.55, size * 0.23, 0, 2 * Math.PI);
            ctx.fill();
            ctx.globalAlpha = 1;
            
            // Utensílios (simplificados para tamanhos pequenos)
            ctx.fillStyle = '#28a745';
            
            if (size >= 32) {
                // Garfo (esquerda)
                ctx.fillRect(size * 0.31, size * 0.23, size * 0.03, size * 0.28);
                ctx.fillRect(size * 0.30, size * 0.23, size * 0.015, size * 0.12);
                ctx.fillRect(size * 0.325, size * 0.23, size * 0.015, size * 0.12);
                
                // Faca (direita)
                ctx.fillRect(size * 0.66, size * 0.23, size * 0.03, size * 0.28);
                ctx.beginPath();
                ctx.moveTo(size * 0.64, size * 0.23);
                ctx.lineTo(size * 0.64, size * 0.34);
                ctx.lineTo(size * 0.61, size * 0.31);
                ctx.lineTo(size * 0.61, size * 0.27);
                ctx.closePath();
                ctx.fill();
                
                // Colher (centro)
                ctx.globalAlpha = 0.8;
                ctx.fillRect(size * 0.485, size * 0.28, size * 0.03, size * 0.23);
                ctx.beginPath();
                ctx.ellipse(size/2, size * 0.25, size * 0.04, size * 0.06, 0, 0, 2 * Math.PI);
                ctx.fill();
                ctx.globalAlpha = 1;
            } else {
                // Versão ultra simplificada para 16x16
                ctx.fillRect(size * 0.35, size * 0.3, size * 0.08, size * 0.25);
                ctx.fillRect(size * 0.57, size * 0.3, size * 0.08, size * 0.25);
            }
            
            // Texto "SR" (apenas para tamanhos maiores)
            if (size >= 32) {
                ctx.fillStyle = '#007bff';
                ctx.font = `bold ${size * 0.125}px Arial`;
                ctx.textAlign = 'center';
                ctx.globalAlpha = 0.8;
                ctx.fillText('SR', size/2, size * 0.66);
                ctx.globalAlpha = 1;
            }
        }

        // Gerar todos os favicons
        function generateFavicons() {
            const sizes = [16, 32, 48, 64, 128, 180];
            sizes.forEach(size => {
                const canvas = document.getElementById(`favicon${size}`);
                if (canvas) {
                    drawFavicon(canvas, size);
                }
            });
        }

        // Download individual
        function downloadFavicon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // Download todos
        function downloadAllFavicons() {
            const sizes = [
                {id: 'favicon16', name: 'favicon-16x16.png'},
                {id: 'favicon32', name: 'favicon-32x32.png'},
                {id: 'favicon48', name: 'favicon-48x48.png'},
                {id: 'favicon64', name: 'favicon-64x64.png'},
                {id: 'favicon128', name: 'favicon-128x128.png'},
                {id: 'favicon180', name: 'apple-touch-icon.png'}
            ];
            
            sizes.forEach((item, index) => {
                setTimeout(() => {
                    downloadFavicon(item.id, item.name);
                }, index * 500); // Delay para evitar bloqueio do navegador
            });
        }

        // Gerar ICO (simulado - na verdade baixa o 32x32)
        function generateICO() {
            downloadFavicon('favicon32', 'favicon.ico');
        }

        // Inicializar quando a página carregar
        window.addEventListener('load', generateFavicons);
    </script>
</body>
</html>
