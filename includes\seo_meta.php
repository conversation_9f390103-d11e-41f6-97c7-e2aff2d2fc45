<?php
// Configurações SEO para o Sistema de Requisição de Material de Cozinha

// Configurações padrão
$default_title = "Sistema de Requisição de Material de Cozinha";
$default_description = "Sistema profissional para gerenciamento de requisições de material de cozinha. Controle completo de estoque, aprovações e entregas com interface moderna e relatórios detalhados.";
$default_keywords = "requisição, material, cozinha, estoque, gestão, controle, sistema, aprovação, entrega, relatórios";
$site_name = "Sistema de Requisição";
$site_url = "http" . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "s" : "") . "://" . $_SERVER['HTTP_HOST'] . rtrim(dirname($_SERVER['PHP_SELF']), '/');

// Configurações específicas por página
$seo_config = [
    'index.php' => [
        'title' => 'Página Inicial - Sistema de Requisição de Material de Cozinha',
        'description' => 'Sistema completo para gerenciamento de requisições de material de cozinha. Faça login para acessar funcionalidades de criação, aprovação e controle de requisições.',
        'keywords' => 'login, sistema requisição, material cozinha, acesso, autenticação, gestão estoque'
    ],
    'login.php' => [
        'title' => 'Login - Acesso ao Sistema de Requisição',
        'description' => 'Faça login no sistema de requisição de material de cozinha. Acesso seguro para funcionários e administradores.',
        'keywords' => 'login, acesso, autenticação, sistema, segurança, usuário, senha'
    ],
    'request_form.php' => [
        'title' => 'Nova Requisição - Solicitar Material de Cozinha',
        'description' => 'Crie uma nova requisição de material de cozinha. Interface intuitiva para seleção de itens, quantidades e envio de solicitações.',
        'keywords' => 'nova requisição, solicitar material, criar pedido, itens cozinha, formulário'
    ],
    'my_requests.php' => [
        'title' => 'Minhas Requisições - Histórico de Solicitações',
        'description' => 'Visualize e gerencie suas requisições de material de cozinha. Acompanhe status, edite requisições pendentes e exporte relatórios.',
        'keywords' => 'minhas requisições, histórico, status, acompanhar, editar, exportar'
    ],
    'edit_request.php' => [
        'title' => 'Editar Requisição - Modificar Solicitação',
        'description' => 'Edite requisições pendentes de material de cozinha. Altere itens, quantidades e nome da requisição com interface moderna.',
        'keywords' => 'editar requisição, modificar, alterar itens, requisição pendente'
    ],
    'view_request.php' => [
        'title' => 'Detalhes da Requisição - Visualizar Solicitação',
        'description' => 'Visualize detalhes completos da requisição de material de cozinha. Informações detalhadas, status e opções de exportação.',
        'keywords' => 'detalhes requisição, visualizar, informações, status, exportar'
    ],
    'manage_requests.php' => [
        'title' => 'Gerenciar Requisições - Administração do Sistema',
        'description' => 'Painel administrativo para gerenciar todas as requisições. Aprovar, rejeitar, marcar como entregue e exportar relatórios.',
        'keywords' => 'gerenciar requisições, administração, aprovar, rejeitar, entregar, admin'
    ],
    'manage_items.php' => [
        'title' => 'Gerenciar Itens - Controle de Estoque',
        'description' => 'Gerencie itens de material de cozinha. Adicione, edite e remova produtos do sistema com controle completo.',
        'keywords' => 'gerenciar itens, estoque, produtos, material cozinha, adicionar, editar'
    ],
    'manage_users.php' => [
        'title' => 'Gerenciar Usuários - Administração de Acesso',
        'description' => 'Administre usuários do sistema. Criar, editar, visualizar e controlar acesso de funcionários e administradores.',
        'keywords' => 'gerenciar usuários, administração, criar usuário, controle acesso, funcionários'
    ],
    'view_user.php' => [
        'title' => 'Detalhes do Usuário - Perfil e Estatísticas',
        'description' => 'Visualize perfil completo do usuário com estatísticas de requisições, histórico e informações detalhadas.',
        'keywords' => 'perfil usuário, estatísticas, histórico requisições, detalhes'
    ],
    'setup_products.php' => [
        'title' => 'Configurar Produtos - Setup do Sistema',
        'description' => 'Configure produtos e itens iniciais do sistema. Setup completo para começar a usar o sistema de requisições.',
        'keywords' => 'configurar produtos, setup, configuração inicial, itens sistema'
    ]
];

// Obter página atual
$current_page = basename($_SERVER['PHP_SELF']);

// Usar configurações específicas ou padrão
$page_config = $seo_config[$current_page] ?? [
    'title' => $default_title,
    'description' => $default_description,
    'keywords' => $default_keywords
];

// Função para gerar meta tags
function generateSEOTags($config, $site_name, $site_url) {
    $current_url = $site_url . '/' . basename($_SERVER['PHP_SELF']);
    
    echo '<!-- Meta Tags SEO -->' . "\n";
    echo '<meta charset="UTF-8">' . "\n";
    echo '<meta name="viewport" content="width=device-width, initial-scale=1.0">' . "\n";
    echo '<meta http-equiv="X-UA-Compatible" content="IE=edge">' . "\n";
    
    // Meta tags básicas
    echo '<meta name="description" content="' . htmlspecialchars($config['description']) . '">' . "\n";
    echo '<meta name="keywords" content="' . htmlspecialchars($config['keywords']) . '">' . "\n";
    echo '<meta name="author" content="Sistema de Requisição de Material de Cozinha">' . "\n";
    echo '<meta name="robots" content="noindex, nofollow">' . "\n"; // Sistema interno
    echo '<meta name="language" content="pt-BR">' . "\n";
    
    // Open Graph (Facebook)
    echo '<meta property="og:type" content="website">' . "\n";
    echo '<meta property="og:title" content="' . htmlspecialchars($config['title']) . '">' . "\n";
    echo '<meta property="og:description" content="' . htmlspecialchars($config['description']) . '">' . "\n";
    echo '<meta property="og:url" content="' . htmlspecialchars($current_url) . '">' . "\n";
    echo '<meta property="og:site_name" content="' . htmlspecialchars($site_name) . '">' . "\n";
    echo '<meta property="og:locale" content="pt_BR">' . "\n";
    
    // Twitter Cards
    echo '<meta name="twitter:card" content="summary">' . "\n";
    echo '<meta name="twitter:title" content="' . htmlspecialchars($config['title']) . '">' . "\n";
    echo '<meta name="twitter:description" content="' . htmlspecialchars($config['description']) . '">' . "\n";
    
    // Canonical URL
    echo '<link rel="canonical" href="' . htmlspecialchars($current_url) . '">' . "\n";
    
    // Favicon e ícones
    echo '<link rel="icon" type="image/x-icon" href="./images/favicon.ico">' . "\n";
    echo '<link rel="apple-touch-icon" href="./images/apple-touch-icon.png">' . "\n";
    
    // Título da página
    echo '<title>' . htmlspecialchars($config['title']) . '</title>' . "\n";
}

// Função para gerar dados estruturados JSON-LD
function generateStructuredData($config, $site_name, $site_url) {
    $current_url = $site_url . '/' . basename($_SERVER['PHP_SELF']);
    
    $structured_data = [
        "@context" => "https://schema.org",
        "@type" => "WebApplication",
        "name" => $site_name,
        "description" => $config['description'],
        "url" => $current_url,
        "applicationCategory" => "BusinessApplication",
        "operatingSystem" => "Web Browser",
        "inLanguage" => "pt-BR",
        "author" => [
            "@type" => "Organization",
            "name" => "Sistema de Requisição"
        ],
        "offers" => [
            "@type" => "Offer",
            "price" => "0",
            "priceCurrency" => "BRL"
        ]
    ];
    
    echo '<script type="application/ld+json">' . "\n";
    echo json_encode($structured_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "\n" . '</script>' . "\n";
}

// Gerar as tags
generateSEOTags($page_config, $site_name, $site_url);
generateStructuredData($page_config, $site_name, $site_url);
?>
