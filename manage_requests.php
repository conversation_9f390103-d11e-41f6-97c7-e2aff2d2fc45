<?php
// Iniciar sessão se não foi iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se usuário está logado e é admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

// Incluir configuração de página
require_once 'includes/page_config.php';

// Aplicar configuração automática da página
initPage();

// Incluir layout
require_once 'includes/layout.php';

// Incluir conexão com banco
require_once 'config/db_connect.php';

$message = '';

// Processar ações
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $id = (int)$_GET['id'];
    $notes = trim($_GET['notes'] ?? '');
    $reason = trim($_GET['reason'] ?? '');

    if (in_array($action, ['approve', 'reject', 'deliver'])) {
        $status = [
            'approve' => 'approved',
            'reject' => 'rejected',
            'deliver' => 'delivered'
        ][$action];

        try {
            $pdo->beginTransaction();

            // Atualizar status da requisição
            $updateFields = ['status = ?'];
            $updateParams = [$status];

            // Adicionar campos de auditoria se existirem
            if ($action === 'approve') {
                // Tentar adicionar campos de aprovação
                try {
                    $updateFields[] = 'approved_by = ?';
                    $updateFields[] = 'approved_date = NOW()';
                    $updateParams[] = $_SESSION['user_id'];
                } catch (Exception $e) {
                    // Campos não existem, continuar sem eles
                }
            } elseif ($action === 'deliver') {
                // Tentar adicionar campos de entrega
                try {
                    $updateFields[] = 'delivered_by = ?';
                    $updateFields[] = 'delivered_date = NOW()';
                    $updateParams[] = $_SESSION['user_id'];
                } catch (Exception $e) {
                    // Campos não existem, continuar sem eles
                }
            }

            $updateParams[] = $id;
            $sql = "UPDATE requests SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($updateParams);

            // Registrar log da ação (se tabela existir)
            try {
                $logData = [
                    'request_id' => $id,
                    'action' => $action,
                    'user_id' => $_SESSION['user_id'],
                    'notes' => $notes,
                    'reason' => $reason,
                    'action_date' => date('Y-m-d H:i:s')
                ];

                $logStmt = $pdo->prepare("
                    INSERT INTO request_logs (request_id, action, user_id, notes, reason, action_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $logStmt->execute([$logData['request_id'], $logData['action'], $logData['user_id'],
                                 $logData['notes'], $logData['reason'], $logData['action_date']]);
            } catch (Exception $e) {
                // Tabela de logs não existe, continuar sem log
            }

            $pdo->commit();

            // Buscar nome da requisição para a mensagem
            $nameStmt = $pdo->prepare("SELECT title, username FROM requests r JOIN users u ON r.user_id = u.id WHERE r.id = ?");
            $nameStmt->execute([$id]);
            $requestData = $nameStmt->fetch();
            $displayName = $requestData['title'] ? htmlspecialchars($requestData['title']) : "Requisição #$id";

            // Mensagens personalizadas por ação
            switch ($action) {
                case 'approve':
                    $message = "✅ <strong>$displayName</strong> foi aprovada com sucesso!";
                    if ($notes) {
                        $message .= "<br><small><strong>Observações:</strong> " . htmlspecialchars($notes) . "</small>";
                    }
                    break;
                case 'reject':
                    $message = "❌ <strong>$displayName</strong> foi rejeitada.";
                    if ($reason) {
                        $reasonLabels = [
                            'itens_indisponiveis' => 'Itens indisponíveis no estoque',
                            'orcamento_insuficiente' => 'Orçamento insuficiente',
                            'requisicao_duplicada' => 'Requisição duplicada',
                            'itens_desnecessarios' => 'Itens considerados desnecessários',
                            'fora_prazo' => 'Solicitação fora do prazo',
                            'informacoes_incompletas' => 'Informações incompletas',
                            'outro' => 'Outro motivo'
                        ];
                        $message .= "<br><small><strong>Motivo:</strong> " . ($reasonLabels[$reason] ?? htmlspecialchars($reason)) . "</small>";
                    }
                    if ($notes) {
                        $message .= "<br><small><strong>Observações:</strong> " . htmlspecialchars($notes) . "</small>";
                    }
                    break;
                case 'deliver':
                    $message = "📦 <strong>$displayName</strong> foi marcada como entregue!";
                    if ($notes) {
                        $message .= "<br><small><strong>Observações:</strong> " . htmlspecialchars($notes) . "</small>";
                    }
                    break;
            }

        } catch (Exception $e) {
            $pdo->rollback();
            $message = "❌ Erro ao processar ação: " . $e->getMessage();
        }
    }
}

// Buscar todas as requisições
$stmt = $pdo->query("
    SELECT r.*, u.username, COUNT(ri.id) as item_count 
    FROM requests r 
    JOIN users u ON r.user_id = u.id 
    JOIN request_items ri ON r.id = ri.request_id 
    GROUP BY r.id 
    ORDER BY r.request_date DESC
");
$requests = $stmt->fetchAll();
?>

<!-- Mensagens de feedback -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>
<!-- Lista de Requisições -->
<div class="content-card">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h5 class="mb-0">
            <i class="fas fa-tasks text-primary me-2"></i>
            Todas as Requisições
        </h5>
        <div class="d-flex gap-2">
            <span class="badge bg-secondary"><?php echo count($requests); ?> requisições</span>
            <a href="request_form.php" class="btn btn-primary btn-sm btn-custom">
                <i class="fas fa-plus me-1"></i>Nova Requisição
            </a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th><i class="fas fa-hashtag me-1"></i>Código</th>
                    <th><i class="fas fa-file-alt me-1"></i>Requisição</th>
                    <th><i class="fas fa-user me-1"></i>Usuário</th>
                    <th><i class="fas fa-flag me-1"></i>Status</th>
                    <th><i class="fas fa-list me-1"></i>Itens</th>
                    <th><i class="fas fa-cogs me-1"></i>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($requests as $request): ?>
                <tr>
                    <td>
                        <?php
                        // Incluir biblioteca de códigos de barras
                        require_once 'includes/barcode_generator.php';

                        // Gerar código interno se não existir
                        $internalCode = $request['internal_code'] ?? 'REQ' . str_pad($request['id'], 6, '0', STR_PAD_LEFT);
                        ?>
                        <div class="text-center">
                            <small class="text-muted">ID: <?php echo $request['id']; ?></small><br>
                            <strong class="text-primary font-monospace"><?php echo htmlspecialchars($internalCode); ?></strong><br>
                            <a href="view_barcode.php?type=request&id=<?php echo $request['id']; ?>"
                               class="btn btn-xs btn-outline-dark" title="Ver código de barras">
                                <i class="fas fa-barcode"></i>
                            </a>
                        </div>
                    </td>
                    <td>
                        <div>
                            <strong class="text-primary"><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></strong>
                            <br><small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?></small>
                        </div>
                    </td>
                    <td><?php echo htmlspecialchars($request['username']); ?></td>
                    <td>
                        <?php
                        $statusLabels = [
                            'pending' => '<span class="badge bg-warning text-dark">Pendente</span>',
                            'approved' => '<span class="badge bg-success">Aprovado</span>',
                            'rejected' => '<span class="badge bg-danger">Rejeitado</span>',
                            'delivered' => '<span class="badge bg-info">Entregue</span>'
                        ];
                        echo $statusLabels[$request['status']];
                        ?>
                    </td>
                    <td><?php echo $request['item_count']; ?> itens</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="view_request.php?id=<?php echo $request['id']; ?>"
                               class="btn btn-info btn-sm" title="Ver Detalhes">
                                <i class="fas fa-eye"></i>
                            </a>

                            <?php if ($request['status'] == 'pending'): ?>
                                <a href="edit_request.php?id=<?php echo $request['id']; ?>"
                                   class="btn btn-warning btn-sm" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-success btn-sm" title="Aprovar"
                                        onclick="confirmQuickApproval(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo $request['item_count']; ?>)">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" title="Rejeitar"
                                        onclick="confirmQuickRejection(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo $request['item_count']; ?>)">
                                    <i class="fas fa-times"></i>
                                </button>
                            <?php elseif ($request['status'] == 'approved'): ?>
                                <button type="button" class="btn btn-primary btn-sm" title="Marcar Entregue"
                                        onclick="confirmQuickDelivery(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($request['username'], ENT_QUOTES); ?>', <?php echo $request['item_count']; ?>)">
                                    <i class="fas fa-truck"></i>
                                </button>
                            <?php endif; ?>

                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-secondary btn-sm dropdown-toggle"
                                        data-bs-toggle="dropdown" title="Exportar">
                                    <i class="fas fa-download"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=excel">
                                        <i class="fas fa-file-excel text-success me-2"></i>Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=pdf">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                                    </a></li>
                                    <li><a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=docx">
                                        <i class="fas fa-file-word text-primary me-2"></i>Word
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Modal de Confirmação Rápida -->
<div class="modal fade" id="quickActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" id="modal-header">
                <h5 class="modal-title" id="modal-title"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
                <div class="modal-body">
                    <div id="modal-alert"></div>

                    <h6><strong>Detalhes da Requisição:</strong></h6>
                    <ul class="list-unstyled">
                        <li><strong>Nome:</strong> <span id="modal-request-name"></span></li>
                        <li><strong>Solicitante:</strong> <span id="modal-username"></span></li>
                        <li><strong>Total de Itens:</strong> <span id="modal-item-count"></span></li>
                    </ul>

                    <!-- Campo de motivo para rejeição -->
                    <div id="rejection-fields" style="display: none;">
                        <div class="mb-3">
                            <label for="quick-rejection-reason" class="form-label">
                                <i class="fas fa-comment-alt me-2"></i>
                                Motivo da Rejeição *
                            </label>
                            <select id="quick-rejection-reason" class="form-select" required>
                                <option value="">Selecione um motivo...</option>
                                <option value="itens_indisponiveis">Itens indisponíveis no estoque</option>
                                <option value="orcamento_insuficiente">Orçamento insuficiente</option>
                                <option value="requisicao_duplicada">Requisição duplicada</option>
                                <option value="itens_desnecessarios">Itens considerados desnecessários</option>
                                <option value="fora_prazo">Solicitação fora do prazo</option>
                                <option value="informacoes_incompletas">Informações incompletas</option>
                                <option value="outro">Outro motivo</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="quick-notes" class="form-label">
                            <i class="fas fa-sticky-note me-2"></i>
                            <span id="notes-label">Observações (opcional)</span>
                        </label>
                        <textarea id="quick-notes" class="form-control" rows="2"
                                  placeholder="Observações adicionais..."></textarea>
                    </div>

                    <div id="modal-warning"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn" id="confirm-action-btn" onclick="executeQuickAction()">
                        <span id="confirm-btn-text"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

<?php
// Scripts específicos da página
$page_scripts = '
<style>
.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
}
.font-monospace {
    font-family: "Courier New", monospace;
}
</style>
<script>
let currentAction = null;
let currentRequestId = null;

function confirmQuickApproval(requestId, requestName, username, itemCount) {
    currentAction = "approve";
    currentRequestId = requestId;

    // Configurar modal
    document.getElementById("modal-header").className = "modal-header bg-success text-white";
    document.getElementById("modal-title").innerHTML = "<i class=\"fas fa-check-circle me-2\"></i>Confirmar Aprovação";
    document.getElementById("modal-alert").innerHTML =
        "<div class=\"alert alert-info\">" +
            "<i class=\"fas fa-info-circle me-2\"></i>" +
            "<strong>Atenção:</strong> Esta ação aprovará a requisição para entrega." +
        "</div>";

    // Preencher dados
    document.getElementById("modal-request-name").textContent = requestName;
    document.getElementById("modal-username").textContent = username;
    document.getElementById("modal-item-count").textContent = itemCount + " item(ns)";

    // Configurar campos
    document.getElementById("rejection-fields").style.display = "none";
    document.getElementById("notes-label").textContent = "Observações da aprovação (opcional)";
    document.getElementById("quick-notes").placeholder = "Instruções especiais, observações...";
    document.getElementById("quick-notes").value = "";

    // Configurar botão
    document.getElementById("confirm-action-btn").className = "btn btn-success btn-lg";
    document.getElementById("confirm-btn-text").innerHTML = "<i class=\"fas fa-check-circle me-2\"></i>Confirmar Aprovação";

    // Configurar aviso
    document.getElementById("modal-warning").innerHTML =
        "<div class=\"alert alert-warning\">" +
            "<i class=\"fas fa-exclamation-triangle me-2\"></i>" +
            "<strong>Importante:</strong> Após a aprovação, a requisição ficará disponível para entrega." +
        "</div>";

    new bootstrap.Modal(document.getElementById("quickActionModal")).show();
}

function confirmQuickRejection(requestId, requestName, username, itemCount) {
    currentAction = "reject";
    currentRequestId = requestId;

    // Configurar modal para rejeição
    document.getElementById("modal-header").className = "modal-header bg-danger text-white";
    document.getElementById("modal-title").innerHTML = "Confirmar Rejeição";
    document.getElementById("modal-request-name").textContent = requestName;
    document.getElementById("modal-username").textContent = username;
    document.getElementById("modal-item-count").textContent = itemCount + " item(ns)";

    document.getElementById("rejection-fields").style.display = "block";
    document.getElementById("notes-label").textContent = "Observações adicionais";
    document.getElementById("quick-notes").value = "";
    document.getElementById("quick-rejection-reason").value = "";

    document.getElementById("confirm-action-btn").className = "btn btn-danger btn-lg";
    document.getElementById("confirm-btn-text").innerHTML = "Confirmar Rejeição";

    new bootstrap.Modal(document.getElementById("quickActionModal")).show();
}

function confirmQuickDelivery(requestId, requestName, username, itemCount) {
    currentAction = "deliver";
    currentRequestId = requestId;

    // Configurar modal para entrega
    document.getElementById("modal-header").className = "modal-header bg-primary text-white";
    document.getElementById("modal-title").innerHTML = "Confirmar Entrega";
    document.getElementById("modal-request-name").textContent = requestName;
    document.getElementById("modal-username").textContent = username;
    document.getElementById("modal-item-count").textContent = itemCount + " item(ns)";

    document.getElementById("rejection-fields").style.display = "none";
    document.getElementById("notes-label").textContent = "Observações da entrega (opcional)";
    document.getElementById("quick-notes").value = "";

    document.getElementById("confirm-action-btn").className = "btn btn-primary btn-lg";
    document.getElementById("confirm-btn-text").innerHTML = "Confirmar Entrega";

    new bootstrap.Modal(document.getElementById("quickActionModal")).show();
}

function executeQuickAction() {
    const notes = document.getElementById("quick-notes").value;
    let reason = "";

    // Validar rejeição
    if (currentAction === "reject") {
        reason = document.getElementById("quick-rejection-reason").value;
        if (!reason) {
            alert("Por favor, selecione um motivo para a rejeição.");
            return;
        }
    }

    // Construir URL
    let url = "?action=" + currentAction + "&id=" + currentRequestId;
    if (notes.trim()) {
        url += "&notes=" + encodeURIComponent(notes);
    }
    if (reason) {
        url += "&reason=" + encodeURIComponent(reason);
    }

    // Redirecionar
    window.location.href = url;
}

// Validação em tempo real
document.addEventListener("DOMContentLoaded", function() {
    const reasonSelect = document.getElementById("quick-rejection-reason");
    if (reasonSelect) {
        reasonSelect.addEventListener("change", function() {
            const notesField = document.getElementById("quick-notes");
            if (this.value === "outro") {
                notesField.required = true;
                notesField.placeholder = "Por favor, explique detalhadamente o motivo da rejeição...";
                notesField.focus();
            } else {
                notesField.required = false;
                notesField.placeholder = "Observações adicionais (opcional)...";
            }
        });
    }
});
</script>
';

// Incluir footer
require_once 'includes/layout_footer.php';
?>