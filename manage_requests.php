<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';

$message = '';

// Processar ações
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $id = (int)$_GET['id'];
    
    if (in_array($action, ['approve', 'reject', 'deliver'])) {
        $status = [
            'approve' => 'approved',
            'reject' => 'rejected',
            'deliver' => 'delivered'
        ][$action];
        
        $stmt = $pdo->prepare("UPDATE requests SET status = ? WHERE id = ?");
        if ($stmt->execute([$status, $id])) {
            // Buscar nome da requisição para a mensagem
            $nameStmt = $pdo->prepare("SELECT title FROM requests WHERE id = ?");
            $nameStmt->execute([$id]);
            $requestTitle = $nameStmt->fetchColumn();
            $displayName = $requestTitle ? htmlspecialchars($requestTitle) : "Requisição #$id";
            $message = "$displayName atualizada para $status com sucesso!";
        }
    }
}

// Buscar todas as requisições
$stmt = $pdo->query("
    SELECT r.*, u.username, COUNT(ri.id) as item_count 
    FROM requests r 
    JOIN users u ON r.user_id = u.id 
    JOIN request_items ri ON r.id = ri.request_id 
    GROUP BY r.id 
    ORDER BY r.request_date DESC
");
$requests = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Gerenciar Requisições - Sistema de Requisição de Material de Cozinha</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <h2>Gerenciar Requisições</h2>
        
        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <table class="table">
            <thead>
                <tr>
                    <th>Requisição</th>
                    <th>Usuário</th>
                    <th>Status</th>
                    <th>Itens</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($requests as $request): ?>
                <tr>
                    <td>
                        <div>
                            <strong class="text-primary"><?php echo htmlspecialchars($request['title'] ?? 'Requisição #' . $request['id']); ?></strong>
                            <br><small class="text-muted">ID: #<?php echo $request['id']; ?> • <?php echo date('d/m/Y H:i', strtotime($request['request_date'])); ?></small>
                        </div>
                    </td>
                    <td><?php echo htmlspecialchars($request['username']); ?></td>
                    <td>
                        <?php 
                        $statusLabels = [
                            'pending' => '<span class="badge badge-warning">Pendente</span>',
                            'approved' => '<span class="badge badge-success">Aprovado</span>',
                            'rejected' => '<span class="badge badge-danger">Rejeitado</span>',
                            'delivered' => '<span class="badge badge-info">Entregue</span>'
                        ];
                        echo $statusLabels[$request['status']];
                        ?>
                    </td>
                    <td><?php echo $request['item_count']; ?> itens</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="view_request.php?id=<?php echo $request['id']; ?>" class="btn btn-info" title="Ver Detalhes">👁️</a>

                            <?php if ($request['status'] == 'pending'): ?>
                                <a href="edit_request.php?id=<?php echo $request['id']; ?>" class="btn btn-warning" title="Editar">✏️</a>
                                <a href="?action=approve&id=<?php echo $request['id']; ?>" class="btn btn-success" title="Aprovar">✅</a>
                                <a href="?action=reject&id=<?php echo $request['id']; ?>" class="btn btn-danger" title="Rejeitar">❌</a>
                            <?php elseif ($request['status'] == 'approved'): ?>
                                <a href="?action=deliver&id=<?php echo $request['id']; ?>" class="btn btn-primary" title="Marcar Entregue">📦</a>
                            <?php endif; ?>

                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" title="Exportar">📄</button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=excel">📊 Excel</a>
                                    <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=pdf">📄 PDF</a>
                                    <a class="dropdown-item" href="export_request.php?id=<?php echo $request['id']; ?>&format=docx">📝 Word</a>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>