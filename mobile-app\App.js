import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Provider as PaperProvider } from 'react-native-paper';
import Toast from 'react-native-toast-message';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Importar telas
import LoginScreen from './src/screens/LoginScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import RequestsScreen from './src/screens/RequestsScreen';
import NewRequestScreen from './src/screens/NewRequestScreen';
import RequestDetailsScreen from './src/screens/RequestDetailsScreen';
import ItemsScreen from './src/screens/ItemsScreen';
import ProfileScreen from './src/screens/ProfileScreen';

// Importar contexto de autenticação
import { AuthProvider, useAuth } from './src/context/AuthContext';

// Importar tema
import { theme } from './src/theme/theme';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Navegação principal com tabs
function MainTabs() {
  const { user } = useAuth();
  
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Dashboard') {
            iconName = 'dashboard';
          } else if (route.name === 'Requisições') {
            iconName = 'assignment';
          } else if (route.name === 'Nova') {
            iconName = 'add-circle';
          } else if (route.name === 'Itens') {
            iconName = 'inventory';
          } else if (route.name === 'Perfil') {
            iconName = 'person';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: 'gray',
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ title: 'Início' }}
      />
      <Tab.Screen 
        name="Requisições" 
        component={RequestsScreen}
        options={{ title: 'Minhas Requisições' }}
      />
      <Tab.Screen 
        name="Nova" 
        component={NewRequestScreen}
        options={{ title: 'Nova Requisição' }}
      />
      <Tab.Screen 
        name="Itens" 
        component={ItemsScreen}
        options={{ title: 'Produtos' }}
      />
      <Tab.Screen 
        name="Perfil" 
        component={ProfileScreen}
        options={{ title: 'Meu Perfil' }}
      />
    </Tab.Navigator>
  );
}

// Navegação principal
function AppNavigator() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return null; // Ou uma tela de loading
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <>
            <Stack.Screen name="Main" component={MainTabs} />
            <Stack.Screen 
              name="RequestDetails" 
              component={RequestDetailsScreen}
              options={{ 
                headerShown: true,
                title: 'Detalhes da Requisição',
                headerStyle: {
                  backgroundColor: theme.colors.primary,
                },
                headerTintColor: '#fff',
              }}
            />
          </>
        ) : (
          <Stack.Screen name="Login" component={LoginScreen} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

// Componente principal
export default function App() {
  return (
    <PaperProvider theme={theme}>
      <AuthProvider>
        <AppNavigator />
        <Toast />
      </AuthProvider>
    </PaperProvider>
  );
}
