import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from '../services/apiService';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth deve ser usado dentro de AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Verificar se há token salvo ao iniciar o app
  useEffect(() => {
    checkStoredAuth();
  }, []);

  const checkStoredAuth = async () => {
    try {
      const storedToken = await AsyncStorage.getItem('auth_token');
      const storedUser = await AsyncStorage.getItem('user_data');

      if (storedToken && storedUser) {
        const userData = JSON.parse(storedUser);
        
        // Verificar se token ainda é válido
        const isValid = await apiService.validateToken(storedToken);
        
        if (isValid) {
          setToken(storedToken);
          setUser(userData);
          apiService.setAuthToken(storedToken);
        } else {
          // Token expirado, limpar dados
          await clearStoredAuth();
        }
      }
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
      await clearStoredAuth();
    } finally {
      setIsLoading(false);
    }
  };

  const clearStoredAuth = async () => {
    await AsyncStorage.multiRemove(['auth_token', 'user_data']);
    setToken(null);
    setUser(null);
    apiService.clearAuthToken();
  };

  const login = async (username, password) => {
    try {
      setIsLoading(true);
      
      const response = await apiService.login(username, password);
      
      if (response.success) {
        const { token: authToken, user: userData } = response.data;
        
        // Salvar dados no AsyncStorage
        await AsyncStorage.setItem('auth_token', authToken);
        await AsyncStorage.setItem('user_data', JSON.stringify(userData));
        
        // Atualizar estado
        setToken(authToken);
        setUser(userData);
        
        // Configurar token no serviço de API
        apiService.setAuthToken(authToken);
        
        return { success: true };
      } else {
        return { success: false, message: response.message || 'Erro no login' };
      }
    } catch (error) {
      console.error('Erro no login:', error);
      return { 
        success: false, 
        message: error.response?.data?.error || 'Erro de conexão' 
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      // Chamar API de logout
      await apiService.logout();
      
      // Limpar dados locais
      await clearStoredAuth();
      
    } catch (error) {
      console.error('Erro no logout:', error);
      // Mesmo com erro, limpar dados locais
      await clearStoredAuth();
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = async (userData) => {
    try {
      // Atualizar dados do usuário
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      
      // Salvar no AsyncStorage
      await AsyncStorage.setItem('user_data', JSON.stringify(updatedUser));
      
      return { success: true };
    } catch (error) {
      console.error('Erro ao atualizar usuário:', error);
      return { success: false, message: 'Erro ao atualizar dados' };
    }
  };

  const refreshAuth = async () => {
    try {
      if (!token) return false;
      
      const isValid = await apiService.validateToken(token);
      
      if (!isValid) {
        await clearStoredAuth();
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Erro ao validar token:', error);
      await clearStoredAuth();
      return false;
    }
  };

  const isAuthenticated = !!token && !!user;
  const isAdmin = user?.role === 'admin';

  const value = {
    // Estado
    user,
    token,
    isLoading,
    isAuthenticated,
    isAdmin,
    
    // Métodos
    login,
    logout,
    updateUser,
    refreshAuth,
    checkStoredAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
