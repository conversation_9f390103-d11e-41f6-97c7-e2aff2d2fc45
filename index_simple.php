<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'config/db_connect.php';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Requisição - Teste Simples</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="alert alert-success">
            <h1>✅ Sistema Funcionando!</h1>
            <p>Bem-vindo, <?php echo htmlspecialchars($_SESSION['username'] ?? 'Usuário'); ?>!</p>
            <p>Esta é uma versão simplificada sem SEO para teste.</p>
            
            <div class="mt-3">
                <a href="request_form.php" class="btn btn-primary">Nova Requisição</a>
                <a href="my_requests.php" class="btn btn-info">Minhas Requisições</a>
                <a href="logout.php" class="btn btn-secondary">Sair</a>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>🔧 Diagnóstico</h3>
            </div>
            <div class="card-body">
                <p><strong>Usuário:</strong> <?php echo htmlspecialchars($_SESSION['username']); ?></p>
                <p><strong>Role:</strong> <?php echo htmlspecialchars($_SESSION['role']); ?></p>
                <p><strong>Servidor:</strong> <?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
                <p><strong>PHP:</strong> <?php echo phpversion(); ?></p>
                
                <hr>
                <a href="debug.php" class="btn btn-warning">Ver Debug Completo</a>
                <a href="index.php" class="btn btn-success">Testar Versão com SEO</a>
            </div>
        </div>
    </div>
</body>
</html>
