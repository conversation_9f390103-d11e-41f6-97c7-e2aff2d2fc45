<?php
require_once 'config/db_connect.php';

echo "<h2>Populando o banco de dados com dados de exemplo...</h2>";

try {
    // Criar usuário admin se não existir
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
        $stmt->execute(['admin', $hashedPassword, 'admin']);
        echo "<p>✓ Usuário admin criado (username: admin, senha: admin123)</p>";
    } else {
        echo "<p>✓ Usuário admin já existe</p>";
    }
    
    // Criar usuário funcionário se não existir
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['funcionario']);
    $staff = $stmt->fetch();
    
    if (!$staff) {
        $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
        $stmt->execute(['funcionario', $hashedPassword, 'staff']);
        echo "<p>✓ Usuário funcionário criado (username: funcionario, senha: 123456)</p>";
    } else {
        echo "<p>✓ Usuário funcionário já existe</p>";
    }
    
    // Verificar se já existem itens
    $stmt = $pdo->query("SELECT COUNT(*) FROM items");
    $itemCount = $stmt->fetchColumn();
    
    if ($itemCount == 0) {
        // Inserir itens de cozinha mais utilizados organizados por categoria
        $items = [
            // GRÃOS E CEREAIS
            ['Arroz Branco', 'Arroz branco tipo 1 agulhinha', 50, 'kg'],
            ['Arroz Integral', 'Arroz integral tipo 1', 20, 'kg'],
            ['Feijão Carioca', 'Feijão carioca tipo 1', 30, 'kg'],
            ['Feijão Preto', 'Feijão preto tipo 1', 25, 'kg'],
            ['Lentilha', 'Lentilha vermelha', 10, 'kg'],
            ['Grão de Bico', 'Grão de bico seco', 8, 'kg'],
            ['Quinoa', 'Quinoa em grãos', 5, 'kg'],
            ['Aveia', 'Aveia em flocos', 10, 'kg'],

            // FARINHAS E MASSAS
            ['Farinha de Trigo', 'Farinha de trigo especial', 25, 'kg'],
            ['Farinha de Mandioca', 'Farinha de mandioca torrada', 15, 'kg'],
            ['Fubá', 'Fubá de milho', 10, 'kg'],
            ['Polvilho Doce', 'Polvilho doce para tapioca', 5, 'kg'],
            ['Macarrão Espaguete', 'Macarrão espaguete 500g', 50, 'pacotes'],
            ['Macarrão Penne', 'Macarrão penne 500g', 30, 'pacotes'],
            ['Macarrão Parafuso', 'Macarrão parafuso 500g', 25, 'pacotes'],
            ['Lasanha', 'Massa para lasanha', 20, 'pacotes'],

            // ÓLEOS E GORDURAS
            ['Óleo de Soja', 'Óleo de soja refinado', 30, 'litros'],
            ['Azeite de Oliva', 'Azeite de oliva extra virgem', 10, 'litros'],
            ['Óleo de Girassol', 'Óleo de girassol', 15, 'litros'],
            ['Margarina', 'Margarina com sal', 12, 'kg'],
            ['Manteiga', 'Manteiga sem sal', 8, 'kg'],

            // TEMPEROS E CONDIMENTOS
            ['Sal Refinado', 'Sal refinado iodado', 15, 'kg'],
            ['Açúcar Cristal', 'Açúcar cristal', 30, 'kg'],
            ['Açúcar Mascavo', 'Açúcar mascavo orgânico', 10, 'kg'],
            ['Vinagre Branco', 'Vinagre de álcool', 10, 'litros'],
            ['Vinagre de Maçã', 'Vinagre de maçã', 5, 'litros'],
            ['Molho de Soja', 'Molho de soja tradicional', 20, 'frascos'],
            ['Azeite de Dendê', 'Azeite de dendê', 5, 'litros'],

            // VEGETAIS E LEGUMES
            ['Cebola', 'Cebola amarela', 25, 'kg'],
            ['Alho', 'Alho descascado', 8, 'kg'],
            ['Batata', 'Batata inglesa', 40, 'kg'],
            ['Batata Doce', 'Batata doce roxa', 20, 'kg'],
            ['Cenoura', 'Cenoura in natura', 18, 'kg'],
            ['Tomate', 'Tomate maduro', 30, 'kg'],
            ['Pimentão', 'Pimentão colorido', 15, 'kg'],
            ['Abobrinha', 'Abobrinha italiana', 12, 'kg'],
            ['Berinjela', 'Berinjela roxa', 10, 'kg'],
            ['Brócolis', 'Brócolis fresco', 8, 'kg'],
            ['Couve-flor', 'Couve-flor branca', 8, 'kg'],
            ['Repolho', 'Repolho verde', 15, 'kg'],
            ['Alface', 'Alface crespa', 50, 'unidades'],
            ['Rúcula', 'Rúcula fresca', 20, 'maços'],
            ['Espinafre', 'Espinafre fresco', 15, 'maços'],

            // FRUTAS
            ['Banana', 'Banana prata', 30, 'kg'],
            ['Maçã', 'Maçã gala', 20, 'kg'],
            ['Laranja', 'Laranja pera', 25, 'kg'],
            ['Limão', 'Limão tahiti', 10, 'kg'],
            ['Abacaxi', 'Abacaxi pérola', 15, 'unidades'],
            ['Mamão', 'Mamão papaya', 20, 'kg'],
            ['Manga', 'Manga tommy', 15, 'kg'],
            ['Uva', 'Uva itália', 10, 'kg'],

            // CARNES E PROTEÍNAS
            ['Frango Inteiro', 'Frango inteiro congelado', 30, 'kg'],
            ['Peito de Frango', 'Peito de frango sem osso', 25, 'kg'],
            ['Coxa de Frango', 'Coxa de frango com sobrecoxa', 20, 'kg'],
            ['Carne Bovina', 'Carne bovina de segunda', 35, 'kg'],
            ['Carne Suína', 'Carne suína lombo', 20, 'kg'],
            ['Peixe', 'Filé de peixe congelado', 15, 'kg'],
            ['Linguiça', 'Linguiça calabresa', 10, 'kg'],
            ['Bacon', 'Bacon defumado', 8, 'kg'],
            ['Presunto', 'Presunto cozido', 6, 'kg'],

            // LATICÍNIOS E OVOS
            ['Leite Integral', 'Leite integral UHT', 60, 'litros'],
            ['Leite Desnatado', 'Leite desnatado UHT', 30, 'litros'],
            ['Iogurte Natural', 'Iogurte natural', 40, 'unidades'],
            ['Queijo Mussarela', 'Queijo mussarela fatiado', 12, 'kg'],
            ['Queijo Prato', 'Queijo prato fatiado', 8, 'kg'],
            ['Requeijão', 'Requeijão cremoso', 20, 'potes'],
            ['Ovos', 'Ovos de galinha brancos', 30, 'dúzias'],
            ['Creme de Leite', 'Creme de leite fresco', 25, 'caixas'],

            // PÃES E PADARIA
            ['Pão Francês', 'Pão francês tradicional', 150, 'unidades'],
            ['Pão de Forma', 'Pão de forma integral', 20, 'pacotes'],
            ['Pão de Açúcar', 'Pão de açúcar', 50, 'unidades'],
            ['Biscoito Cream Cracker', 'Biscoito cream cracker', 30, 'pacotes'],
            ['Torrada', 'Torrada integral', 15, 'pacotes'],

            // MOLHOS E CONSERVAS
            ['Molho de Tomate', 'Molho de tomate tradicional', 40, 'latas'],
            ['Extrato de Tomate', 'Extrato de tomate', 25, 'latas'],
            ['Maionese', 'Maionese tradicional', 15, 'potes'],
            ['Ketchup', 'Ketchup tradicional', 20, 'frascos'],
            ['Mostarda', 'Mostarda amarela', 10, 'frascos'],
            ['Azeitona Verde', 'Azeitona verde sem caroço', 15, 'vidros'],
            ['Azeitona Preta', 'Azeitona preta sem caroço', 12, 'vidros'],
            ['Milho em Conserva', 'Milho em conserva', 30, 'latas'],
            ['Ervilha em Conserva', 'Ervilha em conserva', 25, 'latas'],

            // ESPECIARIAS E ERVAS
            ['Orégano', 'Orégano desidratado', 10, 'potes'],
            ['Manjericão', 'Manjericão desidratado', 8, 'potes'],
            ['Salsa', 'Salsa desidratada', 8, 'potes'],
            ['Cebolinha', 'Cebolinha desidratada', 8, 'potes'],
            ['Pimenta do Reino', 'Pimenta do reino moída', 5, 'potes'],
            ['Cominho', 'Cominho em pó', 5, 'potes'],
            ['Páprica', 'Páprica doce', 5, 'potes'],
            ['Canela em Pó', 'Canela em pó', 3, 'potes'],
            ['Cravo', 'Cravo da índia', 2, 'potes'],
            ['Noz Moscada', 'Noz moscada ralada', 2, 'potes'],

            // BEBIDAS
            ['Café em Pó', 'Café em pó torrado', 20, 'kg'],
            ['Chá Preto', 'Chá preto em saquinhos', 10, 'caixas'],
            ['Chá de Camomila', 'Chá de camomila', 5, 'caixas'],
            ['Suco de Laranja', 'Suco de laranja integral', 30, 'litros'],
            ['Água Mineral', 'Água mineral sem gás', 100, 'litros'],

            // PRODUTOS DE LIMPEZA PARA COZINHA
            ['Detergente', 'Detergente neutro', 20, 'frascos'],
            ['Esponja de Aço', 'Esponja de aço', 50, 'unidades'],
            ['Pano de Prato', 'Pano de prato', 30, 'unidades'],
            ['Álcool 70%', 'Álcool 70% para limpeza', 15, 'litros']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO items (name, description, quantity, unit) VALUES (?, ?, ?, ?)");
        
        foreach ($items as $item) {
            $stmt->execute($item);
        }
        
        echo "<p>✓ " . count($items) . " itens de exemplo inseridos no estoque</p>";
    } else {
        echo "<p>✓ Itens já existem no banco ($itemCount itens)</p>";
    }
    
    echo "<h3>Banco de dados configurado com sucesso!</h3>";
    echo "<p><strong>Credenciais de acesso:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> username = admin, senha = admin123</li>";
    echo "<li><strong>Funcionário:</strong> username = funcionario, senha = 123456</li>";
    echo "</ul>";
    echo "<p><a href='login.php' class='btn btn-primary'>Ir para Login</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Configuração do Banco de Dados</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-4">
        <!-- Conteúdo já foi exibido acima -->
    </div>
</body>
</html>
